import 'package:flutter_audio_room/shared/data/remote/request_queue.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('RequestQueue', () {
    late RequestQueue requestQueue;

    setUp(() {
      requestQueue = RequestQueue(
        maxQueueSize: 10,
      );
    });

    test('should execute requests in order', () async {
      final results = <int>[];
      final futures = <Future>[];

      // Add multiple requests
      for (int i = 0; i < 5; i++) {
        final future = requestQueue.addRequest<int>(
          () async {
            await Future.delayed(const Duration(milliseconds: 100));
            results.add(i);
            return i;
          },
          tag: 'test-$i',
        );
        futures.add(future);
      }

      // Wait for all requests to complete
      await Future.wait(futures);

      // Results should be in order (0, 1, 2, 3, 4)
      expect(results, [0, 1, 2, 3, 4]);
    });

    test('should respect concurrency limits', () async {
      int activeCount = 0;
      int maxActiveCount = 0;
      final futures = <Future>[];

      // Add multiple requests that track active count
      for (int i = 0; i < 5; i++) {
        final future = requestQueue.addRequest<int>(
          () async {
            activeCount++;
            maxActiveCount =
                maxActiveCount > activeCount ? maxActiveCount : activeCount;
            await Future.delayed(const Duration(milliseconds: 100));
            activeCount--;
            return i;
          },
          tag: 'concurrent-test-$i',
        );
        futures.add(future);
      }

      await Future.wait(futures);

      // Should never exceed maxConcurrency (2)
      expect(maxActiveCount, lessThanOrEqualTo(2));
    });

    test('should pause and resume correctly', () async {
      final results = <int>[];
      final futures = <Future>[];

      // Pause the queue first
      requestQueue.pause();

      // Add requests while paused
      for (int i = 0; i < 3; i++) {
        final future = requestQueue.addRequest<int>(
          () async {
            await Future.delayed(const Duration(milliseconds: 50));
            results.add(i);
            return i;
          },
          tag: 'pause-test-$i',
        );
        futures.add(future);
      }

      // Wait a bit to ensure no requests are processed while paused
      await Future.delayed(const Duration(milliseconds: 200));
      expect(results, isEmpty);

      // Resume the queue
      requestQueue.resume();

      // Wait for all requests to complete
      await Future.wait(futures);

      // All results should be present
      expect(results, [0, 1, 2]);
    });

    test('should handle queue cancellation', () async {
      final futures = <Future>[];
      final errors = <Object>[];

      // Pause the queue first to ensure requests stay in queue
      requestQueue.pause();

      // Add requests while paused
      for (int i = 0; i < 3; i++) {
        final future = requestQueue.addRequest<int>(
          () async {
            await Future.delayed(const Duration(milliseconds: 100));
            return i;
          },
          tag: 'cancel-test-$i',
        ).catchError((error) {
          errors.add(error);
          return -1;
        });
        futures.add(future);
      }

      // Cancel the queue while requests are still queued
      requestQueue.cancel();

      await Future.wait(futures);

      // All requests should have been cancelled
      expect(errors.length, 3);
      for (final error in errors) {
        expect(error.toString(), contains('Request cancelled'));
      }
    });

    test('should remove requests by tag', () async {
      final results = <int>[];
      final futures = <Future>[];

      // Pause the queue first to ensure requests stay in queue
      requestQueue.pause();

      // Add requests with different tags while paused
      for (int i = 0; i < 5; i++) {
        final tag = i < 3 ? 'remove-me' : 'keep-me';
        final future = requestQueue.addRequest<int>(
          () async {
            await Future.delayed(const Duration(milliseconds: 50));
            results.add(i);
            return i;
          },
          tag: tag,
        ).catchError((error) {
          // Removed requests will error
          return -1;
        });
        futures.add(future);
      }

      // Remove requests with 'remove-me' tag while they're still queued
      requestQueue.removeByTag('remove-me');

      // Resume the queue
      requestQueue.resume();

      await Future.wait(futures);

      // Only requests 3 and 4 should have completed successfully
      expect(results, [3, 4]);
    });
  });

  // Note: TokenRefreshManager functionality has been merged into TokenRefreshService
  // This test is kept for reference but the actual functionality is now tested
  // through integration tests with TokenRefreshService
}
