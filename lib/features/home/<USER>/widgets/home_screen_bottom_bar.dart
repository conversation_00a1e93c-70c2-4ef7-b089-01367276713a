import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/theme/text_theme.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/conversation_provider.dart';
import 'package:flutter_audio_room/features/home/<USER>/providers/home_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'home_screen_pages_builder.dart';

class HomeScreenBottomBar extends ConsumerWidget {
  final int currentIndex;
  final List<HomePagesOption> pages;

  const HomeScreenBottomBar({
    super.key,
    required this.currentIndex,
    required this.pages,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    List<BottomNavigationBarItem> items = [];

    for (var i = 0; i < pages.length; i++) {
      final e = pages[i];
      items.add(
        BottomNavigationBarItem(
          icon: Builder(builder: (_) {
            if (e.homeTabName == HomeTabName.chat) {
              final conversationState = ref.watch(conversationProvider);
              return conversationState.when(data: (conversations) {
                final unReadCount = conversations
                    .where((element) => element.unreadCount > 0)
                    .fold(
                        0,
                        (previousValue, element) =>
                            previousValue + element.unreadCount);
                return Badge.count(
                  isLabelVisible: unReadCount > 0,
                  count: unReadCount,
                  child: e.icon.svg(
                    // ignore: deprecated_member_use_from_same_package
                    color:
                        currentIndex == i ? context.colorScheme.primary : null,
                  ),
                );
              }, error: (error, stackTrace) {
                LogUtils.e(error.toString(),
                    error: stackTrace, tag: 'HomeScreen');
                return e.icon.svg(
                  // ignore: deprecated_member_use_from_same_package
                  color: currentIndex == i ? context.colorScheme.primary : null,
                );
              }, loading: () {
                return e.icon.svg(
                  // ignore: deprecated_member_use_from_same_package
                  color: currentIndex == i ? context.colorScheme.primary : null,
                );
              });
            }

            return e.icon.svg(
              // ignore: deprecated_member_use_from_same_package
              color: currentIndex == i ? context.colorScheme.primary : null,
            );
          }),
          label: e.homeTabName.name,
        ),
      );
    }

    return SafeArea(
      minimum: EdgeInsets.only(bottom: 20.h),
      child: Container(
        height: 66.h,
        margin: EdgeInsets.symmetric(horizontal: 10.w),
        decoration: BoxDecoration(
          color: const Color(0xff232323),
          borderRadius: BorderRadius.circular(999.r),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(999.r),
          child: Theme(
            data: ThemeData(
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
            ),
            child: BottomNavigationBar(
              currentIndex: currentIndex,
              onTap: (index) {
                ref.read(homeProvider.notifier).setTab(index);
              },
              selectedItemColor: context.colorScheme.primary,
              unselectedItemColor: CupertinoColors.systemGrey,
              backgroundColor: const Color(0xff232323),
              type: BottomNavigationBarType.fixed,
              showSelectedLabels: true,
              selectedFontSize: 10.sp,
              unselectedFontSize: 10.sp,
              selectedLabelStyle: context.textTheme.bodyMediumSemiBold.copyWith(
                color: context.colorScheme.primary,
                fontSize: 10.sp,
              ),
              unselectedLabelStyle:
                  context.textTheme.bodyMediumSemiBold.copyWith(
                color: CupertinoColors.systemGrey,
                fontSize: 10.sp,
              ),
              showUnselectedLabels: true,
              items: items,
            ),
          ),
        ),
      ),
    );
  }
}
