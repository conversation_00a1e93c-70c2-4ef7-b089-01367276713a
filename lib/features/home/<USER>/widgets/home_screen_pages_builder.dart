import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/theme/text_theme.dart';
import 'package:flutter_audio_room/core/widgets/app_button.dart';
import 'package:flutter_audio_room/features/app_settings/screen/app_settings_screen.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/screens/audio_room_home_screen.dart';
import 'package:flutter_audio_room/features/chat/presentation/controllers/chat_socket_provider.dart';
import 'package:flutter_audio_room/features/chat/presentation/pages/conversation_list_page.dart';
import 'package:flutter_audio_room/features/leaderboard/screen/leaderboard_screen.dart';
import 'package:flutter_audio_room/features/my/presentation/screens/my_home_screen.dart';
import 'package:flutter_audio_room/features/purchase/presentation/pages/shop_page.dart';
import 'package:flutter_audio_room/gen/assets.gen.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../providers/home_provider.dart';

enum HomeTabName {
  home,
  shop,
  leaderboard,
  chat,
  my,
}

class HomePagesOption {
  final Widget page;
  final SvgGenImage icon;
  final HomeTabName homeTabName;
  AppBar? appBar;

  HomePagesOption({
    required this.page,
    required this.icon,
    required this.homeTabName,
    this.appBar,
  });
}

class HomeScreenPagesBuilder {
  static List<HomePagesOption> buildPages(BuildContext context, WidgetRef ref) {
    return [
      HomePagesOption(
        page: const AudioRoomHomeScreen(),
        icon: Assets.svgs.tabHome,
        homeTabName: HomeTabName.home,
      ),
      HomePagesOption(
        page: const ShopPage(),
        icon: Assets.svgs.tabShop,
        homeTabName: HomeTabName.shop,
      ),
      HomePagesOption(
        page: const LeaderboardScreen(),
        icon: Assets.svgs.tabLeaderboard,
        homeTabName: HomeTabName.leaderboard,
      ),
      HomePagesOption(
        page: const ConversationListPage(),
        icon: Assets.svgs.tabFollowing,
        homeTabName: HomeTabName.chat,
        appBar: AppBar(
          automaticallyImplyLeading: false,
          title: Builder(builder: (builder) {
            final socketIoState = ref.watch(chatSocketProvider);
            if (socketIoState != SocketConnectionState.connected) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('Connecting...'),
                  5.horizontalSpace,
                  SizedBox(
                    width: 16.w,
                    height: 16.w,
                    child: CircularProgressIndicator(
                      color: context.colorScheme.primary,
                    ),
                  ),
                ],
              );
            }
            return const Text('Chat');
          }),
        ),
      ),
      HomePagesOption(
        page: const MyHomeScreen(),
        icon: Assets.svgs.tabMe,
        homeTabName: HomeTabName.my,
        appBar: AppBar(
          centerTitle: false,
          automaticallyImplyLeading: false,
          title: Row(
            children: [
              12.horizontalSpace,
              AppButton(
                type: AppButtonType.contained,
                padding: EdgeInsets.zero,
                onPressed: () {
                  ref.read(homeProvider.notifier).setTab(1);
                  ref.read(shopTabProvider.notifier).state =
                      ShopTabType.premium;
                },
                child: Container(
                  height: 24.h,
                  padding: EdgeInsets.only(
                    right: 10.w,
                    left: 3.w,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(999.r),
                    border: Border.all(
                      color: context.colorScheme.outline,
                    ),
                    gradient: const LinearGradient(
                      colors: [
                        Color(0xff262626),
                        Color(0xff201F1F),
                      ],
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Assets.images.proCrown.image(
                        width: 30.w,
                        height: 30.w,
                      ),
                      Text(
                        'Premium',
                        style: context.textTheme.bodySmallMedium.copyWith(
                          fontSize: 10.sp,
                          color: const Color(0xffF7C906),
                        ),
                      ),
                    ],
                  ),
                ),
                textStyle: (context, style) => style.copyWith(
                  color: context.colorScheme.primary,
                ),
              ),
            ],
          ),
          actions: [
            IconButton(
              onPressed: () {
                context.push(
                  const WidgetPageConfig(
                    page: AppSettingsScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.settings),
            ),
          ],
        ),
      ),
    ];
  }
}