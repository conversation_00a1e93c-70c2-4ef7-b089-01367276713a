import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/audio_room/domain/interfaces/i_mini_player_service.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_provider.dart';
import 'package:flutter_audio_room/features/instant_chat/domain/entities/call_status.dart';
import 'package:flutter_audio_room/features/instant_chat/presentation/provider/instant_call_provider.dart';
import 'package:flutter_audio_room/features/voice_call/base/provider/voice_call_state.dart';
import 'package:flutter_audio_room/features/voice_call/friend/provider/friend_voice_call_provider.dart';
import 'package:flutter_audio_room/features/voice_call/friend/screen/friend_voice_call_screen.dart';
import 'package:flutter_audio_room/services/network_info_service/network_info_service.dart';
import 'package:flutter_audio_room/services/websocket_service/data/websocket_datasource_manager.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/home_provider.dart';

// 定义一个回调类型用于处理离开房间的逻辑
typedef LeaveRoomCallback = Future<void> Function();

class HomeScreenListeners {
  static void setupListeners(BuildContext context, WidgetRef ref, {LeaveRoomCallback? onLeaveRoom}) {
    ref.listen(homeProvider.select((state) => state.messages),
        (previous, next) {
      if (next.isNotEmpty) {
        final message = next.last;
        LoadingUtils.showToast(message.data);
      }
    });

    ref.listen(
      networkConnectionStateProvider,
      (previous, next) {
        if (previous?.value == null) {
          return;
        } else if (previous?.value != next.value) {
          next.whenData(
            (isConnected) {
              final wasConnected = previous?.value?.getRight() ?? false;
              final isNowConnected = isConnected.getRight() ?? false;

              // 当网络从断开恢复到连接时，重置WebSocket重连状态
              if (!wasConnected && isNowConnected) {
                LogUtils.i(
                  'Network restored, resetting WebSocket reconnection states',
                  tag: 'HomeScreen.networkListener',
                );
                // 使用原子操作重置WebSocket重连状态并检查连接，避免竞态条件
                getIt<WebSocketDataSourceManager>()
                    .resetAndCheckAllConnections();
              }
            },
          );
        }
      },
    );

    ref.listen(friendVoiceCallProvider, (previous, next) {
      if (previous?.callId != next.callId && next.callId != null) {
        if (next.peerId == null) {
          return;
        }

        // We need to access the mixin method from HomeScreenInitializer
        // This is a limitation of static methods, so we'll handle the call logic here
        _handleCallIn(context, ref, next, onLeaveRoom);
      } else if (previous?.callStatus is! CallDisconnected &&
          next.callStatus is CallDisconnected) {
        getIt<IMiniPlayerService>().removeMiniPlayer();
      }
    }, onError: (error, stackTrace) {
      LogUtils.e(error.toString(), error: stackTrace, tag: 'HomeScreen');
    });

    ref.listen(instantCallProvider, (previous, next) {
      if (previous?.callStatus is! CallDisconnected &&
          next.callStatus is CallDisconnected) {
        getIt<IMiniPlayerService>().removeMiniPlayer();
      }
    });
  }

  static Future<void> _handleCallIn(
      BuildContext context, WidgetRef ref, VoiceCallState call, LeaveRoomCallback? onLeaveRoom) async {
    final voiceCallNotifier = ref.read(friendVoiceCallProvider.notifier);
    final instantCallNotifier = ref.read(instantCallProvider.notifier);

    final inAudioRoom = ref
        .read(audioRoomProvider.select((state) => state.currentRoom != null));
    final isInInstantCall =
        ref.read(instantCallProvider.select((state) => state.isCallActive));
    final isInFriendCall =
        ref.read(friendVoiceCallProvider.select((state) => state.isCallActive));
    if (inAudioRoom) {
      final res = await context.showOkCancelAlertDialog(
        title: 'Incoming Call',
        content:
            'Do you want to answer the call? It will quit audio room and join call.',
      );
      if (res == false) {
        voiceCallNotifier.rejectCall();
      } else {
        // 使用传入的回调处理离开房间的逻辑
        if (onLeaveRoom != null) {
          await onLeaveRoom();
        }
        if (!context.mounted) return;
        context.popUntilFirst();
      }
    } else if (isInInstantCall) {
      final res = await context.showOkCancelAlertDialog(
        title: 'Incoming Call',
        content:
            'Do you want to answer the call? It will quit the current call and join the new one.',
      );

      if (res == false) {
        voiceCallNotifier.rejectCall();
      } else {
        instantCallNotifier.endCall();
      }
    } else if (isInFriendCall) {
      final res = await context.showOkCancelAlertDialog(
        title: 'Incoming Call',
        content:
            'Do you want to answer the call? It will quit the current call and join the new one.',
      );

      if (res == false) {
        voiceCallNotifier.rejectCall();
      } else {
        voiceCallNotifier.endCall();
      }
    }

    if (!context.mounted) return;
    context.push(
      RoutePageConfig(
        route: FriendVoiceCallScreen.route(
          friendId: call.peerId!,
          friendName: call.peerNickName,
          friendAvatar: '',
          fromMiniPlayer: false,
        ),
      ),
    );
  }
}