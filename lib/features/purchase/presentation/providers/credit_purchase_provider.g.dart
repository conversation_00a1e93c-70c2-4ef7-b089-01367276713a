// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'credit_purchase_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$creditPurchaseHash() => r'4eb291b46db30d05347cbfdf74536ccc0ecd6409';

/// See also [CreditPurchase].
@ProviderFor(CreditPurchase)
final creditPurchaseProvider = AutoDisposeAsyncNotifierProvider<CreditPurchase,
    List<PurchaseItem>>.internal(
  CreditPurchase.new,
  name: r'creditPurchaseProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$creditPurchaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CreditPurchase = AutoDisposeAsyncNotifier<List<PurchaseItem>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
