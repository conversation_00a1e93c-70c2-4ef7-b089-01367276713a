// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_purchase_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$subscriptionPurchaseHash() =>
    r'f51b0a57aa2ae37194ef91965abb317b5ee75cb4';

/// See also [SubscriptionPurchase].
@ProviderFor(SubscriptionPurchase)
final subscriptionPurchaseProvider = AutoDisposeAsyncNotifierProvider<
    SubscriptionPurchase, List<PurchaseItem>>.internal(
  SubscriptionPurchase.new,
  name: r'subscriptionPurchaseProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$subscriptionPurchaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SubscriptionPurchase = AutoDisposeAsyncNotifier<List<PurchaseItem>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
