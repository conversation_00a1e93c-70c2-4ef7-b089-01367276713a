// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProductImpl _$$ProductImplFromJson(Map<String, dynamic> json) =>
    _$ProductImpl(
      productId: json['productId'] as String?,
      subscriptionId: json['subscriptionId'] as String?,
      subType: (json['subType'] as num?)?.toInt(),
      picUrl: json['picUrl'] as String?,
      description: json['description'] as String? ?? '',
      currencyCode: json['currencyCode'] as String? ?? 'USD',
      currencySymbol: json['currencySymbol'] as String? ?? '\$',
      price: (json['price'] as num?)?.toDouble() ?? 0,
      discountPrice: (json['discountPrice'] as num?)?.toDouble() ?? 0,
      discountRate: (json['discountRate'] as num?)?.toDouble() ?? 0,
      gems: (json['gems'] as num?)?.toInt() ?? 0,
      productOrder: (json['productOrder'] as num?)?.toInt() ?? 0,
      productStatus: (json['productStatus'] as num?)?.toInt() ?? 0,
      isPopular: (json['isPopular'] as num?)?.toInt() ?? 0,
      bestValue: (json['bestValue'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$ProductImplToJson(_$ProductImpl instance) =>
    <String, dynamic>{
      'productId': instance.productId,
      'subscriptionId': instance.subscriptionId,
      'subType': instance.subType,
      'picUrl': instance.picUrl,
      'description': instance.description,
      'currencyCode': instance.currencyCode,
      'currencySymbol': instance.currencySymbol,
      'price': instance.price,
      'discountPrice': instance.discountPrice,
      'discountRate': instance.discountRate,
      'gems': instance.gems,
      'productOrder': instance.productOrder,
      'productStatus': instance.productStatus,
      'isPopular': instance.isPopular,
      'bestValue': instance.bestValue,
    };
