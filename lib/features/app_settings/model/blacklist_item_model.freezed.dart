// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'blacklist_item_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BlacklistItemModel _$BlacklistItemModelFromJson(Map<String, dynamic> json) {
  return _BlacklistItemModel.fromJson(json);
}

/// @nodoc
mixin _$BlacklistItemModel {
  ProfileModel? get profile => throw _privateConstructorUsedError;
  DateTime? get createTime => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BlacklistItemModelCopyWith<BlacklistItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BlacklistItemModelCopyWith<$Res> {
  factory $BlacklistItemModelCopyWith(
          BlacklistItemModel value, $Res Function(BlacklistItemModel) then) =
      _$BlacklistItemModelCopyWithImpl<$Res, BlacklistItemModel>;
  @useResult
  $Res call({ProfileModel? profile, DateTime? createTime});

  $ProfileModelCopyWith<$Res>? get profile;
}

/// @nodoc
class _$BlacklistItemModelCopyWithImpl<$Res, $Val extends BlacklistItemModel>
    implements $BlacklistItemModelCopyWith<$Res> {
  _$BlacklistItemModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profile = freezed,
    Object? createTime = freezed,
  }) {
    return _then(_value.copyWith(
      profile: freezed == profile
          ? _value.profile
          : profile // ignore: cast_nullable_to_non_nullable
              as ProfileModel?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ProfileModelCopyWith<$Res>? get profile {
    if (_value.profile == null) {
      return null;
    }

    return $ProfileModelCopyWith<$Res>(_value.profile!, (value) {
      return _then(_value.copyWith(profile: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BlacklistItemModelImplCopyWith<$Res>
    implements $BlacklistItemModelCopyWith<$Res> {
  factory _$$BlacklistItemModelImplCopyWith(_$BlacklistItemModelImpl value,
          $Res Function(_$BlacklistItemModelImpl) then) =
      __$$BlacklistItemModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({ProfileModel? profile, DateTime? createTime});

  @override
  $ProfileModelCopyWith<$Res>? get profile;
}

/// @nodoc
class __$$BlacklistItemModelImplCopyWithImpl<$Res>
    extends _$BlacklistItemModelCopyWithImpl<$Res, _$BlacklistItemModelImpl>
    implements _$$BlacklistItemModelImplCopyWith<$Res> {
  __$$BlacklistItemModelImplCopyWithImpl(_$BlacklistItemModelImpl _value,
      $Res Function(_$BlacklistItemModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profile = freezed,
    Object? createTime = freezed,
  }) {
    return _then(_$BlacklistItemModelImpl(
      profile: freezed == profile
          ? _value.profile
          : profile // ignore: cast_nullable_to_non_nullable
              as ProfileModel?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$BlacklistItemModelImpl implements _BlacklistItemModel {
  const _$BlacklistItemModelImpl({this.profile, this.createTime});

  factory _$BlacklistItemModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$BlacklistItemModelImplFromJson(json);

  @override
  final ProfileModel? profile;
  @override
  final DateTime? createTime;

  @override
  String toString() {
    return 'BlacklistItemModel(profile: $profile, createTime: $createTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BlacklistItemModelImpl &&
            (identical(other.profile, profile) || other.profile == profile) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, profile, createTime);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$BlacklistItemModelImplCopyWith<_$BlacklistItemModelImpl> get copyWith =>
      __$$BlacklistItemModelImplCopyWithImpl<_$BlacklistItemModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BlacklistItemModelImplToJson(
      this,
    );
  }
}

abstract class _BlacklistItemModel implements BlacklistItemModel {
  const factory _BlacklistItemModel(
      {final ProfileModel? profile,
      final DateTime? createTime}) = _$BlacklistItemModelImpl;

  factory _BlacklistItemModel.fromJson(Map<String, dynamic> json) =
      _$BlacklistItemModelImpl.fromJson;

  @override
  ProfileModel? get profile;
  @override
  DateTime? get createTime;
  @override
  @JsonKey(ignore: true)
  _$$BlacklistItemModelImplCopyWith<_$BlacklistItemModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
