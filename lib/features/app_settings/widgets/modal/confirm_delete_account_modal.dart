import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/theme/text_theme.dart';
import 'package:flutter_audio_room/core/widgets/app_button.dart';
import 'package:flutter_audio_room/core/widgets/dialog_wrapper.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ConfirmDeleteAccountModal extends StatelessWidget {
  const ConfirmDeleteAccountModal({super.key});

  @override
  Widget build(BuildContext context) {
    return DialogWrapper(
      backgroundColor: Colors.transparent,
      blurSigma: 4,
      borderRadius: BorderRadius.circular(16.r),
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
          side: BorderSide(
            color: context.colorScheme.outline,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: 20.w,
            vertical: 16.h,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              30.verticalSpace,
              Text(
                'Your data will be deleted after 14 days. Log in before then to cancel the deletion.',
                style: context.textTheme.bodyLargeMedium.copyWith(
                  fontSize: 20.sp,
                  height: 0,
                  letterSpacing: -0.17,
                ),
                textAlign: TextAlign.center,
              ),
              30.verticalSpace,
              AppButton(
                text: 'Cancel Deletion',
                borderRadius: 999,
                width: 220.w,
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
              10.verticalSpace,
              AppButton(
                text: 'Confirm Deletion',
                type: AppButtonType.outline,
                borderRadius: 999,
                width: 220.w,
                textStyle: (context, defaultStyle) => defaultStyle.copyWith(
                  color: context.colorScheme.primary,
                ),
                onPressed: () {
                  Navigator.of(context).pop(true);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
