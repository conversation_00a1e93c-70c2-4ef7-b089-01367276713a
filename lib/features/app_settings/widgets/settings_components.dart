import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/theme/text_theme.dart';
import 'package:flutter_audio_room/services/package_info/i_package_info_service.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Settings option model
class SettingsOption {
  final String title;
  final IconData? icon;
  final VoidCallback onPressed;
  final Color? iconColor;
  final Widget? trailing;
  final bool showDivider;

  const SettingsOption({
    required this.title,
    this.icon,
    required this.onPressed,
    this.iconColor,
    this.trailing,
    this.showDivider = true,
  });
}

/// Settings section model
class SettingsSection {
  final String? title;
  final List<SettingsOption> options;
  final EdgeInsetsGeometry? padding;

  const SettingsSection({
    this.title,
    required this.options,
    this.padding,
  });
}

/// Settings section widget
class SettingsSectionWidget extends StatelessWidget {
  final SettingsSection section;
  final int sectionIndex;

  const SettingsSectionWidget({
    super.key,
    required this.section,
    required this.sectionIndex,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // iOS-style card color
    final cardColor = isDark
        ? const Color(0xFF1C1C1E) // iOS dark mode grouped background
        : const Color(0xFFFFFFFF); // iOS light mode white

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (section.title != null)
          Padding(
            padding: EdgeInsets.only(left: 10.w, bottom: 6.h),
            child: Text(
              section.title!,
              style: context.textTheme.bodyLargeSemiBold.copyWith(
                color: context.colorScheme.onSurface,
              ),
            ),
          ),
        Container(
          decoration: BoxDecoration(
            color: cardColor,
            border: Border.symmetric(
              horizontal: BorderSide(
                color: context.theme.dividerColor,
              ),
            ),
          ),
          child: Column(
            children: section.options.asMap().entries.map((entry) {
              final itemIndex = entry.key;
              final option = entry.value;
              return SettingsOptionWidget(
                option: option,
                isLast: itemIndex == section.options.length - 1,
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}

/// Settings option item widget
class SettingsOptionWidget extends StatelessWidget {
  final SettingsOption option;
  final bool isLast;

  const SettingsOptionWidget({
    super.key,
    required this.option,
    required this.isLast,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      children: [
        Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: option.onPressed,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: 16.w,
                vertical: 11.h,
              ),
              child: Row(
                children: [
                  if (option.icon != null) ...[
                    Icon(
                      option.icon,
                      size: 22.sp,
                      color: option.iconColor ??
                          (isDark
                              ? const Color(0xFF007AFF)
                              : const Color(0xFF007AFF)), // iOS blue
                    ),
                    SizedBox(width: 12.w),
                  ],
                  Expanded(
                    child: Text(
                      option.title,
                      style: context.textTheme.bodySmallSemiBold,
                    ),
                  ),
                  if (option.trailing != null) ...[
                    option.trailing!,
                  ] else ...[
                    Icon(
                      CupertinoIcons.chevron_right,
                      size: 20.sp,
                      color: context.colorScheme.primary,
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
        if (!isLast && option.showDivider)
          Container(
            margin: EdgeInsets.only(left: option.icon != null ? 50.w : 16.w),
            height: 0.5,
            color: context.theme.dividerColor,
          ),
      ],
    );
  }
}

/// Settings list widget
class SettingsListWidget extends StatelessWidget {
  final List<SettingsSection> sections;

  const SettingsListWidget({
    super.key,
    required this.sections,
  });

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(child: 20.verticalSpace),
        SliverList.separated(
          itemCount: sections.length,
          itemBuilder: (context, sectionIndex) {
            return SettingsSectionWidget(
              section: sections[sectionIndex],
              sectionIndex: sectionIndex,
            );
          },
          separatorBuilder: (context, index) {
            return SizedBox(height: 35.h);
          },
        ),
        SliverToBoxAdapter(
          child: _buildVersionInfo(context),
        ),
      ],
    );
  }

  Widget _buildVersionInfo(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 35.h),
        child: Text(
          'V ${getIt<IPackageInfoService>().version}',
          style: context.textTheme.bodySmallSemiBold,
        ),
      ),
    );
  }
}
