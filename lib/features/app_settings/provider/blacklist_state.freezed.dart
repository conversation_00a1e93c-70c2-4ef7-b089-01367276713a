// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'blacklist_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BlacklistState {
  List<BlacklistItemModel> get blacklist => throw _privateConstructorUsedError;
  bool get hasMore => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $BlacklistStateCopyWith<BlacklistState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BlacklistStateCopyWith<$Res> {
  factory $BlacklistStateCopyWith(
          BlacklistState value, $Res Function(BlacklistState) then) =
      _$BlacklistStateCopyWithImpl<$Res, BlacklistState>;
  @useResult
  $Res call({List<BlacklistItemModel> blacklist, bool hasMore, bool isLoading});
}

/// @nodoc
class _$BlacklistStateCopyWithImpl<$Res, $Val extends BlacklistState>
    implements $BlacklistStateCopyWith<$Res> {
  _$BlacklistStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? blacklist = null,
    Object? hasMore = null,
    Object? isLoading = null,
  }) {
    return _then(_value.copyWith(
      blacklist: null == blacklist
          ? _value.blacklist
          : blacklist // ignore: cast_nullable_to_non_nullable
              as List<BlacklistItemModel>,
      hasMore: null == hasMore
          ? _value.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BlacklistStateImplCopyWith<$Res>
    implements $BlacklistStateCopyWith<$Res> {
  factory _$$BlacklistStateImplCopyWith(_$BlacklistStateImpl value,
          $Res Function(_$BlacklistStateImpl) then) =
      __$$BlacklistStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<BlacklistItemModel> blacklist, bool hasMore, bool isLoading});
}

/// @nodoc
class __$$BlacklistStateImplCopyWithImpl<$Res>
    extends _$BlacklistStateCopyWithImpl<$Res, _$BlacklistStateImpl>
    implements _$$BlacklistStateImplCopyWith<$Res> {
  __$$BlacklistStateImplCopyWithImpl(
      _$BlacklistStateImpl _value, $Res Function(_$BlacklistStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? blacklist = null,
    Object? hasMore = null,
    Object? isLoading = null,
  }) {
    return _then(_$BlacklistStateImpl(
      blacklist: null == blacklist
          ? _value._blacklist
          : blacklist // ignore: cast_nullable_to_non_nullable
              as List<BlacklistItemModel>,
      hasMore: null == hasMore
          ? _value.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$BlacklistStateImpl implements _BlacklistState {
  const _$BlacklistStateImpl(
      {final List<BlacklistItemModel> blacklist = const [],
      this.hasMore = false,
      this.isLoading = false})
      : _blacklist = blacklist;

  final List<BlacklistItemModel> _blacklist;
  @override
  @JsonKey()
  List<BlacklistItemModel> get blacklist {
    if (_blacklist is EqualUnmodifiableListView) return _blacklist;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_blacklist);
  }

  @override
  @JsonKey()
  final bool hasMore;
  @override
  @JsonKey()
  final bool isLoading;

  @override
  String toString() {
    return 'BlacklistState(blacklist: $blacklist, hasMore: $hasMore, isLoading: $isLoading)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BlacklistStateImpl &&
            const DeepCollectionEquality()
                .equals(other._blacklist, _blacklist) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_blacklist), hasMore, isLoading);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$BlacklistStateImplCopyWith<_$BlacklistStateImpl> get copyWith =>
      __$$BlacklistStateImplCopyWithImpl<_$BlacklistStateImpl>(
          this, _$identity);
}

abstract class _BlacklistState implements BlacklistState {
  const factory _BlacklistState(
      {final List<BlacklistItemModel> blacklist,
      final bool hasMore,
      final bool isLoading}) = _$BlacklistStateImpl;

  @override
  List<BlacklistItemModel> get blacklist;
  @override
  bool get hasMore;
  @override
  bool get isLoading;
  @override
  @JsonKey(ignore: true)
  _$$BlacklistStateImplCopyWith<_$BlacklistStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
