// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'blacklist_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$blacklistHash() => r'8867af91bfc2c1a1fd865fe07bc363176eecdf51';

/// See also [Blacklist].
@ProviderFor(Blacklist)
final blacklistProvider = NotifierProvider<Blacklist, BlacklistState>.internal(
  Blacklist.new,
  name: r'blacklistProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$blacklistHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Blacklist = Notifier<BlacklistState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
