// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_online_status_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserOnlineStatusModelImpl _$$UserOnlineStatusModelImplFromJson(
        Map<String, dynamic> json) =>
    _$UserOnlineStatusModelImpl(
      userId: json['userId'] as String?,
      status: (json['status'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$UserOnlineStatusModelImplToJson(
        _$UserOnlineStatusModelImpl instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'status': instance.status,
    };

_$UserOnlineStatusResponseModelImpl
    _$$UserOnlineStatusResponseModelImplFromJson(Map<String, dynamic> json) =>
        _$UserOnlineStatusResponseModelImpl(
          users: (json['users'] as List<dynamic>?)
              ?.map((e) =>
                  UserOnlineStatusModel.fromJson(e as Map<String, dynamic>))
              .toList(),
          success: json['success'] as bool?,
          message: json['message'] as String?,
        );

Map<String, dynamic> _$$UserOnlineStatusResponseModelImplToJson(
        _$UserOnlineStatusResponseModelImpl instance) =>
    <String, dynamic>{
      'users': instance.users?.map((e) => e.toJson()).toList(),
      'success': instance.success,
      'message': instance.message,
    };
