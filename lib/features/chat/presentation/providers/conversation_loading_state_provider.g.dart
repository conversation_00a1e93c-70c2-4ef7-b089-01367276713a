// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversation_loading_state_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$conversationLoadingHash() =>
    r'8c38a2833c0ff1bebe47387f6a4cd358d0cdf04c';

/// See also [ConversationLoading].
@ProviderFor(ConversationLoading)
final conversationLoadingProvider = AutoDisposeNotifierProvider<
    ConversationLoading, ConversationLoadingState>.internal(
  ConversationLoading.new,
  name: r'conversationLoadingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$conversationLoadingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConversationLoading = AutoDisposeNotifier<ConversationLoadingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
