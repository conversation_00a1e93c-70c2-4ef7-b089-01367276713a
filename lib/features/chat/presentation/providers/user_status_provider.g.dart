// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_status_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userOnlineStatusCacheHash() =>
    r'38def66b3ca9cf7c326ceee89208ccda3ac12428';

/// User online status cache provider
/// Manages cache of user online status information
///
/// Copied from [UserOnlineStatusCache].
@ProviderFor(UserOnlineStatusCache)
final userOnlineStatusCacheProvider = NotifierProvider<UserOnlineStatusCache,
    Map<String, UserOnlineStatusModel>>.internal(
  UserOnlineStatusCache.new,
  name: r'userOnlineStatusCacheProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userOnlineStatusCacheHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UserOnlineStatusCache = Notifier<Map<String, UserOnlineStatusModel>>;
String _$userRoomStatusCacheHash() =>
    r'c222fb1dfa03330483906094629e9027f7887de5';

/// User room status cache provider
/// Manages cache of user room status information
///
/// Copied from [UserRoomStatusCache].
@ProviderFor(UserRoomStatusCache)
final userRoomStatusCacheProvider = NotifierProvider<UserRoomStatusCache,
    Map<String, UserRoomStatusModel>>.internal(
  UserRoomStatusCache.new,
  name: r'userRoomStatusCacheProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userRoomStatusCacheHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UserRoomStatusCache = Notifier<Map<String, UserRoomStatusModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
