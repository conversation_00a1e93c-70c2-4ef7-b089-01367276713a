import 'dart:async';

import 'package:flutter_audio_room/core/utils/analytics_utils.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/chat/domain/extension/message_metadata_extension.dart';
import 'package:flutter_audio_room/features/chat/domain/models/message_config.dart';
import 'package:flutter_audio_room/features/chat/domain/models/message_extend_model.dart';
import 'package:flutter_audio_room/features/chat/domain/models/message_subtype.dart';
import 'package:flutter_audio_room/features/chat/domain/models/remote_message_model.dart';
import 'package:flutter_audio_room/features/chat/presentation/controllers/chat_socket_provider.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/base/chat_provider_base.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/conversation_provider.dart';
import 'package:flutter_audio_room/services/signal_protocol_service/repository/signal_repository_provider.dart';
import 'package:flutter_audio_room/services/signal_protocol_service/signal_message_provider.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:flutter_chat_types/flutter_chat_types.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path/path.dart';

mixin SendMessageMixin on ChatProviderBase {

  /// 统一消息发送入口
  /// [config] 消息配置
  /// [type] 消息类型
  Future<VoidResult> sendMessage(MessageConfig config) async {
    final conversationNotifier = ref.read(conversationProvider.notifier);
    final conversationResult =
        await conversationNotifier.getOrCreateConversation(conversationId);
    if (conversationResult.isLeft()) {
      LoadingUtils.showToast('Send message failed, please try again later');
      return const Left(AppException(
        message: 'Send message failed, please try again later',
        statusCode: 500,
        identifier: 'send_message_failed',
      ));
    }

    final conversation = conversationResult.getRight()!;
    final dIdChanged = conversation.extend.dIdChanged;

    LogUtils.d('peer device changed: $dIdChanged', tag: 'SendMessageMixin');

    if (dIdChanged) {
      await conversationNotifier.clearNewDeviceTag(conversationId);
    }

    // 检查session有效性
    final hasSession =
        await ref.read(signalMessageProvider.notifier).hasValidSession(
              ref.read(accountProvider).userInfo?.profile?.id ?? '',
              conversationId,
            );

    if (!hasSession || dIdChanged) {
      final res = await initializeSignalSession(conversationId);
      if (res.isLeft()) {
        return Left(res.getLeft()!);
      }
    }

    switch (config) {
      case TextMessageConfig():
        return _sendTextMessage(config);
      case VoiceCallMessageConfig():
      case InviteRoomMessageConfig():
      case VoiceCallEndMessageConfig():
        return _sendCustomMessage(config);
    }
  }

  /// Initialize signal protocol session for a user
  ///
  /// Returns a [VoidResult] indicating success or failure
  Future<VoidResult> initializeSignalSession(String remoteUserId) async {
    try {
      final userId = ref.read(accountProvider).userInfo?.profile?.id ?? '';
      final signalMessage = ref.read(signalMessageProvider.notifier);

      // 获取对方的用户信息以获取他们的 Bundle
      final userBundle = await ref
          .read(signalRepositoryProvider.notifier)
          .getKeyBundle(userId: remoteUserId);

      if (userBundle.isLeft()) {
        return Left(userBundle.getLeft()!);
      }

      final keyBundle = userBundle.getRight()!;

      LogUtils.d('get peer user keyBundle: $keyBundle',
          tag: 'sendMessageMixin');

      // 使用对方的 Bundle 建立会话
      await signalMessage.recreateSession(
        userId,
        remoteUserId,
        keyBundle,
      );
      return const Right(null);
    } catch (e) {
      LogUtils.e('Failed to initialize signal session: ${e.toString()}',
          tag: 'sendMessageMixin');
      return Left(SignalEncryptionFailure(
          'Failed to initialize signal session: ${e.toString()}'));
    }
  }
  
  /// 创建消息基础对象
  Future<types.Message> _createMessageBase({
    required String userId,
    required types.MessageType type,
    String? content,
    String? filePath,
    int? size,
    int? duration,
    String? thumbnailPath,
    bool isEphemeral = true, // 默认启用阅后即焚
  }) async {
    final now = DateTime.now().millisecondsSinceEpoch;
    // 使用时间戳 + 微秒 + 随机数确保ID唯一性
    final id =
        '${now}_${DateTime.now().microsecondsSinceEpoch % 1000000}_${(DateTime.now().microsecondsSinceEpoch % 10000).toString().padLeft(4, '0')}';
    final createdAt = now;
    final author = types.User(id: userId);

    final ephemeralTimeout = await ref
        .read(conversationProvider.notifier)
        .getEphemeralTimeout(conversationId);

    // 构建基础消息元数据，包括阅后即焚属性和fileId
    final metadata = MessageExtendModel(
      ephemeral: isEphemeral,
      msgCTtlS: ephemeralTimeout,
      thumbnailUrl: thumbnailPath,
      duration: duration,
      fileSize: size,
    );

    // 为不同类型的消息添加阅后即焚属性
    types.Message message;

    switch (type) {
      case types.MessageType.text:
        message = types.TextMessage(
          author: author,
          id: id,
          text: content ?? '',
          status: types.Status.sending,
          createdAt: createdAt,
          metadata: metadata.toJson(),
        );
        break;
      case types.MessageType.image:
        message = ImageMessage(
          author: author,
          id: id,
          uri: filePath!,
          status: types.Status.sending,
          size: size!,
          name: basename(filePath),
          createdAt: createdAt,
          metadata: metadata.toJson(),
        );
        break;
      case types.MessageType.audio:
        message = AudioMessage(
          author: author,
          id: id,
          uri: filePath!,
          status: types.Status.sending,
          duration: Duration(seconds: duration ?? 0),
          name: basename(filePath),
          size: size!,
          createdAt: createdAt,
          metadata: metadata.toJson(),
        );
        break;
      case types.MessageType.video:
        message = VideoMessage(
          author: author,
          id: id,
          uri: filePath!,
          status: types.Status.sending,
          name: basename(filePath),
          size: size!,
          metadata: metadata.toJson(),
          createdAt: createdAt,
        );
        break;
      default:
        throw Exception('Unsupported message type');
    }

    ref
        .read(conversationProvider.notifier)
        .updateLastMessage(conversationId, message);

    return message;
  }

  Future<types.Message> _createCustomMessage({
    required MessageConfig config,
    bool isEphemeral = true,
  }) async {
    final now = DateTime.now().millisecondsSinceEpoch;
    // 使用时间戳 + 微秒 + 随机数确保ID唯一性
    final id =
        '${now}_${DateTime.now().microsecondsSinceEpoch % 1000000}_${(DateTime.now().microsecondsSinceEpoch % 10000).toString().padLeft(4, '0')}';
    final createdAt = now;
    final author = types.User(id: config.userId);

    final ephemeralTimeout = await ref
        .read(conversationProvider.notifier)
        .getEphemeralTimeout(conversationId);

    var metadata = MessageExtendModel(
      ephemeral: isEphemeral,
      msgCTtlS: ephemeralTimeout,
    );

    switch (config) {
      case InviteRoomMessageConfig():
        metadata = metadata.copyWith(
          roomInfo: config.roomInfo,
          subtype: MessageSubtype.INVITE_ROOM,
        );
        break;
      case VoiceCallEndMessageConfig():
        metadata = metadata.copyWith(
          callId: config.callId,
          callerId: config.callerId,
          calleeId: config.calleeId,
          callDuration: config.duration,
          callEndType: config.callEndType,
          subtype: MessageSubtype.VOICE_CALL,
        );
        break;
      default:
        throw Exception('Unsupported message type');
    }

    final message = types.CustomMessage(
      author: author,
      id: id,
      status: types.Status.sending,
      createdAt: createdAt,
      metadata: metadata.toJson(),
    );

    ref
        .read(conversationProvider.notifier)
        .updateLastMessage(conversationId, message);

    return message;
  }

  // 根据消息类型获取字符串表示
  String _getMessageTypeString(types.MessageType type) {
    switch (type) {
      case types.MessageType.text:
        return 'text';
      case types.MessageType.file:
        return 'file';
      case types.MessageType.custom:
        return 'custom';
      default:
        return 'unknown';
    }
  }

  // 上传后更新消息状态
  Future<Message> _updateMessageAfterUpload(
    Message updatedMessage,
    types.Status status,
  ) async {
    final messageList = await future;

    final newMessage = updatedMessage.copyWith(status: status);

    final updatedMessages = messageList.map((msg) {
      if (msg.id == newMessage.id) {
        return newMessage;
      }
      return msg;
    }).toList();

    state = AsyncData(updatedMessages);
    await messageLocalRepository.updateMessage(newMessage);

    return newMessage;
  }

  Future<VoidResult> _sendToRemote(
    Message createdMessage,
    Message encryptedMessage,
  ) async {
    final messageToSend = RemoteMessageModel.fromLocalMessage(
      encryptedMessage,
      conversationId,
    ).toJson();
    LogUtils.d('message to send: ${messageToSend.toString()}',
        tag: 'SendMessageMixin');

    final messageType = _getMessageTypeString(createdMessage.type);

    final completion = Completer<VoidResult>();

    await ref.read(chatSocketProvider.notifier).emitWithAck(
        ChatSocketEvents.messageSend,
        messageToSend, (data) {
      final messageId = data?['data']?['id'] ?? '';
      if (data?['success'] == true) {
        _updateSendingMessageStatus(
          messageId,
          localMessage: createdMessage,
          status: types.Status.sent,
        );

        completion.complete(const Right(null));
      } else {
        _updateSendingMessageStatus(
          messageId,
          localMessage: createdMessage,
          status: types.Status.error,
        );

        // Track message send failure
        final errorMessage = data?['msg'] ?? 'Unknown error';
        final errorCode = data?['code']?.toString();
        AnalyticsUtils.trackMessageFailed(
          conversationId: conversationId,
          messageType: messageType,
          failureReason: errorMessage,
          errorCode: errorCode,
          context: 'private_chat',
        );

        completion.complete(Left(AppException(
          message: errorMessage,
          statusCode: 500,
          identifier: errorCode ?? 'UNKNOWN_ERROR',
        )));
      }
    });

    return completion.future;
  }

  Future<void> _updateSendingMessageStatus(
    String? id, {
    required Message localMessage,
    required types.Status status,
  }) async {
    final messageList = await future;
    final newMessage = localMessage.copyWith(status: status, remoteId: id);
    final updatedMessages = messageList.map((msg) {
      if (msg.id == localMessage.id) {
        return newMessage;
      }
      return msg;
    }).toList();

    state = AsyncData(updatedMessages);
    await messageLocalRepository.updateMessage(
      newMessage,
    );
  }

  /// 处理消息发送结果
  Future<VoidResult> _handleMessageResult(
    Message message,
    Future<VoidResult> Function() sendFn,
  ) async {
    try {
      final result = await sendFn();
      return result.fold(
        (failure) {
          return Left(failure);
        },
        (_) {
          return const Right(null);
        },
      );
    } catch (e) {
      LogUtils.e('Error sending message: $e', tag: 'ChatProvider');
      return Left(AppException(
        message: e.toString(),
        statusCode: 500,
        identifier: 'send_message_error',
      ));
    }
  }

  Future<VoidResult> _sendTextMessage(TextMessageConfig config) async {
    final messageList = await future;

    // 确保content不为空
    if (config.content.isEmpty) {
      return const Left(AppException(
        message: '消息内容不能为空',
        statusCode: 400,
        identifier: 'empty_message_content',
      ));
    }

    final message = await _createMessageBase(
      userId: config.userId,
      type: config.type,
      content: config.content,
    ) as types.TextMessage;

    state = AsyncData([message, ...messageList]);

    // 先存储到本地
    await messageLocalRepository.createMessage(message);

    // 同步更新最后一条消息
    await ref
        .read(conversationProvider.notifier)
        .updateLastMessage(conversationId, message);

    final result = await _handleMessageResult(message, () async {
      final signalMessage = ref.read(signalMessageProvider.notifier);
      // 由于上面已经检查过content不为空，这里可以安全使用!
      final (type, content) = await signalMessage.encryptMessage(
        config.userId,
        conversationId,
        config.content,
      );

      final encryptedMessage = message.copyWith(
        text: content,
        metadata: message.extend
            .copyWith(
              isPreKeyMessage: type == 3,
            )
            .toJson(),
      );
      return _sendToRemote(message, encryptedMessage);
    });

    // Track message sent for private chat
    if (result.isRight()) {
      AnalyticsUtils.trackMessageSent(
        conversationId: conversationId,
        messageType: 'text',
        messageLength: config.content.length,
        context: 'private_chat',
      );
    }

    return result;
  }

  Future<VoidResult> _sendCustomMessage(MessageConfig config) async {
    final messageList = await future;
    final message = await _createCustomMessage(
      config: config,
      isEphemeral: true,
    );

    state = AsyncData([message, ...messageList]);

    await messageLocalRepository.createMessage(message);

    await ref
        .read(conversationProvider.notifier)
        .updateLastMessage(conversationId, message);

    final result = await _handleMessageResult(message, () async {
      return _sendToRemote(message, message);
    });

    // Track custom message sent for private chat
    if (result.isRight()) {
      String messageType = 'custom';
      switch (config) {
        case VoiceCallMessageConfig():
          messageType = 'voice_call_request';
          break;
        case VoiceCallEndMessageConfig():
          messageType = 'voice_call_end';
          break;
        case InviteRoomMessageConfig():
          messageType = 'room_invite';
          break;
        default:
          messageType = 'custom';
      }

      AnalyticsUtils.trackMessageSent(
        conversationId: conversationId,
        messageType: messageType,
        context: 'private_chat',
      );
    }

    return result;
  }

  Future<VoidResult> retryMessage(Message message) async {
    LogUtils.d('重试发送消息: ${message.id}', tag: 'SendMessageMixin');

    // 获取消息类型用于tracking
    final messageType = _getMessageTypeString(message.type);

    try {
      // 检查session有效性
      final result = await _ensureValidSession();
      if (result.isLeft()) {
        // Track retry failure
        AnalyticsUtils.trackMessageRetry(
          conversationId: conversationId,
          messageType: messageType,
          retryReason: 'session_invalid',
          context: 'private_chat',
          success: false,
        );
        return result;
      }

      // 先将消息状态更新为sending
      final updatedMessage = await _updateMessageStatus(
        message,
        types.Status.sending,
      );

      // 根据消息类型处理重试
      VoidResult retryResult;
      switch (message.type) {
        case types.MessageType.text:
          retryResult =
              await _retryTextMessage(updatedMessage as types.TextMessage);
          break;
        default:
          retryResult = const Left(AppException(
            message: '不支持的消息类型重试',
            statusCode: 400,
            identifier: 'unsupported_retry_message_type',
          ));
      }

      // Track retry result
      AnalyticsUtils.trackMessageRetry(
        conversationId: conversationId,
        messageType: messageType,
        retryReason: 'user_initiated',
        context: 'private_chat',
        success: retryResult.isRight(),
      );

      return retryResult;
    } catch (e) {
      LogUtils.e('重试消息失败: $e', tag: 'SendMessageMixin');

      // Track retry failure
      AnalyticsUtils.trackMessageRetry(
        conversationId: conversationId,
        messageType: messageType,
        retryReason: 'exception',
        context: 'private_chat',
        success: false,
      );

      return Left(AppException(
        message: '重试消息失败: ${e.toString()}',
        statusCode: 500,
        identifier: 'retry_message_error',
      ));
    }
  }

  /// 确保Signal会话有效
  Future<VoidResult> _ensureValidSession() async {
    final userId =
        ref.read(accountProvider).userInfo?.profile?.id ?? '';
    final hasSession =
        await ref.read(signalMessageProvider.notifier).hasValidSession(
              userId,
              conversationId,
            );

    if (!hasSession) {
      return initializeSignalSession(conversationId);
    }

    return const Right(null);
  }

  /// 更新消息状态的封装方法
  Future<Message> _updateMessageStatus(
    Message message,
    types.Status status,
  ) async {
    return _updateMessageAfterUpload(message, status);
  }

  /// 重试发送文本消息
  Future<VoidResult> _retryTextMessage(types.TextMessage message) async {
    return _handleMessageResult(message, () async {
      final signalMessage = ref.read(signalMessageProvider.notifier);
      final userId =
          ref.read(accountProvider).userInfo?.profile?.id ?? '';

      // 加密消息内容
      final (type, content) = await signalMessage.encryptMessage(
        userId,
        conversationId,
        message.text,
      );

      // 创建加密后的消息对象
      final encryptedMessage = message.copyWith(
        text: content,
        metadata: message.extend
            .copyWith(
              isPreKeyMessage: type == 3,
            )
            .toJson(),
      );

      // 发送到远程
      return _sendToRemote(message, encryptedMessage);
    });
  }
}
