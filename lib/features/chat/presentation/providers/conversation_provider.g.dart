// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversation_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$conversationHash() => r'a104fb961255f18caad54b79607e6fa691ac4634';

/// See also [Conversation].
@ProviderFor(Conversation)
final conversationProvider = AutoDisposeAsyncNotifierProvider<Conversation,
    List<ConversationModel>>.internal(
  Conversation.new,
  name: r'conversationProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$conversationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Conversation = AutoDisposeAsyncNotifier<List<ConversationModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
