// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'remote_message_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RemoteMessageModelImpl _$$RemoteMessageModelImplFromJson(
        Map<String, dynamic> json) =>
    _$RemoteMessageModelImpl(
      id: json['id'] as String?,
      conversationId: json['conversationId'] as String?,
      contentType:
          $enumDecode(_$RemoteMessageContentTypeEnumMap, json['contentType']),
      msgType: $enumDecode(_$RemoteMessageTypeEnumMap, json['msgType']),
      content: json['content'] as String?,
      extend: json['extend'] == null
          ? null
          : MessageExtendModel.fromJson(json['extend'] as Map<String, dynamic>),
      senderId: json['senderId'] as String?,
      createTimeStamp: (json['createTimeStamp'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$RemoteMessageModelImplToJson(
        _$RemoteMessageModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'conversationId': instance.conversationId,
      'contentType': _$RemoteMessageContentTypeEnumMap[instance.contentType]!,
      'msgType': _$RemoteMessageTypeEnumMap[instance.msgType]!,
      'content': instance.content,
      'extend': instance.extend?.toJson(),
      'senderId': instance.senderId,
      'createTimeStamp': instance.createTimeStamp,
    };

const _$RemoteMessageContentTypeEnumMap = {
  RemoteMessageContentType.TXT: 1,
  RemoteMessageContentType.IMAGE: 2,
  RemoteMessageContentType.VOICE: 3,
  RemoteMessageContentType.VIDEO: 4,
  RemoteMessageContentType.FILE: 5,
  RemoteMessageContentType.SYSTEM: 6,
  RemoteMessageContentType.DEVICE_CHANGE: 7,
  RemoteMessageContentType.VOICE_CALL: 8,
  RemoteMessageContentType.INVITE_ROOM: 9,
};

const _$RemoteMessageTypeEnumMap = {
  RemoteMessageType.USER: 1,
};
