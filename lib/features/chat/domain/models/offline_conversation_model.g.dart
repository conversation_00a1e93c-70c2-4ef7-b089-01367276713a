// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'offline_conversation_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OfflineConversationModelImpl _$$OfflineConversationModelImplFromJson(
        Map<String, dynamic> json) =>
    _$OfflineConversationModelImpl(
      conversationId: json['conversationId'] as String?,
      receiverId: json['receiverId'] as String?,
      lastReadMsg: json['lastReadMsg'] == null
          ? null
          : RemoteMessageModel.fromJson(
              json['lastReadMsg'] as Map<String, dynamic>),
      lastMsg: json['lastMsg'] == null
          ? null
          : RemoteMessageModel.fromJson(
              json['lastMsg'] as Map<String, dynamic>),
      unreadCount: (json['unreadCount'] as num?)?.toInt() ?? 0,
      pinned: json['pinned'] as bool? ?? false,
      muted: json['muted'] as bool? ?? false,
      lastMsgTs: (json['lastMsgTs'] as num?)?.toInt(),
      conversationSetting: json['conversationSetting'] == null
          ? null
          : ConversationSetting.fromJson(
              json['conversationSetting'] as Map<String, dynamic>),
      extend: json['extend'] == null
          ? const ConversationExtendModel()
          : ConversationExtendModel.fromJson(
              Map<String, String>.from(json['extend'] as Map)),
    );

Map<String, dynamic> _$$OfflineConversationModelImplToJson(
        _$OfflineConversationModelImpl instance) =>
    <String, dynamic>{
      'conversationId': instance.conversationId,
      'receiverId': instance.receiverId,
      'lastReadMsg': instance.lastReadMsg?.toJson(),
      'lastMsg': instance.lastMsg?.toJson(),
      'unreadCount': instance.unreadCount,
      'pinned': instance.pinned,
      'muted': instance.muted,
      'lastMsgTs': instance.lastMsgTs,
      'conversationSetting': instance.conversationSetting?.toJson(),
      'extend': instance.extend.toJson(),
    };
