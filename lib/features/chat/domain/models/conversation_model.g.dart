// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversation_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ConversationModelImpl _$$ConversationModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ConversationModelImpl(
      id: json['id'] as String,
      peer: User.fromJson(json['peer'] as Map<String, dynamic>),
      updatedAt: (json['updatedAt'] as num).toInt(),
      ephemeralTimeout: (json['ephemeralTimeout'] as num).toInt(),
      lastMessage: json['lastMessage'] == null
          ? null
          : Message.fromJson(json['lastMessage'] as Map<String, dynamic>),
      lastReadMessage: json['lastReadMessage'] == null
          ? null
          : Message.fromJson(json['lastReadMessage'] as Map<String, dynamic>),
      unreadCount: (json['unreadCount'] as num?)?.toInt() ?? 0,
      pinned: json['pinned'] as bool? ?? false,
      muted: json['muted'] as bool? ?? false,
      extend: json['extend'] == null
          ? const ConversationExtendModel()
          : ConversationExtendModel.fromJson(
              Map<String, String>.from(json['extend'] as Map)),
    );

Map<String, dynamic> _$$ConversationModelImplToJson(
        _$ConversationModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'peer': instance.peer.toJson(),
      'updatedAt': instance.updatedAt,
      'ephemeralTimeout': instance.ephemeralTimeout,
      'lastMessage': instance.lastMessage?.toJson(),
      'lastReadMessage': instance.lastReadMessage?.toJson(),
      'unreadCount': instance.unreadCount,
      'pinned': instance.pinned,
      'muted': instance.muted,
      'extend': instance.extend.toJson(),
    };
