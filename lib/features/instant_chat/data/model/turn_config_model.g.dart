// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'turn_config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TurnConfigModelImpl _$$TurnConfigModelImplFromJson(
        Map<String, dynamic> json) =>
    _$TurnConfigModelImpl(
      forceEnable: json['forceEnable'] as bool?,
      serverHost: json['serverHost'] as String?,
      serverPort: json['serverPort'] as String?,
      username: json['username'] as String?,
      password: json['password'] as String?,
    );

Map<String, dynamic> _$$TurnConfigModelImplToJson(
        _$TurnConfigModelImpl instance) =>
    <String, dynamic>{
      'forceEnable': instance.forceEnable,
      'serverHost': instance.serverHost,
      'serverPort': instance.serverPort,
      'username': instance.username,
      'password': instance.password,
    };
