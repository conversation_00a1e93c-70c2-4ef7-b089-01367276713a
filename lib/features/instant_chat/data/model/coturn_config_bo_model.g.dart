// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'coturn_config_bo_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CoturnConfigBOModelImpl _$$CoturnConfigBOModelImplFromJson(
        Map<String, dynamic> json) =>
    _$CoturnConfigBOModelImpl(
      stunConfigs: (json['stunConfigs'] as List<dynamic>?)
          ?.map((e) => StunConfigBOModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      turnConfigs: (json['turnConfigs'] as List<dynamic>?)
          ?.map((e) => TurnConfigModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$CoturnConfigBOModelImplToJson(
        _$CoturnConfigBOModelImpl instance) =>
    <String, dynamic>{
      'stunConfigs': instance.stunConfigs?.map((e) => e.toJson()).toList(),
      'turnConfigs': instance.turnConfigs?.map((e) => e.toJson()).toList(),
    };
