// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'instant_call_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$InstantCallState {
  MatchStatus get matchStatus => throw _privateConstructorUsedError;
  CallStatus get callStatus => throw _privateConstructorUsedError;
  String get myUserId => throw _privateConstructorUsedError;
  String? get peerId => throw _privateConstructorUsedError;
  String? get peerNickName => throw _privateConstructorUsedError;
  Map<String, dynamic> get peerAvatar => throw _privateConstructorUsedError;
  String? get matchId => throw _privateConstructorUsedError;
  bool get isCaller => throw _privateConstructorUsedError;
  RoomMessageGift? get lastGiftInfo => throw _privateConstructorUsedError;
  Duration get duration => throw _privateConstructorUsedError;
  EncryptionState get encryptionState => throw _privateConstructorUsedError;
  Map<String, int> get cryptorStates => throw _privateConstructorUsedError;
  bool get isAudioEncryptEnabled => throw _privateConstructorUsedError;
  bool get isAudioDecryptEnabled => throw _privateConstructorUsedError;
  RTCPeerConnection? get peerConnection => throw _privateConstructorUsedError;
  MediaStream? get localStream => throw _privateConstructorUsedError;
  MediaStream? get remoteStream => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $InstantCallStateCopyWith<InstantCallState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InstantCallStateCopyWith<$Res> {
  factory $InstantCallStateCopyWith(
          InstantCallState value, $Res Function(InstantCallState) then) =
      _$InstantCallStateCopyWithImpl<$Res, InstantCallState>;
  @useResult
  $Res call(
      {MatchStatus matchStatus,
      CallStatus callStatus,
      String myUserId,
      String? peerId,
      String? peerNickName,
      Map<String, dynamic> peerAvatar,
      String? matchId,
      bool isCaller,
      RoomMessageGift? lastGiftInfo,
      Duration duration,
      EncryptionState encryptionState,
      Map<String, int> cryptorStates,
      bool isAudioEncryptEnabled,
      bool isAudioDecryptEnabled,
      RTCPeerConnection? peerConnection,
      MediaStream? localStream,
      MediaStream? remoteStream});

  $RoomMessageGiftCopyWith<$Res>? get lastGiftInfo;
}

/// @nodoc
class _$InstantCallStateCopyWithImpl<$Res, $Val extends InstantCallState>
    implements $InstantCallStateCopyWith<$Res> {
  _$InstantCallStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? matchStatus = null,
    Object? callStatus = null,
    Object? myUserId = null,
    Object? peerId = freezed,
    Object? peerNickName = freezed,
    Object? peerAvatar = null,
    Object? matchId = freezed,
    Object? isCaller = null,
    Object? lastGiftInfo = freezed,
    Object? duration = null,
    Object? encryptionState = null,
    Object? cryptorStates = null,
    Object? isAudioEncryptEnabled = null,
    Object? isAudioDecryptEnabled = null,
    Object? peerConnection = freezed,
    Object? localStream = freezed,
    Object? remoteStream = freezed,
  }) {
    return _then(_value.copyWith(
      matchStatus: null == matchStatus
          ? _value.matchStatus
          : matchStatus // ignore: cast_nullable_to_non_nullable
              as MatchStatus,
      callStatus: null == callStatus
          ? _value.callStatus
          : callStatus // ignore: cast_nullable_to_non_nullable
              as CallStatus,
      myUserId: null == myUserId
          ? _value.myUserId
          : myUserId // ignore: cast_nullable_to_non_nullable
              as String,
      peerId: freezed == peerId
          ? _value.peerId
          : peerId // ignore: cast_nullable_to_non_nullable
              as String?,
      peerNickName: freezed == peerNickName
          ? _value.peerNickName
          : peerNickName // ignore: cast_nullable_to_non_nullable
              as String?,
      peerAvatar: null == peerAvatar
          ? _value.peerAvatar
          : peerAvatar // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      matchId: freezed == matchId
          ? _value.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String?,
      isCaller: null == isCaller
          ? _value.isCaller
          : isCaller // ignore: cast_nullable_to_non_nullable
              as bool,
      lastGiftInfo: freezed == lastGiftInfo
          ? _value.lastGiftInfo
          : lastGiftInfo // ignore: cast_nullable_to_non_nullable
              as RoomMessageGift?,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as Duration,
      encryptionState: null == encryptionState
          ? _value.encryptionState
          : encryptionState // ignore: cast_nullable_to_non_nullable
              as EncryptionState,
      cryptorStates: null == cryptorStates
          ? _value.cryptorStates
          : cryptorStates // ignore: cast_nullable_to_non_nullable
              as Map<String, int>,
      isAudioEncryptEnabled: null == isAudioEncryptEnabled
          ? _value.isAudioEncryptEnabled
          : isAudioEncryptEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      isAudioDecryptEnabled: null == isAudioDecryptEnabled
          ? _value.isAudioDecryptEnabled
          : isAudioDecryptEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      peerConnection: freezed == peerConnection
          ? _value.peerConnection
          : peerConnection // ignore: cast_nullable_to_non_nullable
              as RTCPeerConnection?,
      localStream: freezed == localStream
          ? _value.localStream
          : localStream // ignore: cast_nullable_to_non_nullable
              as MediaStream?,
      remoteStream: freezed == remoteStream
          ? _value.remoteStream
          : remoteStream // ignore: cast_nullable_to_non_nullable
              as MediaStream?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $RoomMessageGiftCopyWith<$Res>? get lastGiftInfo {
    if (_value.lastGiftInfo == null) {
      return null;
    }

    return $RoomMessageGiftCopyWith<$Res>(_value.lastGiftInfo!, (value) {
      return _then(_value.copyWith(lastGiftInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$InstantChatStateImplCopyWith<$Res>
    implements $InstantCallStateCopyWith<$Res> {
  factory _$$InstantChatStateImplCopyWith(_$InstantChatStateImpl value,
          $Res Function(_$InstantChatStateImpl) then) =
      __$$InstantChatStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {MatchStatus matchStatus,
      CallStatus callStatus,
      String myUserId,
      String? peerId,
      String? peerNickName,
      Map<String, dynamic> peerAvatar,
      String? matchId,
      bool isCaller,
      RoomMessageGift? lastGiftInfo,
      Duration duration,
      EncryptionState encryptionState,
      Map<String, int> cryptorStates,
      bool isAudioEncryptEnabled,
      bool isAudioDecryptEnabled,
      RTCPeerConnection? peerConnection,
      MediaStream? localStream,
      MediaStream? remoteStream});

  @override
  $RoomMessageGiftCopyWith<$Res>? get lastGiftInfo;
}

/// @nodoc
class __$$InstantChatStateImplCopyWithImpl<$Res>
    extends _$InstantCallStateCopyWithImpl<$Res, _$InstantChatStateImpl>
    implements _$$InstantChatStateImplCopyWith<$Res> {
  __$$InstantChatStateImplCopyWithImpl(_$InstantChatStateImpl _value,
      $Res Function(_$InstantChatStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? matchStatus = null,
    Object? callStatus = null,
    Object? myUserId = null,
    Object? peerId = freezed,
    Object? peerNickName = freezed,
    Object? peerAvatar = null,
    Object? matchId = freezed,
    Object? isCaller = null,
    Object? lastGiftInfo = freezed,
    Object? duration = null,
    Object? encryptionState = null,
    Object? cryptorStates = null,
    Object? isAudioEncryptEnabled = null,
    Object? isAudioDecryptEnabled = null,
    Object? peerConnection = freezed,
    Object? localStream = freezed,
    Object? remoteStream = freezed,
  }) {
    return _then(_$InstantChatStateImpl(
      matchStatus: null == matchStatus
          ? _value.matchStatus
          : matchStatus // ignore: cast_nullable_to_non_nullable
              as MatchStatus,
      callStatus: null == callStatus
          ? _value.callStatus
          : callStatus // ignore: cast_nullable_to_non_nullable
              as CallStatus,
      myUserId: null == myUserId
          ? _value.myUserId
          : myUserId // ignore: cast_nullable_to_non_nullable
              as String,
      peerId: freezed == peerId
          ? _value.peerId
          : peerId // ignore: cast_nullable_to_non_nullable
              as String?,
      peerNickName: freezed == peerNickName
          ? _value.peerNickName
          : peerNickName // ignore: cast_nullable_to_non_nullable
              as String?,
      peerAvatar: null == peerAvatar
          ? _value._peerAvatar
          : peerAvatar // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      matchId: freezed == matchId
          ? _value.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String?,
      isCaller: null == isCaller
          ? _value.isCaller
          : isCaller // ignore: cast_nullable_to_non_nullable
              as bool,
      lastGiftInfo: freezed == lastGiftInfo
          ? _value.lastGiftInfo
          : lastGiftInfo // ignore: cast_nullable_to_non_nullable
              as RoomMessageGift?,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as Duration,
      encryptionState: null == encryptionState
          ? _value.encryptionState
          : encryptionState // ignore: cast_nullable_to_non_nullable
              as EncryptionState,
      cryptorStates: null == cryptorStates
          ? _value._cryptorStates
          : cryptorStates // ignore: cast_nullable_to_non_nullable
              as Map<String, int>,
      isAudioEncryptEnabled: null == isAudioEncryptEnabled
          ? _value.isAudioEncryptEnabled
          : isAudioEncryptEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      isAudioDecryptEnabled: null == isAudioDecryptEnabled
          ? _value.isAudioDecryptEnabled
          : isAudioDecryptEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      peerConnection: freezed == peerConnection
          ? _value.peerConnection
          : peerConnection // ignore: cast_nullable_to_non_nullable
              as RTCPeerConnection?,
      localStream: freezed == localStream
          ? _value.localStream
          : localStream // ignore: cast_nullable_to_non_nullable
              as MediaStream?,
      remoteStream: freezed == remoteStream
          ? _value.remoteStream
          : remoteStream // ignore: cast_nullable_to_non_nullable
              as MediaStream?,
    ));
  }
}

/// @nodoc

class _$InstantChatStateImpl extends _InstantChatState {
  const _$InstantChatStateImpl(
      {required this.matchStatus,
      required this.callStatus,
      required this.myUserId,
      this.peerId = null,
      this.peerNickName = null,
      final Map<String, dynamic> peerAvatar = const {},
      this.matchId = null,
      this.isCaller = false,
      this.lastGiftInfo,
      this.duration = Duration.zero,
      this.encryptionState = EncryptionState.notInitialized,
      final Map<String, int> cryptorStates = const {},
      this.isAudioEncryptEnabled = false,
      this.isAudioDecryptEnabled = false,
      this.peerConnection,
      this.localStream,
      this.remoteStream})
      : _peerAvatar = peerAvatar,
        _cryptorStates = cryptorStates,
        super._();

  @override
  final MatchStatus matchStatus;
  @override
  final CallStatus callStatus;
  @override
  final String myUserId;
  @override
  @JsonKey()
  final String? peerId;
  @override
  @JsonKey()
  final String? peerNickName;
  final Map<String, dynamic> _peerAvatar;
  @override
  @JsonKey()
  Map<String, dynamic> get peerAvatar {
    if (_peerAvatar is EqualUnmodifiableMapView) return _peerAvatar;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_peerAvatar);
  }

  @override
  @JsonKey()
  final String? matchId;
  @override
  @JsonKey()
  final bool isCaller;
  @override
  final RoomMessageGift? lastGiftInfo;
  @override
  @JsonKey()
  final Duration duration;
  @override
  @JsonKey()
  final EncryptionState encryptionState;
  final Map<String, int> _cryptorStates;
  @override
  @JsonKey()
  Map<String, int> get cryptorStates {
    if (_cryptorStates is EqualUnmodifiableMapView) return _cryptorStates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_cryptorStates);
  }

  @override
  @JsonKey()
  final bool isAudioEncryptEnabled;
  @override
  @JsonKey()
  final bool isAudioDecryptEnabled;
  @override
  final RTCPeerConnection? peerConnection;
  @override
  final MediaStream? localStream;
  @override
  final MediaStream? remoteStream;

  @override
  String toString() {
    return 'InstantCallState(matchStatus: $matchStatus, callStatus: $callStatus, myUserId: $myUserId, peerId: $peerId, peerNickName: $peerNickName, peerAvatar: $peerAvatar, matchId: $matchId, isCaller: $isCaller, lastGiftInfo: $lastGiftInfo, duration: $duration, encryptionState: $encryptionState, cryptorStates: $cryptorStates, isAudioEncryptEnabled: $isAudioEncryptEnabled, isAudioDecryptEnabled: $isAudioDecryptEnabled, peerConnection: $peerConnection, localStream: $localStream, remoteStream: $remoteStream)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InstantChatStateImpl &&
            (identical(other.matchStatus, matchStatus) ||
                other.matchStatus == matchStatus) &&
            (identical(other.callStatus, callStatus) ||
                other.callStatus == callStatus) &&
            (identical(other.myUserId, myUserId) ||
                other.myUserId == myUserId) &&
            (identical(other.peerId, peerId) || other.peerId == peerId) &&
            (identical(other.peerNickName, peerNickName) ||
                other.peerNickName == peerNickName) &&
            const DeepCollectionEquality()
                .equals(other._peerAvatar, _peerAvatar) &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            (identical(other.isCaller, isCaller) ||
                other.isCaller == isCaller) &&
            (identical(other.lastGiftInfo, lastGiftInfo) ||
                other.lastGiftInfo == lastGiftInfo) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.encryptionState, encryptionState) ||
                other.encryptionState == encryptionState) &&
            const DeepCollectionEquality()
                .equals(other._cryptorStates, _cryptorStates) &&
            (identical(other.isAudioEncryptEnabled, isAudioEncryptEnabled) ||
                other.isAudioEncryptEnabled == isAudioEncryptEnabled) &&
            (identical(other.isAudioDecryptEnabled, isAudioDecryptEnabled) ||
                other.isAudioDecryptEnabled == isAudioDecryptEnabled) &&
            (identical(other.peerConnection, peerConnection) ||
                other.peerConnection == peerConnection) &&
            (identical(other.localStream, localStream) ||
                other.localStream == localStream) &&
            (identical(other.remoteStream, remoteStream) ||
                other.remoteStream == remoteStream));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      matchStatus,
      callStatus,
      myUserId,
      peerId,
      peerNickName,
      const DeepCollectionEquality().hash(_peerAvatar),
      matchId,
      isCaller,
      lastGiftInfo,
      duration,
      encryptionState,
      const DeepCollectionEquality().hash(_cryptorStates),
      isAudioEncryptEnabled,
      isAudioDecryptEnabled,
      peerConnection,
      localStream,
      remoteStream);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$InstantChatStateImplCopyWith<_$InstantChatStateImpl> get copyWith =>
      __$$InstantChatStateImplCopyWithImpl<_$InstantChatStateImpl>(
          this, _$identity);
}

abstract class _InstantChatState extends InstantCallState {
  const factory _InstantChatState(
      {required final MatchStatus matchStatus,
      required final CallStatus callStatus,
      required final String myUserId,
      final String? peerId,
      final String? peerNickName,
      final Map<String, dynamic> peerAvatar,
      final String? matchId,
      final bool isCaller,
      final RoomMessageGift? lastGiftInfo,
      final Duration duration,
      final EncryptionState encryptionState,
      final Map<String, int> cryptorStates,
      final bool isAudioEncryptEnabled,
      final bool isAudioDecryptEnabled,
      final RTCPeerConnection? peerConnection,
      final MediaStream? localStream,
      final MediaStream? remoteStream}) = _$InstantChatStateImpl;
  const _InstantChatState._() : super._();

  @override
  MatchStatus get matchStatus;
  @override
  CallStatus get callStatus;
  @override
  String get myUserId;
  @override
  String? get peerId;
  @override
  String? get peerNickName;
  @override
  Map<String, dynamic> get peerAvatar;
  @override
  String? get matchId;
  @override
  bool get isCaller;
  @override
  RoomMessageGift? get lastGiftInfo;
  @override
  Duration get duration;
  @override
  EncryptionState get encryptionState;
  @override
  Map<String, int> get cryptorStates;
  @override
  bool get isAudioEncryptEnabled;
  @override
  bool get isAudioDecryptEnabled;
  @override
  RTCPeerConnection? get peerConnection;
  @override
  MediaStream? get localStream;
  @override
  MediaStream? get remoteStream;
  @override
  @JsonKey(ignore: true)
  _$$InstantChatStateImplCopyWith<_$InstantChatStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
