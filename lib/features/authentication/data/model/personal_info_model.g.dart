// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'personal_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PersonalInfoModelImpl _$$PersonalInfoModelImplFromJson(
        Map<String, dynamic> json) =>
    _$PersonalInfoModelImpl(
      profileVO: json['profileVO'] == null
          ? null
          : ProfileModel.fromJson(json['profileVO'] as Map<String, dynamic>),
      followeeCount: (json['followeeCount'] as num?)?.toInt() ?? 0,
      followerCount: (json['followerCount'] as num?)?.toInt() ?? 0,
      mutualFollowCount: (json['mutualFollowCount'] as num?)?.toInt() ?? 0,
      userSubscriptionInfo: json['userSubscriptionInfo'] == null
          ? null
          : SubscriptionInfoModel.fromJson(
              json['userSubscriptionInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$PersonalInfoModelImplToJson(
        _$PersonalInfoModelImpl instance) =>
    <String, dynamic>{
      'profileVO': instance.profileVO?.toJson(),
      'followeeCount': instance.followeeCount,
      'followerCount': instance.followerCount,
      'mutualFollowCount': instance.mutualFollowCount,
      'userSubscriptionInfo': instance.userSubscriptionInfo?.toJson(),
    };
