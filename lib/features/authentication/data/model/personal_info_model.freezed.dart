// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'personal_info_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PersonalInfoModel _$PersonalInfoModelFromJson(Map<String, dynamic> json) {
  return _PersonalInfoModel.fromJson(json);
}

/// @nodoc
mixin _$PersonalInfoModel {
  @JsonKey(name: 'profileVO')
  ProfileModel? get profileVO => throw _privateConstructorUsedError;
  int get followeeCount => throw _privateConstructorUsedError;
  int get followerCount => throw _privateConstructorUsedError;
  int get mutualFollowCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'userSubscriptionInfo')
  SubscriptionInfoModel? get userSubscriptionInfo =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PersonalInfoModelCopyWith<PersonalInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PersonalInfoModelCopyWith<$Res> {
  factory $PersonalInfoModelCopyWith(
          PersonalInfoModel value, $Res Function(PersonalInfoModel) then) =
      _$PersonalInfoModelCopyWithImpl<$Res, PersonalInfoModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'profileVO') ProfileModel? profileVO,
      int followeeCount,
      int followerCount,
      int mutualFollowCount,
      @JsonKey(name: 'userSubscriptionInfo')
      SubscriptionInfoModel? userSubscriptionInfo});

  $ProfileModelCopyWith<$Res>? get profileVO;
  $SubscriptionInfoModelCopyWith<$Res>? get userSubscriptionInfo;
}

/// @nodoc
class _$PersonalInfoModelCopyWithImpl<$Res, $Val extends PersonalInfoModel>
    implements $PersonalInfoModelCopyWith<$Res> {
  _$PersonalInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profileVO = freezed,
    Object? followeeCount = null,
    Object? followerCount = null,
    Object? mutualFollowCount = null,
    Object? userSubscriptionInfo = freezed,
  }) {
    return _then(_value.copyWith(
      profileVO: freezed == profileVO
          ? _value.profileVO
          : profileVO // ignore: cast_nullable_to_non_nullable
              as ProfileModel?,
      followeeCount: null == followeeCount
          ? _value.followeeCount
          : followeeCount // ignore: cast_nullable_to_non_nullable
              as int,
      followerCount: null == followerCount
          ? _value.followerCount
          : followerCount // ignore: cast_nullable_to_non_nullable
              as int,
      mutualFollowCount: null == mutualFollowCount
          ? _value.mutualFollowCount
          : mutualFollowCount // ignore: cast_nullable_to_non_nullable
              as int,
      userSubscriptionInfo: freezed == userSubscriptionInfo
          ? _value.userSubscriptionInfo
          : userSubscriptionInfo // ignore: cast_nullable_to_non_nullable
              as SubscriptionInfoModel?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ProfileModelCopyWith<$Res>? get profileVO {
    if (_value.profileVO == null) {
      return null;
    }

    return $ProfileModelCopyWith<$Res>(_value.profileVO!, (value) {
      return _then(_value.copyWith(profileVO: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SubscriptionInfoModelCopyWith<$Res>? get userSubscriptionInfo {
    if (_value.userSubscriptionInfo == null) {
      return null;
    }

    return $SubscriptionInfoModelCopyWith<$Res>(_value.userSubscriptionInfo!,
        (value) {
      return _then(_value.copyWith(userSubscriptionInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PersonalInfoModelImplCopyWith<$Res>
    implements $PersonalInfoModelCopyWith<$Res> {
  factory _$$PersonalInfoModelImplCopyWith(_$PersonalInfoModelImpl value,
          $Res Function(_$PersonalInfoModelImpl) then) =
      __$$PersonalInfoModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'profileVO') ProfileModel? profileVO,
      int followeeCount,
      int followerCount,
      int mutualFollowCount,
      @JsonKey(name: 'userSubscriptionInfo')
      SubscriptionInfoModel? userSubscriptionInfo});

  @override
  $ProfileModelCopyWith<$Res>? get profileVO;
  @override
  $SubscriptionInfoModelCopyWith<$Res>? get userSubscriptionInfo;
}

/// @nodoc
class __$$PersonalInfoModelImplCopyWithImpl<$Res>
    extends _$PersonalInfoModelCopyWithImpl<$Res, _$PersonalInfoModelImpl>
    implements _$$PersonalInfoModelImplCopyWith<$Res> {
  __$$PersonalInfoModelImplCopyWithImpl(_$PersonalInfoModelImpl _value,
      $Res Function(_$PersonalInfoModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profileVO = freezed,
    Object? followeeCount = null,
    Object? followerCount = null,
    Object? mutualFollowCount = null,
    Object? userSubscriptionInfo = freezed,
  }) {
    return _then(_$PersonalInfoModelImpl(
      profileVO: freezed == profileVO
          ? _value.profileVO
          : profileVO // ignore: cast_nullable_to_non_nullable
              as ProfileModel?,
      followeeCount: null == followeeCount
          ? _value.followeeCount
          : followeeCount // ignore: cast_nullable_to_non_nullable
              as int,
      followerCount: null == followerCount
          ? _value.followerCount
          : followerCount // ignore: cast_nullable_to_non_nullable
              as int,
      mutualFollowCount: null == mutualFollowCount
          ? _value.mutualFollowCount
          : mutualFollowCount // ignore: cast_nullable_to_non_nullable
              as int,
      userSubscriptionInfo: freezed == userSubscriptionInfo
          ? _value.userSubscriptionInfo
          : userSubscriptionInfo // ignore: cast_nullable_to_non_nullable
              as SubscriptionInfoModel?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$PersonalInfoModelImpl implements _PersonalInfoModel {
  const _$PersonalInfoModelImpl(
      {@JsonKey(name: 'profileVO') this.profileVO,
      this.followeeCount = 0,
      this.followerCount = 0,
      this.mutualFollowCount = 0,
      @JsonKey(name: 'userSubscriptionInfo') this.userSubscriptionInfo});

  factory _$PersonalInfoModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$PersonalInfoModelImplFromJson(json);

  @override
  @JsonKey(name: 'profileVO')
  final ProfileModel? profileVO;
  @override
  @JsonKey()
  final int followeeCount;
  @override
  @JsonKey()
  final int followerCount;
  @override
  @JsonKey()
  final int mutualFollowCount;
  @override
  @JsonKey(name: 'userSubscriptionInfo')
  final SubscriptionInfoModel? userSubscriptionInfo;

  @override
  String toString() {
    return 'PersonalInfoModel(profileVO: $profileVO, followeeCount: $followeeCount, followerCount: $followerCount, mutualFollowCount: $mutualFollowCount, userSubscriptionInfo: $userSubscriptionInfo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PersonalInfoModelImpl &&
            (identical(other.profileVO, profileVO) ||
                other.profileVO == profileVO) &&
            (identical(other.followeeCount, followeeCount) ||
                other.followeeCount == followeeCount) &&
            (identical(other.followerCount, followerCount) ||
                other.followerCount == followerCount) &&
            (identical(other.mutualFollowCount, mutualFollowCount) ||
                other.mutualFollowCount == mutualFollowCount) &&
            (identical(other.userSubscriptionInfo, userSubscriptionInfo) ||
                other.userSubscriptionInfo == userSubscriptionInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, profileVO, followeeCount,
      followerCount, mutualFollowCount, userSubscriptionInfo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PersonalInfoModelImplCopyWith<_$PersonalInfoModelImpl> get copyWith =>
      __$$PersonalInfoModelImplCopyWithImpl<_$PersonalInfoModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PersonalInfoModelImplToJson(
      this,
    );
  }
}

abstract class _PersonalInfoModel implements PersonalInfoModel {
  const factory _PersonalInfoModel(
          {@JsonKey(name: 'profileVO') final ProfileModel? profileVO,
          final int followeeCount,
          final int followerCount,
          final int mutualFollowCount,
          @JsonKey(name: 'userSubscriptionInfo')
          final SubscriptionInfoModel? userSubscriptionInfo}) =
      _$PersonalInfoModelImpl;

  factory _PersonalInfoModel.fromJson(Map<String, dynamic> json) =
      _$PersonalInfoModelImpl.fromJson;

  @override
  @JsonKey(name: 'profileVO')
  ProfileModel? get profileVO;
  @override
  int get followeeCount;
  @override
  int get followerCount;
  @override
  int get mutualFollowCount;
  @override
  @JsonKey(name: 'userSubscriptionInfo')
  SubscriptionInfoModel? get userSubscriptionInfo;
  @override
  @JsonKey(ignore: true)
  _$$PersonalInfoModelImplCopyWith<_$PersonalInfoModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
