// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_level_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserLevelModelImpl _$$UserLevelModelImplFromJson(Map<String, dynamic> json) =>
    _$UserLevelModelImpl(
      curGemSpent: (json['curGemSpent'] as num?)?.toInt(),
      curLevel: (json['curLevel'] as num?)?.toInt(),
      nextLevel: (json['nextLevel'] as num?)?.toInt(),
      curLevelRequiredGems: (json['curLevelRequiredGems'] as num?)?.toInt(),
      nextLevelRequiredGems: (json['nextLevelRequiredGems'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$UserLevelModelImplToJson(
        _$UserLevelModelImpl instance) =>
    <String, dynamic>{
      'curGemSpent': instance.curGemSpent,
      'curLevel': instance.curLevel,
      'nextLevel': instance.nextLevel,
      'curLevelRequiredGems': instance.curLevelRequiredGems,
      'nextLevelRequiredGems': instance.nextLevelRequiredGems,
    };
