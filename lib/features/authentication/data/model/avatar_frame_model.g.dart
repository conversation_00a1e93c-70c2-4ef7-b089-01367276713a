// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'avatar_frame_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AvatarFrameModelImpl _$$AvatarFrameModelImplFromJson(
        Map<String, dynamic> json) =>
    _$AvatarFrameModelImpl(
      id: (json['id'] as num?)?.toInt(),
      userId: json['userId'] as String?,
      giftId: (json['giftId'] as num?)?.toInt(),
      source: json['source'] as String?,
      giftStatus: json['giftStatus'] as String?,
      activateExpireTime: json['activateExpireTime'] == null
          ? null
          : DateTime.parse(json['activateExpireTime'] as String),
      createdTime: json['createdTime'] == null
          ? null
          : DateTime.parse(json['createdTime'] as String),
      expireTime: json['expireTime'] == null
          ? null
          : DateTime.parse(json['expireTime'] as String),
      name: json['name'] as String?,
      imageUrl: json['imageUrl'] as String?,
      svgaUrl: json['svgaUrl'] as String?,
      price: (json['price'] as num?)?.toInt(),
      giftFormat: json['giftFormat'] as String?,
    );

Map<String, dynamic> _$$AvatarFrameModelImplToJson(
        _$AvatarFrameModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'giftId': instance.giftId,
      'source': instance.source,
      'giftStatus': instance.giftStatus,
      'activateExpireTime': instance.activateExpireTime?.toIso8601String(),
      'createdTime': instance.createdTime?.toIso8601String(),
      'expireTime': instance.expireTime?.toIso8601String(),
      'name': instance.name,
      'imageUrl': instance.imageUrl,
      'svgaUrl': instance.svgaUrl,
      'price': instance.price,
      'giftFormat': instance.giftFormat,
    };
