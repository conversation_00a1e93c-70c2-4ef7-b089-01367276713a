// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_info_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserInfoModel _$UserInfoModelFromJson(Map<String, dynamic> json) {
  return _UserInfoModel.fromJson(json);
}

/// @nodoc
mixin _$UserInfoModel {
  ProfileModel? get profile => throw _privateConstructorUsedError;
  SessionInfoModel? get sessionInfo => throw _privateConstructorUsedError;
  AvatarFrameModel? get frame => throw _privateConstructorUsedError;
  SubscriptionInfoModel? get subscriptionInfo =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserInfoModelCopyWith<UserInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserInfoModelCopyWith<$Res> {
  factory $UserInfoModelCopyWith(
          UserInfoModel value, $Res Function(UserInfoModel) then) =
      _$UserInfoModelCopyWithImpl<$Res, UserInfoModel>;
  @useResult
  $Res call(
      {ProfileModel? profile,
      SessionInfoModel? sessionInfo,
      AvatarFrameModel? frame,
      SubscriptionInfoModel? subscriptionInfo});

  $ProfileModelCopyWith<$Res>? get profile;
  $SessionInfoModelCopyWith<$Res>? get sessionInfo;
  $AvatarFrameModelCopyWith<$Res>? get frame;
  $SubscriptionInfoModelCopyWith<$Res>? get subscriptionInfo;
}

/// @nodoc
class _$UserInfoModelCopyWithImpl<$Res, $Val extends UserInfoModel>
    implements $UserInfoModelCopyWith<$Res> {
  _$UserInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profile = freezed,
    Object? sessionInfo = freezed,
    Object? frame = freezed,
    Object? subscriptionInfo = freezed,
  }) {
    return _then(_value.copyWith(
      profile: freezed == profile
          ? _value.profile
          : profile // ignore: cast_nullable_to_non_nullable
              as ProfileModel?,
      sessionInfo: freezed == sessionInfo
          ? _value.sessionInfo
          : sessionInfo // ignore: cast_nullable_to_non_nullable
              as SessionInfoModel?,
      frame: freezed == frame
          ? _value.frame
          : frame // ignore: cast_nullable_to_non_nullable
              as AvatarFrameModel?,
      subscriptionInfo: freezed == subscriptionInfo
          ? _value.subscriptionInfo
          : subscriptionInfo // ignore: cast_nullable_to_non_nullable
              as SubscriptionInfoModel?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ProfileModelCopyWith<$Res>? get profile {
    if (_value.profile == null) {
      return null;
    }

    return $ProfileModelCopyWith<$Res>(_value.profile!, (value) {
      return _then(_value.copyWith(profile: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SessionInfoModelCopyWith<$Res>? get sessionInfo {
    if (_value.sessionInfo == null) {
      return null;
    }

    return $SessionInfoModelCopyWith<$Res>(_value.sessionInfo!, (value) {
      return _then(_value.copyWith(sessionInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $AvatarFrameModelCopyWith<$Res>? get frame {
    if (_value.frame == null) {
      return null;
    }

    return $AvatarFrameModelCopyWith<$Res>(_value.frame!, (value) {
      return _then(_value.copyWith(frame: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SubscriptionInfoModelCopyWith<$Res>? get subscriptionInfo {
    if (_value.subscriptionInfo == null) {
      return null;
    }

    return $SubscriptionInfoModelCopyWith<$Res>(_value.subscriptionInfo!,
        (value) {
      return _then(_value.copyWith(subscriptionInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserInfoModelImplCopyWith<$Res>
    implements $UserInfoModelCopyWith<$Res> {
  factory _$$UserInfoModelImplCopyWith(
          _$UserInfoModelImpl value, $Res Function(_$UserInfoModelImpl) then) =
      __$$UserInfoModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {ProfileModel? profile,
      SessionInfoModel? sessionInfo,
      AvatarFrameModel? frame,
      SubscriptionInfoModel? subscriptionInfo});

  @override
  $ProfileModelCopyWith<$Res>? get profile;
  @override
  $SessionInfoModelCopyWith<$Res>? get sessionInfo;
  @override
  $AvatarFrameModelCopyWith<$Res>? get frame;
  @override
  $SubscriptionInfoModelCopyWith<$Res>? get subscriptionInfo;
}

/// @nodoc
class __$$UserInfoModelImplCopyWithImpl<$Res>
    extends _$UserInfoModelCopyWithImpl<$Res, _$UserInfoModelImpl>
    implements _$$UserInfoModelImplCopyWith<$Res> {
  __$$UserInfoModelImplCopyWithImpl(
      _$UserInfoModelImpl _value, $Res Function(_$UserInfoModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profile = freezed,
    Object? sessionInfo = freezed,
    Object? frame = freezed,
    Object? subscriptionInfo = freezed,
  }) {
    return _then(_$UserInfoModelImpl(
      profile: freezed == profile
          ? _value.profile
          : profile // ignore: cast_nullable_to_non_nullable
              as ProfileModel?,
      sessionInfo: freezed == sessionInfo
          ? _value.sessionInfo
          : sessionInfo // ignore: cast_nullable_to_non_nullable
              as SessionInfoModel?,
      frame: freezed == frame
          ? _value.frame
          : frame // ignore: cast_nullable_to_non_nullable
              as AvatarFrameModel?,
      subscriptionInfo: freezed == subscriptionInfo
          ? _value.subscriptionInfo
          : subscriptionInfo // ignore: cast_nullable_to_non_nullable
              as SubscriptionInfoModel?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$UserInfoModelImpl implements _UserInfoModel {
  const _$UserInfoModelImpl(
      {this.profile, this.sessionInfo, this.frame, this.subscriptionInfo});

  factory _$UserInfoModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserInfoModelImplFromJson(json);

  @override
  final ProfileModel? profile;
  @override
  final SessionInfoModel? sessionInfo;
  @override
  final AvatarFrameModel? frame;
  @override
  final SubscriptionInfoModel? subscriptionInfo;

  @override
  String toString() {
    return 'UserInfoModel(profile: $profile, sessionInfo: $sessionInfo, frame: $frame, subscriptionInfo: $subscriptionInfo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserInfoModelImpl &&
            (identical(other.profile, profile) || other.profile == profile) &&
            (identical(other.sessionInfo, sessionInfo) ||
                other.sessionInfo == sessionInfo) &&
            (identical(other.frame, frame) || other.frame == frame) &&
            (identical(other.subscriptionInfo, subscriptionInfo) ||
                other.subscriptionInfo == subscriptionInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, profile, sessionInfo, frame, subscriptionInfo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UserInfoModelImplCopyWith<_$UserInfoModelImpl> get copyWith =>
      __$$UserInfoModelImplCopyWithImpl<_$UserInfoModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserInfoModelImplToJson(
      this,
    );
  }
}

abstract class _UserInfoModel implements UserInfoModel {
  const factory _UserInfoModel(
      {final ProfileModel? profile,
      final SessionInfoModel? sessionInfo,
      final AvatarFrameModel? frame,
      final SubscriptionInfoModel? subscriptionInfo}) = _$UserInfoModelImpl;

  factory _UserInfoModel.fromJson(Map<String, dynamic> json) =
      _$UserInfoModelImpl.fromJson;

  @override
  ProfileModel? get profile;
  @override
  SessionInfoModel? get sessionInfo;
  @override
  AvatarFrameModel? get frame;
  @override
  SubscriptionInfoModel? get subscriptionInfo;
  @override
  @JsonKey(ignore: true)
  _$$UserInfoModelImplCopyWith<_$UserInfoModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
