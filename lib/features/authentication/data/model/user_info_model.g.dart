// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserInfoModelImpl _$$UserInfoModelImplFromJson(Map<String, dynamic> json) =>
    _$UserInfoModelImpl(
      profile: json['profile'] == null
          ? null
          : ProfileModel.fromJson(json['profile'] as Map<String, dynamic>),
      sessionInfo: json['sessionInfo'] == null
          ? null
          : SessionInfoModel.fromJson(
              json['sessionInfo'] as Map<String, dynamic>),
      frame: json['frame'] == null
          ? null
          : AvatarFrameModel.fromJson(json['frame'] as Map<String, dynamic>),
      subscriptionInfo: json['subscriptionInfo'] == null
          ? null
          : SubscriptionInfoModel.fromJson(
              json['subscriptionInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$UserInfoModelImplToJson(_$UserInfoModelImpl instance) =>
    <String, dynamic>{
      'profile': instance.profile?.toJson(),
      'sessionInfo': instance.sessionInfo?.toJson(),
      'frame': instance.frame?.toJson(),
      'subscriptionInfo': instance.subscriptionInfo?.toJson(),
    };
