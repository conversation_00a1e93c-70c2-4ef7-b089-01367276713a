// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'profile_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ProfileModel _$ProfileModelFromJson(Map<String, dynamic> json) {
  return _ProfileModel.fromJson(json);
}

/// @nodoc
mixin _$ProfileModel {
  String? get id => throw _privateConstructorUsedError;
  String? get nickName => throw _privateConstructorUsedError;
  String? get role => throw _privateConstructorUsedError;
  String? get avatar => throw _privateConstructorUsedError;
  UserGender? get gender => throw _privateConstructorUsedError;
  String? get profileMessage => throw _privateConstructorUsedError;
  DateTime? get birthday => throw _privateConstructorUsedError;
  bool? get banned => throw _privateConstructorUsedError;
  DeleteStatus? get deleteStatus => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ProfileModelCopyWith<ProfileModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileModelCopyWith<$Res> {
  factory $ProfileModelCopyWith(
          ProfileModel value, $Res Function(ProfileModel) then) =
      _$ProfileModelCopyWithImpl<$Res, ProfileModel>;
  @useResult
  $Res call(
      {String? id,
      String? nickName,
      String? role,
      String? avatar,
      UserGender? gender,
      String? profileMessage,
      DateTime? birthday,
      bool? banned,
      DeleteStatus? deleteStatus});
}

/// @nodoc
class _$ProfileModelCopyWithImpl<$Res, $Val extends ProfileModel>
    implements $ProfileModelCopyWith<$Res> {
  _$ProfileModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? nickName = freezed,
    Object? role = freezed,
    Object? avatar = freezed,
    Object? gender = freezed,
    Object? profileMessage = freezed,
    Object? birthday = freezed,
    Object? banned = freezed,
    Object? deleteStatus = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      nickName: freezed == nickName
          ? _value.nickName
          : nickName // ignore: cast_nullable_to_non_nullable
              as String?,
      role: freezed == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String?,
      avatar: freezed == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String?,
      gender: freezed == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as UserGender?,
      profileMessage: freezed == profileMessage
          ? _value.profileMessage
          : profileMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      birthday: freezed == birthday
          ? _value.birthday
          : birthday // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      banned: freezed == banned
          ? _value.banned
          : banned // ignore: cast_nullable_to_non_nullable
              as bool?,
      deleteStatus: freezed == deleteStatus
          ? _value.deleteStatus
          : deleteStatus // ignore: cast_nullable_to_non_nullable
              as DeleteStatus?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProfileModelImplCopyWith<$Res>
    implements $ProfileModelCopyWith<$Res> {
  factory _$$ProfileModelImplCopyWith(
          _$ProfileModelImpl value, $Res Function(_$ProfileModelImpl) then) =
      __$$ProfileModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      String? nickName,
      String? role,
      String? avatar,
      UserGender? gender,
      String? profileMessage,
      DateTime? birthday,
      bool? banned,
      DeleteStatus? deleteStatus});
}

/// @nodoc
class __$$ProfileModelImplCopyWithImpl<$Res>
    extends _$ProfileModelCopyWithImpl<$Res, _$ProfileModelImpl>
    implements _$$ProfileModelImplCopyWith<$Res> {
  __$$ProfileModelImplCopyWithImpl(
      _$ProfileModelImpl _value, $Res Function(_$ProfileModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? nickName = freezed,
    Object? role = freezed,
    Object? avatar = freezed,
    Object? gender = freezed,
    Object? profileMessage = freezed,
    Object? birthday = freezed,
    Object? banned = freezed,
    Object? deleteStatus = freezed,
  }) {
    return _then(_$ProfileModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      nickName: freezed == nickName
          ? _value.nickName
          : nickName // ignore: cast_nullable_to_non_nullable
              as String?,
      role: freezed == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String?,
      avatar: freezed == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String?,
      gender: freezed == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as UserGender?,
      profileMessage: freezed == profileMessage
          ? _value.profileMessage
          : profileMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      birthday: freezed == birthday
          ? _value.birthday
          : birthday // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      banned: freezed == banned
          ? _value.banned
          : banned // ignore: cast_nullable_to_non_nullable
              as bool?,
      deleteStatus: freezed == deleteStatus
          ? _value.deleteStatus
          : deleteStatus // ignore: cast_nullable_to_non_nullable
              as DeleteStatus?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$ProfileModelImpl implements _ProfileModel {
  const _$ProfileModelImpl(
      {this.id,
      this.nickName,
      this.role,
      this.avatar,
      this.gender,
      this.profileMessage,
      this.birthday,
      this.banned,
      this.deleteStatus});

  factory _$ProfileModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfileModelImplFromJson(json);

  @override
  final String? id;
  @override
  final String? nickName;
  @override
  final String? role;
  @override
  final String? avatar;
  @override
  final UserGender? gender;
  @override
  final String? profileMessage;
  @override
  final DateTime? birthday;
  @override
  final bool? banned;
  @override
  final DeleteStatus? deleteStatus;

  @override
  String toString() {
    return 'ProfileModel(id: $id, nickName: $nickName, role: $role, avatar: $avatar, gender: $gender, profileMessage: $profileMessage, birthday: $birthday, banned: $banned, deleteStatus: $deleteStatus)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.nickName, nickName) ||
                other.nickName == nickName) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.profileMessage, profileMessage) ||
                other.profileMessage == profileMessage) &&
            (identical(other.birthday, birthday) ||
                other.birthday == birthday) &&
            (identical(other.banned, banned) || other.banned == banned) &&
            (identical(other.deleteStatus, deleteStatus) ||
                other.deleteStatus == deleteStatus));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, nickName, role, avatar,
      gender, profileMessage, birthday, banned, deleteStatus);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileModelImplCopyWith<_$ProfileModelImpl> get copyWith =>
      __$$ProfileModelImplCopyWithImpl<_$ProfileModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfileModelImplToJson(
      this,
    );
  }
}

abstract class _ProfileModel implements ProfileModel {
  const factory _ProfileModel(
      {final String? id,
      final String? nickName,
      final String? role,
      final String? avatar,
      final UserGender? gender,
      final String? profileMessage,
      final DateTime? birthday,
      final bool? banned,
      final DeleteStatus? deleteStatus}) = _$ProfileModelImpl;

  factory _ProfileModel.fromJson(Map<String, dynamic> json) =
      _$ProfileModelImpl.fromJson;

  @override
  String? get id;
  @override
  String? get nickName;
  @override
  String? get role;
  @override
  String? get avatar;
  @override
  UserGender? get gender;
  @override
  String? get profileMessage;
  @override
  DateTime? get birthday;
  @override
  bool? get banned;
  @override
  DeleteStatus? get deleteStatus;
  @override
  @JsonKey(ignore: true)
  _$$ProfileModelImplCopyWith<_$ProfileModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
