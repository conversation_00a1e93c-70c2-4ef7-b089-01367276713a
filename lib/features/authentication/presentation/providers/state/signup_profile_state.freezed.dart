// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'signup_profile_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SignupProfileState {
  bool get isLoading => throw _privateConstructorUsedError;
  SignupProfileModel? get signupProfileModel =>
      throw _privateConstructorUsedError;
  AppException? get error => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $SignupProfileStateCopyWith<SignupProfileState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SignupProfileStateCopyWith<$Res> {
  factory $SignupProfileStateCopyWith(
          SignupProfileState value, $Res Function(SignupProfileState) then) =
      _$SignupProfileStateCopyWithImpl<$Res, SignupProfileState>;
  @useResult
  $Res call(
      {bool isLoading,
      SignupProfileModel? signupProfileModel,
      AppException? error});
}

/// @nodoc
class _$SignupProfileStateCopyWithImpl<$Res, $Val extends SignupProfileState>
    implements $SignupProfileStateCopyWith<$Res> {
  _$SignupProfileStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? signupProfileModel = freezed,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      signupProfileModel: freezed == signupProfileModel
          ? _value.signupProfileModel
          : signupProfileModel // ignore: cast_nullable_to_non_nullable
              as SignupProfileModel?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppException?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SignupProfileStateImplCopyWith<$Res>
    implements $SignupProfileStateCopyWith<$Res> {
  factory _$$SignupProfileStateImplCopyWith(_$SignupProfileStateImpl value,
          $Res Function(_$SignupProfileStateImpl) then) =
      __$$SignupProfileStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      SignupProfileModel? signupProfileModel,
      AppException? error});
}

/// @nodoc
class __$$SignupProfileStateImplCopyWithImpl<$Res>
    extends _$SignupProfileStateCopyWithImpl<$Res, _$SignupProfileStateImpl>
    implements _$$SignupProfileStateImplCopyWith<$Res> {
  __$$SignupProfileStateImplCopyWithImpl(_$SignupProfileStateImpl _value,
      $Res Function(_$SignupProfileStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? signupProfileModel = freezed,
    Object? error = freezed,
  }) {
    return _then(_$SignupProfileStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      signupProfileModel: freezed == signupProfileModel
          ? _value.signupProfileModel
          : signupProfileModel // ignore: cast_nullable_to_non_nullable
              as SignupProfileModel?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppException?,
    ));
  }
}

/// @nodoc

class _$SignupProfileStateImpl implements _SignupProfileState {
  const _$SignupProfileStateImpl(
      {this.isLoading = false, this.signupProfileModel, this.error});

  @override
  @JsonKey()
  final bool isLoading;
  @override
  final SignupProfileModel? signupProfileModel;
  @override
  final AppException? error;

  @override
  String toString() {
    return 'SignupProfileState(isLoading: $isLoading, signupProfileModel: $signupProfileModel, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SignupProfileStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.signupProfileModel, signupProfileModel) ||
                other.signupProfileModel == signupProfileModel) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, isLoading, signupProfileModel, error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SignupProfileStateImplCopyWith<_$SignupProfileStateImpl> get copyWith =>
      __$$SignupProfileStateImplCopyWithImpl<_$SignupProfileStateImpl>(
          this, _$identity);
}

abstract class _SignupProfileState implements SignupProfileState {
  const factory _SignupProfileState(
      {final bool isLoading,
      final SignupProfileModel? signupProfileModel,
      final AppException? error}) = _$SignupProfileStateImpl;

  @override
  bool get isLoading;
  @override
  SignupProfileModel? get signupProfileModel;
  @override
  AppException? get error;
  @override
  @JsonKey(ignore: true)
  _$$SignupProfileStateImplCopyWith<_$SignupProfileStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
