// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'leaderboard_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$leaderboardHash() => r'5598f2b6d2bb034bdbece5dc01713971cfa5e215';

/// See also [Leaderboard].
@ProviderFor(Leaderboard)
final leaderboardProvider =
    NotifierProvider<Leaderboard, LeaderboardState>.internal(
  Leaderboard.new,
  name: r'leaderboardProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$leaderboardHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Leaderboard = Notifier<LeaderboardState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
