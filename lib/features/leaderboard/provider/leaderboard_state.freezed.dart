// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'leaderboard_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LeaderboardState {
  List<LeaderboardModel> get dayLeaderboard =>
      throw _privateConstructorUsedError;
  List<LeaderboardModel> get weekLeaderboard =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $LeaderboardStateCopyWith<LeaderboardState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LeaderboardStateCopyWith<$Res> {
  factory $LeaderboardStateCopyWith(
          LeaderboardState value, $Res Function(LeaderboardState) then) =
      _$LeaderboardStateCopyWithImpl<$Res, LeaderboardState>;
  @useResult
  $Res call(
      {List<LeaderboardModel> dayLeaderboard,
      List<LeaderboardModel> weekLeaderboard});
}

/// @nodoc
class _$LeaderboardStateCopyWithImpl<$Res, $Val extends LeaderboardState>
    implements $LeaderboardStateCopyWith<$Res> {
  _$LeaderboardStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dayLeaderboard = null,
    Object? weekLeaderboard = null,
  }) {
    return _then(_value.copyWith(
      dayLeaderboard: null == dayLeaderboard
          ? _value.dayLeaderboard
          : dayLeaderboard // ignore: cast_nullable_to_non_nullable
              as List<LeaderboardModel>,
      weekLeaderboard: null == weekLeaderboard
          ? _value.weekLeaderboard
          : weekLeaderboard // ignore: cast_nullable_to_non_nullable
              as List<LeaderboardModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LeaderboardStateImplCopyWith<$Res>
    implements $LeaderboardStateCopyWith<$Res> {
  factory _$$LeaderboardStateImplCopyWith(_$LeaderboardStateImpl value,
          $Res Function(_$LeaderboardStateImpl) then) =
      __$$LeaderboardStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<LeaderboardModel> dayLeaderboard,
      List<LeaderboardModel> weekLeaderboard});
}

/// @nodoc
class __$$LeaderboardStateImplCopyWithImpl<$Res>
    extends _$LeaderboardStateCopyWithImpl<$Res, _$LeaderboardStateImpl>
    implements _$$LeaderboardStateImplCopyWith<$Res> {
  __$$LeaderboardStateImplCopyWithImpl(_$LeaderboardStateImpl _value,
      $Res Function(_$LeaderboardStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dayLeaderboard = null,
    Object? weekLeaderboard = null,
  }) {
    return _then(_$LeaderboardStateImpl(
      dayLeaderboard: null == dayLeaderboard
          ? _value._dayLeaderboard
          : dayLeaderboard // ignore: cast_nullable_to_non_nullable
              as List<LeaderboardModel>,
      weekLeaderboard: null == weekLeaderboard
          ? _value._weekLeaderboard
          : weekLeaderboard // ignore: cast_nullable_to_non_nullable
              as List<LeaderboardModel>,
    ));
  }
}

/// @nodoc

class _$LeaderboardStateImpl implements _LeaderboardState {
  const _$LeaderboardStateImpl(
      {final List<LeaderboardModel> dayLeaderboard = const [],
      final List<LeaderboardModel> weekLeaderboard = const []})
      : _dayLeaderboard = dayLeaderboard,
        _weekLeaderboard = weekLeaderboard;

  final List<LeaderboardModel> _dayLeaderboard;
  @override
  @JsonKey()
  List<LeaderboardModel> get dayLeaderboard {
    if (_dayLeaderboard is EqualUnmodifiableListView) return _dayLeaderboard;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_dayLeaderboard);
  }

  final List<LeaderboardModel> _weekLeaderboard;
  @override
  @JsonKey()
  List<LeaderboardModel> get weekLeaderboard {
    if (_weekLeaderboard is EqualUnmodifiableListView) return _weekLeaderboard;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_weekLeaderboard);
  }

  @override
  String toString() {
    return 'LeaderboardState(dayLeaderboard: $dayLeaderboard, weekLeaderboard: $weekLeaderboard)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LeaderboardStateImpl &&
            const DeepCollectionEquality()
                .equals(other._dayLeaderboard, _dayLeaderboard) &&
            const DeepCollectionEquality()
                .equals(other._weekLeaderboard, _weekLeaderboard));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_dayLeaderboard),
      const DeepCollectionEquality().hash(_weekLeaderboard));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LeaderboardStateImplCopyWith<_$LeaderboardStateImpl> get copyWith =>
      __$$LeaderboardStateImplCopyWithImpl<_$LeaderboardStateImpl>(
          this, _$identity);
}

abstract class _LeaderboardState implements LeaderboardState {
  const factory _LeaderboardState(
      {final List<LeaderboardModel> dayLeaderboard,
      final List<LeaderboardModel> weekLeaderboard}) = _$LeaderboardStateImpl;

  @override
  List<LeaderboardModel> get dayLeaderboard;
  @override
  List<LeaderboardModel> get weekLeaderboard;
  @override
  @JsonKey(ignore: true)
  _$$LeaderboardStateImplCopyWith<_$LeaderboardStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
