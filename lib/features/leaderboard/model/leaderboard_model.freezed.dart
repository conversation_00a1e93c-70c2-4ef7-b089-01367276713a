// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'leaderboard_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LeaderboardModel _$LeaderboardModelFromJson(Map<String, dynamic> json) {
  return _LeaderboardModel.fromJson(json);
}

/// @nodoc
mixin _$LeaderboardModel {
  @JsonKey(name: 'profileVO')
  ProfileModel? get profile => throw _privateConstructorUsedError;
  @JsonKey(name: 'avatarInfo')
  AvatarFrameModel? get frame => throw _privateConstructorUsedError;
  int get userRank => throw _privateConstructorUsedError;
  int get gemSpent => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LeaderboardModelCopyWith<LeaderboardModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LeaderboardModelCopyWith<$Res> {
  factory $LeaderboardModelCopyWith(
          LeaderboardModel value, $Res Function(LeaderboardModel) then) =
      _$LeaderboardModelCopyWithImpl<$Res, LeaderboardModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'profileVO') ProfileModel? profile,
      @JsonKey(name: 'avatarInfo') AvatarFrameModel? frame,
      int userRank,
      int gemSpent});

  $ProfileModelCopyWith<$Res>? get profile;
  $AvatarFrameModelCopyWith<$Res>? get frame;
}

/// @nodoc
class _$LeaderboardModelCopyWithImpl<$Res, $Val extends LeaderboardModel>
    implements $LeaderboardModelCopyWith<$Res> {
  _$LeaderboardModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profile = freezed,
    Object? frame = freezed,
    Object? userRank = null,
    Object? gemSpent = null,
  }) {
    return _then(_value.copyWith(
      profile: freezed == profile
          ? _value.profile
          : profile // ignore: cast_nullable_to_non_nullable
              as ProfileModel?,
      frame: freezed == frame
          ? _value.frame
          : frame // ignore: cast_nullable_to_non_nullable
              as AvatarFrameModel?,
      userRank: null == userRank
          ? _value.userRank
          : userRank // ignore: cast_nullable_to_non_nullable
              as int,
      gemSpent: null == gemSpent
          ? _value.gemSpent
          : gemSpent // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ProfileModelCopyWith<$Res>? get profile {
    if (_value.profile == null) {
      return null;
    }

    return $ProfileModelCopyWith<$Res>(_value.profile!, (value) {
      return _then(_value.copyWith(profile: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $AvatarFrameModelCopyWith<$Res>? get frame {
    if (_value.frame == null) {
      return null;
    }

    return $AvatarFrameModelCopyWith<$Res>(_value.frame!, (value) {
      return _then(_value.copyWith(frame: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LeaderboardModelImplCopyWith<$Res>
    implements $LeaderboardModelCopyWith<$Res> {
  factory _$$LeaderboardModelImplCopyWith(_$LeaderboardModelImpl value,
          $Res Function(_$LeaderboardModelImpl) then) =
      __$$LeaderboardModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'profileVO') ProfileModel? profile,
      @JsonKey(name: 'avatarInfo') AvatarFrameModel? frame,
      int userRank,
      int gemSpent});

  @override
  $ProfileModelCopyWith<$Res>? get profile;
  @override
  $AvatarFrameModelCopyWith<$Res>? get frame;
}

/// @nodoc
class __$$LeaderboardModelImplCopyWithImpl<$Res>
    extends _$LeaderboardModelCopyWithImpl<$Res, _$LeaderboardModelImpl>
    implements _$$LeaderboardModelImplCopyWith<$Res> {
  __$$LeaderboardModelImplCopyWithImpl(_$LeaderboardModelImpl _value,
      $Res Function(_$LeaderboardModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profile = freezed,
    Object? frame = freezed,
    Object? userRank = null,
    Object? gemSpent = null,
  }) {
    return _then(_$LeaderboardModelImpl(
      profile: freezed == profile
          ? _value.profile
          : profile // ignore: cast_nullable_to_non_nullable
              as ProfileModel?,
      frame: freezed == frame
          ? _value.frame
          : frame // ignore: cast_nullable_to_non_nullable
              as AvatarFrameModel?,
      userRank: null == userRank
          ? _value.userRank
          : userRank // ignore: cast_nullable_to_non_nullable
              as int,
      gemSpent: null == gemSpent
          ? _value.gemSpent
          : gemSpent // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$LeaderboardModelImpl implements _LeaderboardModel {
  const _$LeaderboardModelImpl(
      {@JsonKey(name: 'profileVO') this.profile,
      @JsonKey(name: 'avatarInfo') this.frame,
      this.userRank = 0,
      this.gemSpent = 0});

  factory _$LeaderboardModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$LeaderboardModelImplFromJson(json);

  @override
  @JsonKey(name: 'profileVO')
  final ProfileModel? profile;
  @override
  @JsonKey(name: 'avatarInfo')
  final AvatarFrameModel? frame;
  @override
  @JsonKey()
  final int userRank;
  @override
  @JsonKey()
  final int gemSpent;

  @override
  String toString() {
    return 'LeaderboardModel(profile: $profile, frame: $frame, userRank: $userRank, gemSpent: $gemSpent)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LeaderboardModelImpl &&
            (identical(other.profile, profile) || other.profile == profile) &&
            (identical(other.frame, frame) || other.frame == frame) &&
            (identical(other.userRank, userRank) ||
                other.userRank == userRank) &&
            (identical(other.gemSpent, gemSpent) ||
                other.gemSpent == gemSpent));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, profile, frame, userRank, gemSpent);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LeaderboardModelImplCopyWith<_$LeaderboardModelImpl> get copyWith =>
      __$$LeaderboardModelImplCopyWithImpl<_$LeaderboardModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LeaderboardModelImplToJson(
      this,
    );
  }
}

abstract class _LeaderboardModel implements LeaderboardModel {
  const factory _LeaderboardModel(
      {@JsonKey(name: 'profileVO') final ProfileModel? profile,
      @JsonKey(name: 'avatarInfo') final AvatarFrameModel? frame,
      final int userRank,
      final int gemSpent}) = _$LeaderboardModelImpl;

  factory _LeaderboardModel.fromJson(Map<String, dynamic> json) =
      _$LeaderboardModelImpl.fromJson;

  @override
  @JsonKey(name: 'profileVO')
  ProfileModel? get profile;
  @override
  @JsonKey(name: 'avatarInfo')
  AvatarFrameModel? get frame;
  @override
  int get userRank;
  @override
  int get gemSpent;
  @override
  @JsonKey(ignore: true)
  _$$LeaderboardModelImplCopyWith<_$LeaderboardModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
