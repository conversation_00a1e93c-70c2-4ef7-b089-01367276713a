// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'leaderboard_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LeaderboardModelImpl _$$LeaderboardModelImplFromJson(
        Map<String, dynamic> json) =>
    _$LeaderboardModelImpl(
      profile: json['profileVO'] == null
          ? null
          : ProfileModel.fromJson(json['profileVO'] as Map<String, dynamic>),
      frame: json['avatarInfo'] == null
          ? null
          : AvatarFrameModel.fromJson(
              json['avatarInfo'] as Map<String, dynamic>),
      userRank: (json['userRank'] as num?)?.toInt() ?? 0,
      gemSpent: (json['gemSpent'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$LeaderboardModelImplToJson(
        _$LeaderboardModelImpl instance) =>
    <String, dynamic>{
      'profileVO': instance.profile?.toJson(),
      'avatarInfo': instance.frame?.toJson(),
      'userRank': instance.userRank,
      'gemSpent': instance.gemSpent,
    };
