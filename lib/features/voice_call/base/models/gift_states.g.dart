// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gift_states.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$GiftEffectStateImpl _$$GiftEffectStateImplFromJson(
        Map<String, dynamic> json) =>
    _$GiftEffectStateImpl(
      svgaUrl: json['svgaUrl'] as String?,
      imageUrl: json['imageUrl'] as String?,
      giftType: $enumDecode(_$GiftTypeEnumMap, json['giftType']),
      timestamp: (json['timestamp'] as num).toInt(),
    );

Map<String, dynamic> _$$GiftEffectStateImplToJson(
        _$GiftEffectStateImpl instance) =>
    <String, dynamic>{
      'svgaUrl': instance.svgaUrl,
      'imageUrl': instance.imageUrl,
      'giftType': _$GiftTypeEnumMap[instance.giftType]!,
      'timestamp': instance.timestamp,
    };

const _$GiftTypeEnumMap = {
  GiftType.gift: 'gift',
  GiftType.frame: 'avatar_frame',
};

_$GiftInfoImpl _$$GiftInfoImplFromJson(Map<String, dynamic> json) =>
    _$GiftInfoImpl(
      senderId: json['senderId'] as String?,
      senderName: json['senderName'] as String?,
      recipientIds: (json['recipientIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      giftId: (json['giftId'] as num?)?.toInt(),
      giftName: json['giftName'] as String?,
      timestamp: (json['timestamp'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$GiftInfoImplToJson(_$GiftInfoImpl instance) =>
    <String, dynamic>{
      'senderId': instance.senderId,
      'senderName': instance.senderName,
      'recipientIds': instance.recipientIds,
      'giftId': instance.giftId,
      'giftName': instance.giftName,
      'timestamp': instance.timestamp,
    };

_$FrameInfoImpl _$$FrameInfoImplFromJson(Map<String, dynamic> json) =>
    _$FrameInfoImpl(
      senderId: json['senderId'] as String?,
      senderName: json['senderName'] as String?,
      recipientId: json['recipientId'] as String,
      frameId: (json['frameId'] as num?)?.toInt(),
      frameName: json['frameName'] as String?,
      timestamp: (json['timestamp'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$FrameInfoImplToJson(_$FrameInfoImpl instance) =>
    <String, dynamic>{
      'senderId': instance.senderId,
      'senderName': instance.senderName,
      'recipientId': instance.recipientId,
      'frameId': instance.frameId,
      'frameName': instance.frameName,
      'timestamp': instance.timestamp,
    };
