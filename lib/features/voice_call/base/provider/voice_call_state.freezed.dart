// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'voice_call_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$VoiceCallState {
  String get myUserId => throw _privateConstructorUsedError;
  MatchStatus get matchStatus => throw _privateConstructorUsedError;
  CallStatus get callStatus => throw _privateConstructorUsedError;
  String? get callId => throw _privateConstructorUsedError;
  String? get peerId => throw _privateConstructorUsedError;
  String? get peerNickName => throw _privateConstructorUsedError;
  Map<String, dynamic> get peerAvatar => throw _privateConstructorUsedError;
  bool get isCaller => throw _privateConstructorUsedError;
  RoomMessageGift? get lastGiftInfo => throw _privateConstructorUsedError;
  Duration get duration => throw _privateConstructorUsedError;
  EncryptionState get encryptionState => throw _privateConstructorUsedError;
  Map<String, int> get cryptorStates => throw _privateConstructorUsedError;
  bool get isAudioEncryptEnabled => throw _privateConstructorUsedError;
  bool get isAudioDecryptEnabled => throw _privateConstructorUsedError;
  RTCPeerConnection? get peerConnection => throw _privateConstructorUsedError;
  MediaStream? get localStream => throw _privateConstructorUsedError;
  MediaStream? get remoteStream => throw _privateConstructorUsedError;
  int get reconnectAttempts => throw _privateConstructorUsedError;
  int get maxReconnectAttempts => throw _privateConstructorUsedError;
  bool get isReconnecting => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $VoiceCallStateCopyWith<VoiceCallState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VoiceCallStateCopyWith<$Res> {
  factory $VoiceCallStateCopyWith(
          VoiceCallState value, $Res Function(VoiceCallState) then) =
      _$VoiceCallStateCopyWithImpl<$Res, VoiceCallState>;
  @useResult
  $Res call(
      {String myUserId,
      MatchStatus matchStatus,
      CallStatus callStatus,
      String? callId,
      String? peerId,
      String? peerNickName,
      Map<String, dynamic> peerAvatar,
      bool isCaller,
      RoomMessageGift? lastGiftInfo,
      Duration duration,
      EncryptionState encryptionState,
      Map<String, int> cryptorStates,
      bool isAudioEncryptEnabled,
      bool isAudioDecryptEnabled,
      RTCPeerConnection? peerConnection,
      MediaStream? localStream,
      MediaStream? remoteStream,
      int reconnectAttempts,
      int maxReconnectAttempts,
      bool isReconnecting});

  $RoomMessageGiftCopyWith<$Res>? get lastGiftInfo;
}

/// @nodoc
class _$VoiceCallStateCopyWithImpl<$Res, $Val extends VoiceCallState>
    implements $VoiceCallStateCopyWith<$Res> {
  _$VoiceCallStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? myUserId = null,
    Object? matchStatus = null,
    Object? callStatus = null,
    Object? callId = freezed,
    Object? peerId = freezed,
    Object? peerNickName = freezed,
    Object? peerAvatar = null,
    Object? isCaller = null,
    Object? lastGiftInfo = freezed,
    Object? duration = null,
    Object? encryptionState = null,
    Object? cryptorStates = null,
    Object? isAudioEncryptEnabled = null,
    Object? isAudioDecryptEnabled = null,
    Object? peerConnection = freezed,
    Object? localStream = freezed,
    Object? remoteStream = freezed,
    Object? reconnectAttempts = null,
    Object? maxReconnectAttempts = null,
    Object? isReconnecting = null,
  }) {
    return _then(_value.copyWith(
      myUserId: null == myUserId
          ? _value.myUserId
          : myUserId // ignore: cast_nullable_to_non_nullable
              as String,
      matchStatus: null == matchStatus
          ? _value.matchStatus
          : matchStatus // ignore: cast_nullable_to_non_nullable
              as MatchStatus,
      callStatus: null == callStatus
          ? _value.callStatus
          : callStatus // ignore: cast_nullable_to_non_nullable
              as CallStatus,
      callId: freezed == callId
          ? _value.callId
          : callId // ignore: cast_nullable_to_non_nullable
              as String?,
      peerId: freezed == peerId
          ? _value.peerId
          : peerId // ignore: cast_nullable_to_non_nullable
              as String?,
      peerNickName: freezed == peerNickName
          ? _value.peerNickName
          : peerNickName // ignore: cast_nullable_to_non_nullable
              as String?,
      peerAvatar: null == peerAvatar
          ? _value.peerAvatar
          : peerAvatar // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      isCaller: null == isCaller
          ? _value.isCaller
          : isCaller // ignore: cast_nullable_to_non_nullable
              as bool,
      lastGiftInfo: freezed == lastGiftInfo
          ? _value.lastGiftInfo
          : lastGiftInfo // ignore: cast_nullable_to_non_nullable
              as RoomMessageGift?,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as Duration,
      encryptionState: null == encryptionState
          ? _value.encryptionState
          : encryptionState // ignore: cast_nullable_to_non_nullable
              as EncryptionState,
      cryptorStates: null == cryptorStates
          ? _value.cryptorStates
          : cryptorStates // ignore: cast_nullable_to_non_nullable
              as Map<String, int>,
      isAudioEncryptEnabled: null == isAudioEncryptEnabled
          ? _value.isAudioEncryptEnabled
          : isAudioEncryptEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      isAudioDecryptEnabled: null == isAudioDecryptEnabled
          ? _value.isAudioDecryptEnabled
          : isAudioDecryptEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      peerConnection: freezed == peerConnection
          ? _value.peerConnection
          : peerConnection // ignore: cast_nullable_to_non_nullable
              as RTCPeerConnection?,
      localStream: freezed == localStream
          ? _value.localStream
          : localStream // ignore: cast_nullable_to_non_nullable
              as MediaStream?,
      remoteStream: freezed == remoteStream
          ? _value.remoteStream
          : remoteStream // ignore: cast_nullable_to_non_nullable
              as MediaStream?,
      reconnectAttempts: null == reconnectAttempts
          ? _value.reconnectAttempts
          : reconnectAttempts // ignore: cast_nullable_to_non_nullable
              as int,
      maxReconnectAttempts: null == maxReconnectAttempts
          ? _value.maxReconnectAttempts
          : maxReconnectAttempts // ignore: cast_nullable_to_non_nullable
              as int,
      isReconnecting: null == isReconnecting
          ? _value.isReconnecting
          : isReconnecting // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $RoomMessageGiftCopyWith<$Res>? get lastGiftInfo {
    if (_value.lastGiftInfo == null) {
      return null;
    }

    return $RoomMessageGiftCopyWith<$Res>(_value.lastGiftInfo!, (value) {
      return _then(_value.copyWith(lastGiftInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$VoiceCallStateImplCopyWith<$Res>
    implements $VoiceCallStateCopyWith<$Res> {
  factory _$$VoiceCallStateImplCopyWith(_$VoiceCallStateImpl value,
          $Res Function(_$VoiceCallStateImpl) then) =
      __$$VoiceCallStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String myUserId,
      MatchStatus matchStatus,
      CallStatus callStatus,
      String? callId,
      String? peerId,
      String? peerNickName,
      Map<String, dynamic> peerAvatar,
      bool isCaller,
      RoomMessageGift? lastGiftInfo,
      Duration duration,
      EncryptionState encryptionState,
      Map<String, int> cryptorStates,
      bool isAudioEncryptEnabled,
      bool isAudioDecryptEnabled,
      RTCPeerConnection? peerConnection,
      MediaStream? localStream,
      MediaStream? remoteStream,
      int reconnectAttempts,
      int maxReconnectAttempts,
      bool isReconnecting});

  @override
  $RoomMessageGiftCopyWith<$Res>? get lastGiftInfo;
}

/// @nodoc
class __$$VoiceCallStateImplCopyWithImpl<$Res>
    extends _$VoiceCallStateCopyWithImpl<$Res, _$VoiceCallStateImpl>
    implements _$$VoiceCallStateImplCopyWith<$Res> {
  __$$VoiceCallStateImplCopyWithImpl(
      _$VoiceCallStateImpl _value, $Res Function(_$VoiceCallStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? myUserId = null,
    Object? matchStatus = null,
    Object? callStatus = null,
    Object? callId = freezed,
    Object? peerId = freezed,
    Object? peerNickName = freezed,
    Object? peerAvatar = null,
    Object? isCaller = null,
    Object? lastGiftInfo = freezed,
    Object? duration = null,
    Object? encryptionState = null,
    Object? cryptorStates = null,
    Object? isAudioEncryptEnabled = null,
    Object? isAudioDecryptEnabled = null,
    Object? peerConnection = freezed,
    Object? localStream = freezed,
    Object? remoteStream = freezed,
    Object? reconnectAttempts = null,
    Object? maxReconnectAttempts = null,
    Object? isReconnecting = null,
  }) {
    return _then(_$VoiceCallStateImpl(
      myUserId: null == myUserId
          ? _value.myUserId
          : myUserId // ignore: cast_nullable_to_non_nullable
              as String,
      matchStatus: null == matchStatus
          ? _value.matchStatus
          : matchStatus // ignore: cast_nullable_to_non_nullable
              as MatchStatus,
      callStatus: null == callStatus
          ? _value.callStatus
          : callStatus // ignore: cast_nullable_to_non_nullable
              as CallStatus,
      callId: freezed == callId
          ? _value.callId
          : callId // ignore: cast_nullable_to_non_nullable
              as String?,
      peerId: freezed == peerId
          ? _value.peerId
          : peerId // ignore: cast_nullable_to_non_nullable
              as String?,
      peerNickName: freezed == peerNickName
          ? _value.peerNickName
          : peerNickName // ignore: cast_nullable_to_non_nullable
              as String?,
      peerAvatar: null == peerAvatar
          ? _value._peerAvatar
          : peerAvatar // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      isCaller: null == isCaller
          ? _value.isCaller
          : isCaller // ignore: cast_nullable_to_non_nullable
              as bool,
      lastGiftInfo: freezed == lastGiftInfo
          ? _value.lastGiftInfo
          : lastGiftInfo // ignore: cast_nullable_to_non_nullable
              as RoomMessageGift?,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as Duration,
      encryptionState: null == encryptionState
          ? _value.encryptionState
          : encryptionState // ignore: cast_nullable_to_non_nullable
              as EncryptionState,
      cryptorStates: null == cryptorStates
          ? _value._cryptorStates
          : cryptorStates // ignore: cast_nullable_to_non_nullable
              as Map<String, int>,
      isAudioEncryptEnabled: null == isAudioEncryptEnabled
          ? _value.isAudioEncryptEnabled
          : isAudioEncryptEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      isAudioDecryptEnabled: null == isAudioDecryptEnabled
          ? _value.isAudioDecryptEnabled
          : isAudioDecryptEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      peerConnection: freezed == peerConnection
          ? _value.peerConnection
          : peerConnection // ignore: cast_nullable_to_non_nullable
              as RTCPeerConnection?,
      localStream: freezed == localStream
          ? _value.localStream
          : localStream // ignore: cast_nullable_to_non_nullable
              as MediaStream?,
      remoteStream: freezed == remoteStream
          ? _value.remoteStream
          : remoteStream // ignore: cast_nullable_to_non_nullable
              as MediaStream?,
      reconnectAttempts: null == reconnectAttempts
          ? _value.reconnectAttempts
          : reconnectAttempts // ignore: cast_nullable_to_non_nullable
              as int,
      maxReconnectAttempts: null == maxReconnectAttempts
          ? _value.maxReconnectAttempts
          : maxReconnectAttempts // ignore: cast_nullable_to_non_nullable
              as int,
      isReconnecting: null == isReconnecting
          ? _value.isReconnecting
          : isReconnecting // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$VoiceCallStateImpl extends _VoiceCallState {
  const _$VoiceCallStateImpl(
      {required this.myUserId,
      this.matchStatus = const MatchNotStarted(),
      required this.callStatus,
      this.callId = null,
      this.peerId = null,
      this.peerNickName = null,
      final Map<String, dynamic> peerAvatar = const {},
      this.isCaller = false,
      this.lastGiftInfo,
      this.duration = Duration.zero,
      this.encryptionState = EncryptionState.notInitialized,
      final Map<String, int> cryptorStates = const {},
      this.isAudioEncryptEnabled = false,
      this.isAudioDecryptEnabled = false,
      this.peerConnection,
      this.localStream,
      this.remoteStream,
      this.reconnectAttempts = 0,
      this.maxReconnectAttempts = 3,
      this.isReconnecting = false})
      : _peerAvatar = peerAvatar,
        _cryptorStates = cryptorStates,
        super._();

  @override
  final String myUserId;
  @override
  @JsonKey()
  final MatchStatus matchStatus;
  @override
  final CallStatus callStatus;
  @override
  @JsonKey()
  final String? callId;
  @override
  @JsonKey()
  final String? peerId;
  @override
  @JsonKey()
  final String? peerNickName;
  final Map<String, dynamic> _peerAvatar;
  @override
  @JsonKey()
  Map<String, dynamic> get peerAvatar {
    if (_peerAvatar is EqualUnmodifiableMapView) return _peerAvatar;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_peerAvatar);
  }

  @override
  @JsonKey()
  final bool isCaller;
  @override
  final RoomMessageGift? lastGiftInfo;
  @override
  @JsonKey()
  final Duration duration;
  @override
  @JsonKey()
  final EncryptionState encryptionState;
  final Map<String, int> _cryptorStates;
  @override
  @JsonKey()
  Map<String, int> get cryptorStates {
    if (_cryptorStates is EqualUnmodifiableMapView) return _cryptorStates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_cryptorStates);
  }

  @override
  @JsonKey()
  final bool isAudioEncryptEnabled;
  @override
  @JsonKey()
  final bool isAudioDecryptEnabled;
  @override
  final RTCPeerConnection? peerConnection;
  @override
  final MediaStream? localStream;
  @override
  final MediaStream? remoteStream;
  @override
  @JsonKey()
  final int reconnectAttempts;
  @override
  @JsonKey()
  final int maxReconnectAttempts;
  @override
  @JsonKey()
  final bool isReconnecting;

  @override
  String toString() {
    return 'VoiceCallState(myUserId: $myUserId, matchStatus: $matchStatus, callStatus: $callStatus, callId: $callId, peerId: $peerId, peerNickName: $peerNickName, peerAvatar: $peerAvatar, isCaller: $isCaller, lastGiftInfo: $lastGiftInfo, duration: $duration, encryptionState: $encryptionState, cryptorStates: $cryptorStates, isAudioEncryptEnabled: $isAudioEncryptEnabled, isAudioDecryptEnabled: $isAudioDecryptEnabled, peerConnection: $peerConnection, localStream: $localStream, remoteStream: $remoteStream, reconnectAttempts: $reconnectAttempts, maxReconnectAttempts: $maxReconnectAttempts, isReconnecting: $isReconnecting)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VoiceCallStateImpl &&
            (identical(other.myUserId, myUserId) ||
                other.myUserId == myUserId) &&
            (identical(other.matchStatus, matchStatus) ||
                other.matchStatus == matchStatus) &&
            (identical(other.callStatus, callStatus) ||
                other.callStatus == callStatus) &&
            (identical(other.callId, callId) || other.callId == callId) &&
            (identical(other.peerId, peerId) || other.peerId == peerId) &&
            (identical(other.peerNickName, peerNickName) ||
                other.peerNickName == peerNickName) &&
            const DeepCollectionEquality()
                .equals(other._peerAvatar, _peerAvatar) &&
            (identical(other.isCaller, isCaller) ||
                other.isCaller == isCaller) &&
            (identical(other.lastGiftInfo, lastGiftInfo) ||
                other.lastGiftInfo == lastGiftInfo) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.encryptionState, encryptionState) ||
                other.encryptionState == encryptionState) &&
            const DeepCollectionEquality()
                .equals(other._cryptorStates, _cryptorStates) &&
            (identical(other.isAudioEncryptEnabled, isAudioEncryptEnabled) ||
                other.isAudioEncryptEnabled == isAudioEncryptEnabled) &&
            (identical(other.isAudioDecryptEnabled, isAudioDecryptEnabled) ||
                other.isAudioDecryptEnabled == isAudioDecryptEnabled) &&
            (identical(other.peerConnection, peerConnection) ||
                other.peerConnection == peerConnection) &&
            (identical(other.localStream, localStream) ||
                other.localStream == localStream) &&
            (identical(other.remoteStream, remoteStream) ||
                other.remoteStream == remoteStream) &&
            (identical(other.reconnectAttempts, reconnectAttempts) ||
                other.reconnectAttempts == reconnectAttempts) &&
            (identical(other.maxReconnectAttempts, maxReconnectAttempts) ||
                other.maxReconnectAttempts == maxReconnectAttempts) &&
            (identical(other.isReconnecting, isReconnecting) ||
                other.isReconnecting == isReconnecting));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        myUserId,
        matchStatus,
        callStatus,
        callId,
        peerId,
        peerNickName,
        const DeepCollectionEquality().hash(_peerAvatar),
        isCaller,
        lastGiftInfo,
        duration,
        encryptionState,
        const DeepCollectionEquality().hash(_cryptorStates),
        isAudioEncryptEnabled,
        isAudioDecryptEnabled,
        peerConnection,
        localStream,
        remoteStream,
        reconnectAttempts,
        maxReconnectAttempts,
        isReconnecting
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VoiceCallStateImplCopyWith<_$VoiceCallStateImpl> get copyWith =>
      __$$VoiceCallStateImplCopyWithImpl<_$VoiceCallStateImpl>(
          this, _$identity);
}

abstract class _VoiceCallState extends VoiceCallState {
  const factory _VoiceCallState(
      {required final String myUserId,
      final MatchStatus matchStatus,
      required final CallStatus callStatus,
      final String? callId,
      final String? peerId,
      final String? peerNickName,
      final Map<String, dynamic> peerAvatar,
      final bool isCaller,
      final RoomMessageGift? lastGiftInfo,
      final Duration duration,
      final EncryptionState encryptionState,
      final Map<String, int> cryptorStates,
      final bool isAudioEncryptEnabled,
      final bool isAudioDecryptEnabled,
      final RTCPeerConnection? peerConnection,
      final MediaStream? localStream,
      final MediaStream? remoteStream,
      final int reconnectAttempts,
      final int maxReconnectAttempts,
      final bool isReconnecting}) = _$VoiceCallStateImpl;
  const _VoiceCallState._() : super._();

  @override
  String get myUserId;
  @override
  MatchStatus get matchStatus;
  @override
  CallStatus get callStatus;
  @override
  String? get callId;
  @override
  String? get peerId;
  @override
  String? get peerNickName;
  @override
  Map<String, dynamic> get peerAvatar;
  @override
  bool get isCaller;
  @override
  RoomMessageGift? get lastGiftInfo;
  @override
  Duration get duration;
  @override
  EncryptionState get encryptionState;
  @override
  Map<String, int> get cryptorStates;
  @override
  bool get isAudioEncryptEnabled;
  @override
  bool get isAudioDecryptEnabled;
  @override
  RTCPeerConnection? get peerConnection;
  @override
  MediaStream? get localStream;
  @override
  MediaStream? get remoteStream;
  @override
  int get reconnectAttempts;
  @override
  int get maxReconnectAttempts;
  @override
  bool get isReconnecting;
  @override
  @JsonKey(ignore: true)
  _$$VoiceCallStateImplCopyWith<_$VoiceCallStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
