// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'follow_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FollowState {
  int get followerCount => throw _privateConstructorUsedError;
  int get followingCount => throw _privateConstructorUsedError;
  int get mutualFollowCount => throw _privateConstructorUsedError;
  PaginatedResponse<FollowUserInfoModel>? get followers =>
      throw _privateConstructorUsedError;
  PaginatedResponse<FollowUserInfoModel>? get following =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $FollowStateCopyWith<FollowState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FollowStateCopyWith<$Res> {
  factory $FollowStateCopyWith(
          FollowState value, $Res Function(FollowState) then) =
      _$FollowStateCopyWithImpl<$Res, FollowState>;
  @useResult
  $Res call(
      {int followerCount,
      int followingCount,
      int mutualFollowCount,
      PaginatedResponse<FollowUserInfoModel>? followers,
      PaginatedResponse<FollowUserInfoModel>? following});

  $PaginatedResponseCopyWith<FollowUserInfoModel, $Res>? get followers;
  $PaginatedResponseCopyWith<FollowUserInfoModel, $Res>? get following;
}

/// @nodoc
class _$FollowStateCopyWithImpl<$Res, $Val extends FollowState>
    implements $FollowStateCopyWith<$Res> {
  _$FollowStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? followerCount = null,
    Object? followingCount = null,
    Object? mutualFollowCount = null,
    Object? followers = freezed,
    Object? following = freezed,
  }) {
    return _then(_value.copyWith(
      followerCount: null == followerCount
          ? _value.followerCount
          : followerCount // ignore: cast_nullable_to_non_nullable
              as int,
      followingCount: null == followingCount
          ? _value.followingCount
          : followingCount // ignore: cast_nullable_to_non_nullable
              as int,
      mutualFollowCount: null == mutualFollowCount
          ? _value.mutualFollowCount
          : mutualFollowCount // ignore: cast_nullable_to_non_nullable
              as int,
      followers: freezed == followers
          ? _value.followers
          : followers // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<FollowUserInfoModel>?,
      following: freezed == following
          ? _value.following
          : following // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<FollowUserInfoModel>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginatedResponseCopyWith<FollowUserInfoModel, $Res>? get followers {
    if (_value.followers == null) {
      return null;
    }

    return $PaginatedResponseCopyWith<FollowUserInfoModel, $Res>(
        _value.followers!, (value) {
      return _then(_value.copyWith(followers: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginatedResponseCopyWith<FollowUserInfoModel, $Res>? get following {
    if (_value.following == null) {
      return null;
    }

    return $PaginatedResponseCopyWith<FollowUserInfoModel, $Res>(
        _value.following!, (value) {
      return _then(_value.copyWith(following: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$FollowStateImplCopyWith<$Res>
    implements $FollowStateCopyWith<$Res> {
  factory _$$FollowStateImplCopyWith(
          _$FollowStateImpl value, $Res Function(_$FollowStateImpl) then) =
      __$$FollowStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int followerCount,
      int followingCount,
      int mutualFollowCount,
      PaginatedResponse<FollowUserInfoModel>? followers,
      PaginatedResponse<FollowUserInfoModel>? following});

  @override
  $PaginatedResponseCopyWith<FollowUserInfoModel, $Res>? get followers;
  @override
  $PaginatedResponseCopyWith<FollowUserInfoModel, $Res>? get following;
}

/// @nodoc
class __$$FollowStateImplCopyWithImpl<$Res>
    extends _$FollowStateCopyWithImpl<$Res, _$FollowStateImpl>
    implements _$$FollowStateImplCopyWith<$Res> {
  __$$FollowStateImplCopyWithImpl(
      _$FollowStateImpl _value, $Res Function(_$FollowStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? followerCount = null,
    Object? followingCount = null,
    Object? mutualFollowCount = null,
    Object? followers = freezed,
    Object? following = freezed,
  }) {
    return _then(_$FollowStateImpl(
      followerCount: null == followerCount
          ? _value.followerCount
          : followerCount // ignore: cast_nullable_to_non_nullable
              as int,
      followingCount: null == followingCount
          ? _value.followingCount
          : followingCount // ignore: cast_nullable_to_non_nullable
              as int,
      mutualFollowCount: null == mutualFollowCount
          ? _value.mutualFollowCount
          : mutualFollowCount // ignore: cast_nullable_to_non_nullable
              as int,
      followers: freezed == followers
          ? _value.followers
          : followers // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<FollowUserInfoModel>?,
      following: freezed == following
          ? _value.following
          : following // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<FollowUserInfoModel>?,
    ));
  }
}

/// @nodoc

class _$FollowStateImpl extends _FollowState {
  const _$FollowStateImpl(
      {this.followerCount = 0,
      this.followingCount = 0,
      this.mutualFollowCount = 0,
      this.followers = null,
      this.following = null})
      : super._();

  @override
  @JsonKey()
  final int followerCount;
  @override
  @JsonKey()
  final int followingCount;
  @override
  @JsonKey()
  final int mutualFollowCount;
  @override
  @JsonKey()
  final PaginatedResponse<FollowUserInfoModel>? followers;
  @override
  @JsonKey()
  final PaginatedResponse<FollowUserInfoModel>? following;

  @override
  String toString() {
    return 'FollowState(followerCount: $followerCount, followingCount: $followingCount, mutualFollowCount: $mutualFollowCount, followers: $followers, following: $following)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FollowStateImpl &&
            (identical(other.followerCount, followerCount) ||
                other.followerCount == followerCount) &&
            (identical(other.followingCount, followingCount) ||
                other.followingCount == followingCount) &&
            (identical(other.mutualFollowCount, mutualFollowCount) ||
                other.mutualFollowCount == mutualFollowCount) &&
            (identical(other.followers, followers) ||
                other.followers == followers) &&
            (identical(other.following, following) ||
                other.following == following));
  }

  @override
  int get hashCode => Object.hash(runtimeType, followerCount, followingCount,
      mutualFollowCount, followers, following);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FollowStateImplCopyWith<_$FollowStateImpl> get copyWith =>
      __$$FollowStateImplCopyWithImpl<_$FollowStateImpl>(this, _$identity);
}

abstract class _FollowState extends FollowState {
  const factory _FollowState(
          {final int followerCount,
          final int followingCount,
          final int mutualFollowCount,
          final PaginatedResponse<FollowUserInfoModel>? followers,
          final PaginatedResponse<FollowUserInfoModel>? following}) =
      _$FollowStateImpl;
  const _FollowState._() : super._();

  @override
  int get followerCount;
  @override
  int get followingCount;
  @override
  int get mutualFollowCount;
  @override
  PaginatedResponse<FollowUserInfoModel>? get followers;
  @override
  PaginatedResponse<FollowUserInfoModel>? get following;
  @override
  @JsonKey(ignore: true)
  _$$FollowStateImplCopyWith<_$FollowStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
