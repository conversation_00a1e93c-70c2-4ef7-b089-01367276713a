// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'follow_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$followHash() => r'7e373186822da0e71012424ef3b45facbf846538';

/// See also [Follow].
@ProviderFor(Follow)
final followProvider = NotifierProvider<Follow, FollowState>.internal(
  Follow.new,
  name: r'followProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$followHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Follow = Notifier<FollowState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
