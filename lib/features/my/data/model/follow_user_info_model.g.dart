// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'follow_user_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FollowUserInfoModelImpl _$$FollowUserInfoModelImplFromJson(
        Map<String, dynamic> json) =>
    _$FollowUserInfoModelImpl(
      profile: json['profileVO'] == null
          ? null
          : ProfileModel.fromJson(json['profileVO'] as Map<String, dynamic>),
      frame: json['avatarFrameInfo'] == null
          ? null
          : AvatarFrameModel.fromJson(
              json['avatarFrameInfo'] as Map<String, dynamic>),
      isMutualFollow: json['isMutualFollow'] as bool? ?? false,
      onlineStatus: (json['onlineStatus'] as num?)?.toInt(),
      roomId: json['roomId'] as String?,
    );

Map<String, dynamic> _$$FollowUserInfoModelImplToJson(
        _$FollowUserInfoModelImpl instance) =>
    <String, dynamic>{
      'profileVO': instance.profile?.toJson(),
      'avatarFrameInfo': instance.frame?.toJson(),
      'isMutualFollow': instance.isMutualFollow,
      'onlineStatus': instance.onlineStatus,
      'roomId': instance.roomId,
    };
