// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'follow_user_info_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

FollowUserInfoModel _$FollowUserInfoModelFromJson(Map<String, dynamic> json) {
  return _FollowUserInfoModel.fromJson(json);
}

/// @nodoc
mixin _$FollowUserInfoModel {
  @JsonKey(name: 'profileVO')
  ProfileModel? get profile => throw _privateConstructorUsedError;
  @JsonKey(name: 'avatarFrameInfo')
  AvatarFrameModel? get frame => throw _privateConstructorUsedError;
  @JsonKey(name: 'isMutualFollow', defaultValue: false)
  bool get isMutualFollow => throw _privateConstructorUsedError;

  /// Online status: 1 = online, 2 = offline
  int? get onlineStatus => throw _privateConstructorUsedError;

  /// Room ID if user is in a live room
  String? get roomId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FollowUserInfoModelCopyWith<FollowUserInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FollowUserInfoModelCopyWith<$Res> {
  factory $FollowUserInfoModelCopyWith(
          FollowUserInfoModel value, $Res Function(FollowUserInfoModel) then) =
      _$FollowUserInfoModelCopyWithImpl<$Res, FollowUserInfoModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'profileVO') ProfileModel? profile,
      @JsonKey(name: 'avatarFrameInfo') AvatarFrameModel? frame,
      @JsonKey(name: 'isMutualFollow', defaultValue: false) bool isMutualFollow,
      int? onlineStatus,
      String? roomId});

  $ProfileModelCopyWith<$Res>? get profile;
  $AvatarFrameModelCopyWith<$Res>? get frame;
}

/// @nodoc
class _$FollowUserInfoModelCopyWithImpl<$Res, $Val extends FollowUserInfoModel>
    implements $FollowUserInfoModelCopyWith<$Res> {
  _$FollowUserInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profile = freezed,
    Object? frame = freezed,
    Object? isMutualFollow = null,
    Object? onlineStatus = freezed,
    Object? roomId = freezed,
  }) {
    return _then(_value.copyWith(
      profile: freezed == profile
          ? _value.profile
          : profile // ignore: cast_nullable_to_non_nullable
              as ProfileModel?,
      frame: freezed == frame
          ? _value.frame
          : frame // ignore: cast_nullable_to_non_nullable
              as AvatarFrameModel?,
      isMutualFollow: null == isMutualFollow
          ? _value.isMutualFollow
          : isMutualFollow // ignore: cast_nullable_to_non_nullable
              as bool,
      onlineStatus: freezed == onlineStatus
          ? _value.onlineStatus
          : onlineStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      roomId: freezed == roomId
          ? _value.roomId
          : roomId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ProfileModelCopyWith<$Res>? get profile {
    if (_value.profile == null) {
      return null;
    }

    return $ProfileModelCopyWith<$Res>(_value.profile!, (value) {
      return _then(_value.copyWith(profile: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $AvatarFrameModelCopyWith<$Res>? get frame {
    if (_value.frame == null) {
      return null;
    }

    return $AvatarFrameModelCopyWith<$Res>(_value.frame!, (value) {
      return _then(_value.copyWith(frame: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$FollowUserInfoModelImplCopyWith<$Res>
    implements $FollowUserInfoModelCopyWith<$Res> {
  factory _$$FollowUserInfoModelImplCopyWith(_$FollowUserInfoModelImpl value,
          $Res Function(_$FollowUserInfoModelImpl) then) =
      __$$FollowUserInfoModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'profileVO') ProfileModel? profile,
      @JsonKey(name: 'avatarFrameInfo') AvatarFrameModel? frame,
      @JsonKey(name: 'isMutualFollow', defaultValue: false) bool isMutualFollow,
      int? onlineStatus,
      String? roomId});

  @override
  $ProfileModelCopyWith<$Res>? get profile;
  @override
  $AvatarFrameModelCopyWith<$Res>? get frame;
}

/// @nodoc
class __$$FollowUserInfoModelImplCopyWithImpl<$Res>
    extends _$FollowUserInfoModelCopyWithImpl<$Res, _$FollowUserInfoModelImpl>
    implements _$$FollowUserInfoModelImplCopyWith<$Res> {
  __$$FollowUserInfoModelImplCopyWithImpl(_$FollowUserInfoModelImpl _value,
      $Res Function(_$FollowUserInfoModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profile = freezed,
    Object? frame = freezed,
    Object? isMutualFollow = null,
    Object? onlineStatus = freezed,
    Object? roomId = freezed,
  }) {
    return _then(_$FollowUserInfoModelImpl(
      profile: freezed == profile
          ? _value.profile
          : profile // ignore: cast_nullable_to_non_nullable
              as ProfileModel?,
      frame: freezed == frame
          ? _value.frame
          : frame // ignore: cast_nullable_to_non_nullable
              as AvatarFrameModel?,
      isMutualFollow: null == isMutualFollow
          ? _value.isMutualFollow
          : isMutualFollow // ignore: cast_nullable_to_non_nullable
              as bool,
      onlineStatus: freezed == onlineStatus
          ? _value.onlineStatus
          : onlineStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      roomId: freezed == roomId
          ? _value.roomId
          : roomId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$FollowUserInfoModelImpl extends _FollowUserInfoModel {
  const _$FollowUserInfoModelImpl(
      {@JsonKey(name: 'profileVO') this.profile,
      @JsonKey(name: 'avatarFrameInfo') this.frame,
      @JsonKey(name: 'isMutualFollow', defaultValue: false)
      this.isMutualFollow = false,
      this.onlineStatus,
      this.roomId})
      : super._();

  factory _$FollowUserInfoModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$FollowUserInfoModelImplFromJson(json);

  @override
  @JsonKey(name: 'profileVO')
  final ProfileModel? profile;
  @override
  @JsonKey(name: 'avatarFrameInfo')
  final AvatarFrameModel? frame;
  @override
  @JsonKey(name: 'isMutualFollow', defaultValue: false)
  final bool isMutualFollow;

  /// Online status: 1 = online, 2 = offline
  @override
  final int? onlineStatus;

  /// Room ID if user is in a live room
  @override
  final String? roomId;

  @override
  String toString() {
    return 'FollowUserInfoModel(profile: $profile, frame: $frame, isMutualFollow: $isMutualFollow, onlineStatus: $onlineStatus, roomId: $roomId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FollowUserInfoModelImpl &&
            (identical(other.profile, profile) || other.profile == profile) &&
            (identical(other.frame, frame) || other.frame == frame) &&
            (identical(other.isMutualFollow, isMutualFollow) ||
                other.isMutualFollow == isMutualFollow) &&
            (identical(other.onlineStatus, onlineStatus) ||
                other.onlineStatus == onlineStatus) &&
            (identical(other.roomId, roomId) || other.roomId == roomId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, profile, frame, isMutualFollow, onlineStatus, roomId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FollowUserInfoModelImplCopyWith<_$FollowUserInfoModelImpl> get copyWith =>
      __$$FollowUserInfoModelImplCopyWithImpl<_$FollowUserInfoModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FollowUserInfoModelImplToJson(
      this,
    );
  }
}

abstract class _FollowUserInfoModel extends FollowUserInfoModel {
  const factory _FollowUserInfoModel(
      {@JsonKey(name: 'profileVO') final ProfileModel? profile,
      @JsonKey(name: 'avatarFrameInfo') final AvatarFrameModel? frame,
      @JsonKey(name: 'isMutualFollow', defaultValue: false)
      final bool isMutualFollow,
      final int? onlineStatus,
      final String? roomId}) = _$FollowUserInfoModelImpl;
  const _FollowUserInfoModel._() : super._();

  factory _FollowUserInfoModel.fromJson(Map<String, dynamic> json) =
      _$FollowUserInfoModelImpl.fromJson;

  @override
  @JsonKey(name: 'profileVO')
  ProfileModel? get profile;
  @override
  @JsonKey(name: 'avatarFrameInfo')
  AvatarFrameModel? get frame;
  @override
  @JsonKey(name: 'isMutualFollow', defaultValue: false)
  bool get isMutualFollow;
  @override

  /// Online status: 1 = online, 2 = offline
  int? get onlineStatus;
  @override

  /// Room ID if user is in a live room
  String? get roomId;
  @override
  @JsonKey(ignore: true)
  _$$FollowUserInfoModelImplCopyWith<_$FollowUserInfoModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
