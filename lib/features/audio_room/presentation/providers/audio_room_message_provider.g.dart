// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'audio_room_message_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$audioRoomMessageHash() => r'76e03a32ddfd1b3e7bf524c9363cebc2dbcc9ac5';

/// See also [AudioRoomMessage].
@ProviderFor(AudioRoomMessage)
final audioRoomMessageProvider =
    NotifierProvider<AudioRoomMessage, List<RoomMessageModel>>.internal(
  AudioRoomMessage.new,
  name: r'audioRoomMessageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$audioRoomMessageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AudioRoomMessage = Notifier<List<RoomMessageModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
