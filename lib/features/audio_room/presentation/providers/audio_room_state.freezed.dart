// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'audio_room_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AudioRoomState {
// {seat: uid}
  Map<int, int> get users => throw _privateConstructorUsedError;
  bool get isConnected => throw _privateConstructorUsedError;
  RoomInfo? get currentRoom => throw _privateConstructorUsedError;
  RoomUser? get currentUser => throw _privateConstructorUsedError;
  RoomUser? get creator => throw _privateConstructorUsedError;
  RoomUser? get manager => throw _privateConstructorUsedError;
  int? get currentUid => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  List<MetadataItem> get metadata => throw _privateConstructorUsedError;
  Map<int, RoomUser> get members => throw _privateConstructorUsedError;
  int? get lastRequestSeatTime =>
      throw _privateConstructorUsedError; // leaderboard
  PaginatedResponse<LeaderBoardInfo> get receivedLeaderBoard =>
      throw _privateConstructorUsedError;
  PaginatedResponse<LeaderBoardInfo> get sentLeaderBoard =>
      throw _privateConstructorUsedError;
  EncryptionConfig? get encryptionConfig => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $AudioRoomStateCopyWith<AudioRoomState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AudioRoomStateCopyWith<$Res> {
  factory $AudioRoomStateCopyWith(
          AudioRoomState value, $Res Function(AudioRoomState) then) =
      _$AudioRoomStateCopyWithImpl<$Res, AudioRoomState>;
  @useResult
  $Res call(
      {Map<int, int> users,
      bool isConnected,
      RoomInfo? currentRoom,
      RoomUser? currentUser,
      RoomUser? creator,
      RoomUser? manager,
      int? currentUid,
      String? error,
      List<MetadataItem> metadata,
      Map<int, RoomUser> members,
      int? lastRequestSeatTime,
      PaginatedResponse<LeaderBoardInfo> receivedLeaderBoard,
      PaginatedResponse<LeaderBoardInfo> sentLeaderBoard,
      EncryptionConfig? encryptionConfig});

  $RoomInfoCopyWith<$Res>? get currentRoom;
  $RoomUserCopyWith<$Res>? get currentUser;
  $RoomUserCopyWith<$Res>? get creator;
  $RoomUserCopyWith<$Res>? get manager;
  $PaginatedResponseCopyWith<LeaderBoardInfo, $Res> get receivedLeaderBoard;
  $PaginatedResponseCopyWith<LeaderBoardInfo, $Res> get sentLeaderBoard;
}

/// @nodoc
class _$AudioRoomStateCopyWithImpl<$Res, $Val extends AudioRoomState>
    implements $AudioRoomStateCopyWith<$Res> {
  _$AudioRoomStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? users = null,
    Object? isConnected = null,
    Object? currentRoom = freezed,
    Object? currentUser = freezed,
    Object? creator = freezed,
    Object? manager = freezed,
    Object? currentUid = freezed,
    Object? error = freezed,
    Object? metadata = null,
    Object? members = null,
    Object? lastRequestSeatTime = freezed,
    Object? receivedLeaderBoard = null,
    Object? sentLeaderBoard = null,
    Object? encryptionConfig = freezed,
  }) {
    return _then(_value.copyWith(
      users: null == users
          ? _value.users
          : users // ignore: cast_nullable_to_non_nullable
              as Map<int, int>,
      isConnected: null == isConnected
          ? _value.isConnected
          : isConnected // ignore: cast_nullable_to_non_nullable
              as bool,
      currentRoom: freezed == currentRoom
          ? _value.currentRoom
          : currentRoom // ignore: cast_nullable_to_non_nullable
              as RoomInfo?,
      currentUser: freezed == currentUser
          ? _value.currentUser
          : currentUser // ignore: cast_nullable_to_non_nullable
              as RoomUser?,
      creator: freezed == creator
          ? _value.creator
          : creator // ignore: cast_nullable_to_non_nullable
              as RoomUser?,
      manager: freezed == manager
          ? _value.manager
          : manager // ignore: cast_nullable_to_non_nullable
              as RoomUser?,
      currentUid: freezed == currentUid
          ? _value.currentUid
          : currentUid // ignore: cast_nullable_to_non_nullable
              as int?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      metadata: null == metadata
          ? _value.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as List<MetadataItem>,
      members: null == members
          ? _value.members
          : members // ignore: cast_nullable_to_non_nullable
              as Map<int, RoomUser>,
      lastRequestSeatTime: freezed == lastRequestSeatTime
          ? _value.lastRequestSeatTime
          : lastRequestSeatTime // ignore: cast_nullable_to_non_nullable
              as int?,
      receivedLeaderBoard: null == receivedLeaderBoard
          ? _value.receivedLeaderBoard
          : receivedLeaderBoard // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<LeaderBoardInfo>,
      sentLeaderBoard: null == sentLeaderBoard
          ? _value.sentLeaderBoard
          : sentLeaderBoard // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<LeaderBoardInfo>,
      encryptionConfig: freezed == encryptionConfig
          ? _value.encryptionConfig
          : encryptionConfig // ignore: cast_nullable_to_non_nullable
              as EncryptionConfig?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $RoomInfoCopyWith<$Res>? get currentRoom {
    if (_value.currentRoom == null) {
      return null;
    }

    return $RoomInfoCopyWith<$Res>(_value.currentRoom!, (value) {
      return _then(_value.copyWith(currentRoom: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $RoomUserCopyWith<$Res>? get currentUser {
    if (_value.currentUser == null) {
      return null;
    }

    return $RoomUserCopyWith<$Res>(_value.currentUser!, (value) {
      return _then(_value.copyWith(currentUser: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $RoomUserCopyWith<$Res>? get creator {
    if (_value.creator == null) {
      return null;
    }

    return $RoomUserCopyWith<$Res>(_value.creator!, (value) {
      return _then(_value.copyWith(creator: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $RoomUserCopyWith<$Res>? get manager {
    if (_value.manager == null) {
      return null;
    }

    return $RoomUserCopyWith<$Res>(_value.manager!, (value) {
      return _then(_value.copyWith(manager: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginatedResponseCopyWith<LeaderBoardInfo, $Res> get receivedLeaderBoard {
    return $PaginatedResponseCopyWith<LeaderBoardInfo, $Res>(
        _value.receivedLeaderBoard, (value) {
      return _then(_value.copyWith(receivedLeaderBoard: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginatedResponseCopyWith<LeaderBoardInfo, $Res> get sentLeaderBoard {
    return $PaginatedResponseCopyWith<LeaderBoardInfo, $Res>(
        _value.sentLeaderBoard, (value) {
      return _then(_value.copyWith(sentLeaderBoard: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AudioRoomStateImplCopyWith<$Res>
    implements $AudioRoomStateCopyWith<$Res> {
  factory _$$AudioRoomStateImplCopyWith(_$AudioRoomStateImpl value,
          $Res Function(_$AudioRoomStateImpl) then) =
      __$$AudioRoomStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Map<int, int> users,
      bool isConnected,
      RoomInfo? currentRoom,
      RoomUser? currentUser,
      RoomUser? creator,
      RoomUser? manager,
      int? currentUid,
      String? error,
      List<MetadataItem> metadata,
      Map<int, RoomUser> members,
      int? lastRequestSeatTime,
      PaginatedResponse<LeaderBoardInfo> receivedLeaderBoard,
      PaginatedResponse<LeaderBoardInfo> sentLeaderBoard,
      EncryptionConfig? encryptionConfig});

  @override
  $RoomInfoCopyWith<$Res>? get currentRoom;
  @override
  $RoomUserCopyWith<$Res>? get currentUser;
  @override
  $RoomUserCopyWith<$Res>? get creator;
  @override
  $RoomUserCopyWith<$Res>? get manager;
  @override
  $PaginatedResponseCopyWith<LeaderBoardInfo, $Res> get receivedLeaderBoard;
  @override
  $PaginatedResponseCopyWith<LeaderBoardInfo, $Res> get sentLeaderBoard;
}

/// @nodoc
class __$$AudioRoomStateImplCopyWithImpl<$Res>
    extends _$AudioRoomStateCopyWithImpl<$Res, _$AudioRoomStateImpl>
    implements _$$AudioRoomStateImplCopyWith<$Res> {
  __$$AudioRoomStateImplCopyWithImpl(
      _$AudioRoomStateImpl _value, $Res Function(_$AudioRoomStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? users = null,
    Object? isConnected = null,
    Object? currentRoom = freezed,
    Object? currentUser = freezed,
    Object? creator = freezed,
    Object? manager = freezed,
    Object? currentUid = freezed,
    Object? error = freezed,
    Object? metadata = null,
    Object? members = null,
    Object? lastRequestSeatTime = freezed,
    Object? receivedLeaderBoard = null,
    Object? sentLeaderBoard = null,
    Object? encryptionConfig = freezed,
  }) {
    return _then(_$AudioRoomStateImpl(
      users: null == users
          ? _value._users
          : users // ignore: cast_nullable_to_non_nullable
              as Map<int, int>,
      isConnected: null == isConnected
          ? _value.isConnected
          : isConnected // ignore: cast_nullable_to_non_nullable
              as bool,
      currentRoom: freezed == currentRoom
          ? _value.currentRoom
          : currentRoom // ignore: cast_nullable_to_non_nullable
              as RoomInfo?,
      currentUser: freezed == currentUser
          ? _value.currentUser
          : currentUser // ignore: cast_nullable_to_non_nullable
              as RoomUser?,
      creator: freezed == creator
          ? _value.creator
          : creator // ignore: cast_nullable_to_non_nullable
              as RoomUser?,
      manager: freezed == manager
          ? _value.manager
          : manager // ignore: cast_nullable_to_non_nullable
              as RoomUser?,
      currentUid: freezed == currentUid
          ? _value.currentUid
          : currentUid // ignore: cast_nullable_to_non_nullable
              as int?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      metadata: null == metadata
          ? _value._metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as List<MetadataItem>,
      members: null == members
          ? _value._members
          : members // ignore: cast_nullable_to_non_nullable
              as Map<int, RoomUser>,
      lastRequestSeatTime: freezed == lastRequestSeatTime
          ? _value.lastRequestSeatTime
          : lastRequestSeatTime // ignore: cast_nullable_to_non_nullable
              as int?,
      receivedLeaderBoard: null == receivedLeaderBoard
          ? _value.receivedLeaderBoard
          : receivedLeaderBoard // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<LeaderBoardInfo>,
      sentLeaderBoard: null == sentLeaderBoard
          ? _value.sentLeaderBoard
          : sentLeaderBoard // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<LeaderBoardInfo>,
      encryptionConfig: freezed == encryptionConfig
          ? _value.encryptionConfig
          : encryptionConfig // ignore: cast_nullable_to_non_nullable
              as EncryptionConfig?,
    ));
  }
}

/// @nodoc

class _$AudioRoomStateImpl extends _AudioRoomState {
  const _$AudioRoomStateImpl(
      {final Map<int, int> users = const {},
      this.isConnected = false,
      this.currentRoom,
      this.currentUser,
      this.creator,
      this.manager,
      this.currentUid,
      this.error,
      final List<MetadataItem> metadata = const [],
      final Map<int, RoomUser> members = const {},
      this.lastRequestSeatTime,
      this.receivedLeaderBoard = const PaginatedResponse<LeaderBoardInfo>(),
      this.sentLeaderBoard = const PaginatedResponse<LeaderBoardInfo>(),
      this.encryptionConfig})
      : _users = users,
        _metadata = metadata,
        _members = members,
        super._();

// {seat: uid}
  final Map<int, int> _users;
// {seat: uid}
  @override
  @JsonKey()
  Map<int, int> get users {
    if (_users is EqualUnmodifiableMapView) return _users;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_users);
  }

  @override
  @JsonKey()
  final bool isConnected;
  @override
  final RoomInfo? currentRoom;
  @override
  final RoomUser? currentUser;
  @override
  final RoomUser? creator;
  @override
  final RoomUser? manager;
  @override
  final int? currentUid;
  @override
  final String? error;
  final List<MetadataItem> _metadata;
  @override
  @JsonKey()
  List<MetadataItem> get metadata {
    if (_metadata is EqualUnmodifiableListView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_metadata);
  }

  final Map<int, RoomUser> _members;
  @override
  @JsonKey()
  Map<int, RoomUser> get members {
    if (_members is EqualUnmodifiableMapView) return _members;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_members);
  }

  @override
  final int? lastRequestSeatTime;
// leaderboard
  @override
  @JsonKey()
  final PaginatedResponse<LeaderBoardInfo> receivedLeaderBoard;
  @override
  @JsonKey()
  final PaginatedResponse<LeaderBoardInfo> sentLeaderBoard;
  @override
  final EncryptionConfig? encryptionConfig;

  @override
  String toString() {
    return 'AudioRoomState(users: $users, isConnected: $isConnected, currentRoom: $currentRoom, currentUser: $currentUser, creator: $creator, manager: $manager, currentUid: $currentUid, error: $error, metadata: $metadata, members: $members, lastRequestSeatTime: $lastRequestSeatTime, receivedLeaderBoard: $receivedLeaderBoard, sentLeaderBoard: $sentLeaderBoard, encryptionConfig: $encryptionConfig)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AudioRoomStateImpl &&
            const DeepCollectionEquality().equals(other._users, _users) &&
            (identical(other.isConnected, isConnected) ||
                other.isConnected == isConnected) &&
            (identical(other.currentRoom, currentRoom) ||
                other.currentRoom == currentRoom) &&
            (identical(other.currentUser, currentUser) ||
                other.currentUser == currentUser) &&
            (identical(other.creator, creator) || other.creator == creator) &&
            (identical(other.manager, manager) || other.manager == manager) &&
            (identical(other.currentUid, currentUid) ||
                other.currentUid == currentUid) &&
            (identical(other.error, error) || other.error == error) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            const DeepCollectionEquality().equals(other._members, _members) &&
            (identical(other.lastRequestSeatTime, lastRequestSeatTime) ||
                other.lastRequestSeatTime == lastRequestSeatTime) &&
            (identical(other.receivedLeaderBoard, receivedLeaderBoard) ||
                other.receivedLeaderBoard == receivedLeaderBoard) &&
            (identical(other.sentLeaderBoard, sentLeaderBoard) ||
                other.sentLeaderBoard == sentLeaderBoard) &&
            (identical(other.encryptionConfig, encryptionConfig) ||
                other.encryptionConfig == encryptionConfig));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_users),
      isConnected,
      currentRoom,
      currentUser,
      creator,
      manager,
      currentUid,
      error,
      const DeepCollectionEquality().hash(_metadata),
      const DeepCollectionEquality().hash(_members),
      lastRequestSeatTime,
      receivedLeaderBoard,
      sentLeaderBoard,
      encryptionConfig);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AudioRoomStateImplCopyWith<_$AudioRoomStateImpl> get copyWith =>
      __$$AudioRoomStateImplCopyWithImpl<_$AudioRoomStateImpl>(
          this, _$identity);
}

abstract class _AudioRoomState extends AudioRoomState {
  const factory _AudioRoomState(
      {final Map<int, int> users,
      final bool isConnected,
      final RoomInfo? currentRoom,
      final RoomUser? currentUser,
      final RoomUser? creator,
      final RoomUser? manager,
      final int? currentUid,
      final String? error,
      final List<MetadataItem> metadata,
      final Map<int, RoomUser> members,
      final int? lastRequestSeatTime,
      final PaginatedResponse<LeaderBoardInfo> receivedLeaderBoard,
      final PaginatedResponse<LeaderBoardInfo> sentLeaderBoard,
      final EncryptionConfig? encryptionConfig}) = _$AudioRoomStateImpl;
  const _AudioRoomState._() : super._();

  @override // {seat: uid}
  Map<int, int> get users;
  @override
  bool get isConnected;
  @override
  RoomInfo? get currentRoom;
  @override
  RoomUser? get currentUser;
  @override
  RoomUser? get creator;
  @override
  RoomUser? get manager;
  @override
  int? get currentUid;
  @override
  String? get error;
  @override
  List<MetadataItem> get metadata;
  @override
  Map<int, RoomUser> get members;
  @override
  int? get lastRequestSeatTime;
  @override // leaderboard
  PaginatedResponse<LeaderBoardInfo> get receivedLeaderBoard;
  @override
  PaginatedResponse<LeaderBoardInfo> get sentLeaderBoard;
  @override
  EncryptionConfig? get encryptionConfig;
  @override
  @JsonKey(ignore: true)
  _$$AudioRoomStateImplCopyWith<_$AudioRoomStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
