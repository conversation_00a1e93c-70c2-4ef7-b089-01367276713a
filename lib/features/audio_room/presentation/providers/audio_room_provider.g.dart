// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'audio_room_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$audioRoomHash() => r'297b998763d8183e1bd8ba2170a8854ed8bae1a7';

/// See also [AudioRoom].
@ProviderFor(AudioRoom)
final audioRoomProvider = NotifierProvider<AudioRoom, AudioRoomState>.internal(
  AudioRoom.new,
  name: r'audioRoomProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$audioRoomHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AudioRoom = Notifier<AudioRoomState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
