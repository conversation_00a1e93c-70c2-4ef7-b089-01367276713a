// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'lucky_draw_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LuckyDrawState {
  bool get isAnimating => throw _privateConstructorUsedError;
  int get currentHighlightIndex => throw _privateConstructorUsedError;
  int get selectedTab => throw _privateConstructorUsedError;
  int get currentDraws => throw _privateConstructorUsedError;
  List<LotteryType> get tabs => throw _privateConstructorUsedError;
  EconomyLotteryGifts get economyItems => throw _privateConstructorUsedError;
  LuxuryLotteryGifts get luxuryItems => throw _privateConstructorUsedError;
  PaginatedResponse<LuckyDrawItem> get drawHistory =>
      throw _privateConstructorUsedError;
  int get economyDrawCount => throw _privateConstructorUsedError;
  int get luxuryDrawCount => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $LuckyDrawStateCopyWith<LuckyDrawState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LuckyDrawStateCopyWith<$Res> {
  factory $LuckyDrawStateCopyWith(
          LuckyDrawState value, $Res Function(LuckyDrawState) then) =
      _$LuckyDrawStateCopyWithImpl<$Res, LuckyDrawState>;
  @useResult
  $Res call(
      {bool isAnimating,
      int currentHighlightIndex,
      int selectedTab,
      int currentDraws,
      List<LotteryType> tabs,
      EconomyLotteryGifts economyItems,
      LuxuryLotteryGifts luxuryItems,
      PaginatedResponse<LuckyDrawItem> drawHistory,
      int economyDrawCount,
      int luxuryDrawCount});

  $EconomyLotteryGiftsCopyWith<$Res> get economyItems;
  $LuxuryLotteryGiftsCopyWith<$Res> get luxuryItems;
  $PaginatedResponseCopyWith<LuckyDrawItem, $Res> get drawHistory;
}

/// @nodoc
class _$LuckyDrawStateCopyWithImpl<$Res, $Val extends LuckyDrawState>
    implements $LuckyDrawStateCopyWith<$Res> {
  _$LuckyDrawStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isAnimating = null,
    Object? currentHighlightIndex = null,
    Object? selectedTab = null,
    Object? currentDraws = null,
    Object? tabs = null,
    Object? economyItems = null,
    Object? luxuryItems = null,
    Object? drawHistory = null,
    Object? economyDrawCount = null,
    Object? luxuryDrawCount = null,
  }) {
    return _then(_value.copyWith(
      isAnimating: null == isAnimating
          ? _value.isAnimating
          : isAnimating // ignore: cast_nullable_to_non_nullable
              as bool,
      currentHighlightIndex: null == currentHighlightIndex
          ? _value.currentHighlightIndex
          : currentHighlightIndex // ignore: cast_nullable_to_non_nullable
              as int,
      selectedTab: null == selectedTab
          ? _value.selectedTab
          : selectedTab // ignore: cast_nullable_to_non_nullable
              as int,
      currentDraws: null == currentDraws
          ? _value.currentDraws
          : currentDraws // ignore: cast_nullable_to_non_nullable
              as int,
      tabs: null == tabs
          ? _value.tabs
          : tabs // ignore: cast_nullable_to_non_nullable
              as List<LotteryType>,
      economyItems: null == economyItems
          ? _value.economyItems
          : economyItems // ignore: cast_nullable_to_non_nullable
              as EconomyLotteryGifts,
      luxuryItems: null == luxuryItems
          ? _value.luxuryItems
          : luxuryItems // ignore: cast_nullable_to_non_nullable
              as LuxuryLotteryGifts,
      drawHistory: null == drawHistory
          ? _value.drawHistory
          : drawHistory // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<LuckyDrawItem>,
      economyDrawCount: null == economyDrawCount
          ? _value.economyDrawCount
          : economyDrawCount // ignore: cast_nullable_to_non_nullable
              as int,
      luxuryDrawCount: null == luxuryDrawCount
          ? _value.luxuryDrawCount
          : luxuryDrawCount // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $EconomyLotteryGiftsCopyWith<$Res> get economyItems {
    return $EconomyLotteryGiftsCopyWith<$Res>(_value.economyItems, (value) {
      return _then(_value.copyWith(economyItems: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $LuxuryLotteryGiftsCopyWith<$Res> get luxuryItems {
    return $LuxuryLotteryGiftsCopyWith<$Res>(_value.luxuryItems, (value) {
      return _then(_value.copyWith(luxuryItems: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginatedResponseCopyWith<LuckyDrawItem, $Res> get drawHistory {
    return $PaginatedResponseCopyWith<LuckyDrawItem, $Res>(_value.drawHistory,
        (value) {
      return _then(_value.copyWith(drawHistory: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LuckyDrawStateImplCopyWith<$Res>
    implements $LuckyDrawStateCopyWith<$Res> {
  factory _$$LuckyDrawStateImplCopyWith(_$LuckyDrawStateImpl value,
          $Res Function(_$LuckyDrawStateImpl) then) =
      __$$LuckyDrawStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isAnimating,
      int currentHighlightIndex,
      int selectedTab,
      int currentDraws,
      List<LotteryType> tabs,
      EconomyLotteryGifts economyItems,
      LuxuryLotteryGifts luxuryItems,
      PaginatedResponse<LuckyDrawItem> drawHistory,
      int economyDrawCount,
      int luxuryDrawCount});

  @override
  $EconomyLotteryGiftsCopyWith<$Res> get economyItems;
  @override
  $LuxuryLotteryGiftsCopyWith<$Res> get luxuryItems;
  @override
  $PaginatedResponseCopyWith<LuckyDrawItem, $Res> get drawHistory;
}

/// @nodoc
class __$$LuckyDrawStateImplCopyWithImpl<$Res>
    extends _$LuckyDrawStateCopyWithImpl<$Res, _$LuckyDrawStateImpl>
    implements _$$LuckyDrawStateImplCopyWith<$Res> {
  __$$LuckyDrawStateImplCopyWithImpl(
      _$LuckyDrawStateImpl _value, $Res Function(_$LuckyDrawStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isAnimating = null,
    Object? currentHighlightIndex = null,
    Object? selectedTab = null,
    Object? currentDraws = null,
    Object? tabs = null,
    Object? economyItems = null,
    Object? luxuryItems = null,
    Object? drawHistory = null,
    Object? economyDrawCount = null,
    Object? luxuryDrawCount = null,
  }) {
    return _then(_$LuckyDrawStateImpl(
      isAnimating: null == isAnimating
          ? _value.isAnimating
          : isAnimating // ignore: cast_nullable_to_non_nullable
              as bool,
      currentHighlightIndex: null == currentHighlightIndex
          ? _value.currentHighlightIndex
          : currentHighlightIndex // ignore: cast_nullable_to_non_nullable
              as int,
      selectedTab: null == selectedTab
          ? _value.selectedTab
          : selectedTab // ignore: cast_nullable_to_non_nullable
              as int,
      currentDraws: null == currentDraws
          ? _value.currentDraws
          : currentDraws // ignore: cast_nullable_to_non_nullable
              as int,
      tabs: null == tabs
          ? _value._tabs
          : tabs // ignore: cast_nullable_to_non_nullable
              as List<LotteryType>,
      economyItems: null == economyItems
          ? _value.economyItems
          : economyItems // ignore: cast_nullable_to_non_nullable
              as EconomyLotteryGifts,
      luxuryItems: null == luxuryItems
          ? _value.luxuryItems
          : luxuryItems // ignore: cast_nullable_to_non_nullable
              as LuxuryLotteryGifts,
      drawHistory: null == drawHistory
          ? _value.drawHistory
          : drawHistory // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<LuckyDrawItem>,
      economyDrawCount: null == economyDrawCount
          ? _value.economyDrawCount
          : economyDrawCount // ignore: cast_nullable_to_non_nullable
              as int,
      luxuryDrawCount: null == luxuryDrawCount
          ? _value.luxuryDrawCount
          : luxuryDrawCount // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$LuckyDrawStateImpl extends _LuckyDrawState {
  const _$LuckyDrawStateImpl(
      {this.isAnimating = false,
      this.currentHighlightIndex = -1,
      this.selectedTab = 0,
      this.currentDraws = 0,
      final List<LotteryType> tabs = LotteryType.values,
      this.economyItems = const EconomyLotteryGifts(),
      this.luxuryItems = const LuxuryLotteryGifts(),
      this.drawHistory = const PaginatedResponse(),
      this.economyDrawCount = 0,
      this.luxuryDrawCount = 0})
      : _tabs = tabs,
        super._();

  @override
  @JsonKey()
  final bool isAnimating;
  @override
  @JsonKey()
  final int currentHighlightIndex;
  @override
  @JsonKey()
  final int selectedTab;
  @override
  @JsonKey()
  final int currentDraws;
  final List<LotteryType> _tabs;
  @override
  @JsonKey()
  List<LotteryType> get tabs {
    if (_tabs is EqualUnmodifiableListView) return _tabs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tabs);
  }

  @override
  @JsonKey()
  final EconomyLotteryGifts economyItems;
  @override
  @JsonKey()
  final LuxuryLotteryGifts luxuryItems;
  @override
  @JsonKey()
  final PaginatedResponse<LuckyDrawItem> drawHistory;
  @override
  @JsonKey()
  final int economyDrawCount;
  @override
  @JsonKey()
  final int luxuryDrawCount;

  @override
  String toString() {
    return 'LuckyDrawState(isAnimating: $isAnimating, currentHighlightIndex: $currentHighlightIndex, selectedTab: $selectedTab, currentDraws: $currentDraws, tabs: $tabs, economyItems: $economyItems, luxuryItems: $luxuryItems, drawHistory: $drawHistory, economyDrawCount: $economyDrawCount, luxuryDrawCount: $luxuryDrawCount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LuckyDrawStateImpl &&
            (identical(other.isAnimating, isAnimating) ||
                other.isAnimating == isAnimating) &&
            (identical(other.currentHighlightIndex, currentHighlightIndex) ||
                other.currentHighlightIndex == currentHighlightIndex) &&
            (identical(other.selectedTab, selectedTab) ||
                other.selectedTab == selectedTab) &&
            (identical(other.currentDraws, currentDraws) ||
                other.currentDraws == currentDraws) &&
            const DeepCollectionEquality().equals(other._tabs, _tabs) &&
            (identical(other.economyItems, economyItems) ||
                other.economyItems == economyItems) &&
            (identical(other.luxuryItems, luxuryItems) ||
                other.luxuryItems == luxuryItems) &&
            (identical(other.drawHistory, drawHistory) ||
                other.drawHistory == drawHistory) &&
            (identical(other.economyDrawCount, economyDrawCount) ||
                other.economyDrawCount == economyDrawCount) &&
            (identical(other.luxuryDrawCount, luxuryDrawCount) ||
                other.luxuryDrawCount == luxuryDrawCount));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isAnimating,
      currentHighlightIndex,
      selectedTab,
      currentDraws,
      const DeepCollectionEquality().hash(_tabs),
      economyItems,
      luxuryItems,
      drawHistory,
      economyDrawCount,
      luxuryDrawCount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LuckyDrawStateImplCopyWith<_$LuckyDrawStateImpl> get copyWith =>
      __$$LuckyDrawStateImplCopyWithImpl<_$LuckyDrawStateImpl>(
          this, _$identity);
}

abstract class _LuckyDrawState extends LuckyDrawState {
  const factory _LuckyDrawState(
      {final bool isAnimating,
      final int currentHighlightIndex,
      final int selectedTab,
      final int currentDraws,
      final List<LotteryType> tabs,
      final EconomyLotteryGifts economyItems,
      final LuxuryLotteryGifts luxuryItems,
      final PaginatedResponse<LuckyDrawItem> drawHistory,
      final int economyDrawCount,
      final int luxuryDrawCount}) = _$LuckyDrawStateImpl;
  const _LuckyDrawState._() : super._();

  @override
  bool get isAnimating;
  @override
  int get currentHighlightIndex;
  @override
  int get selectedTab;
  @override
  int get currentDraws;
  @override
  List<LotteryType> get tabs;
  @override
  EconomyLotteryGifts get economyItems;
  @override
  LuxuryLotteryGifts get luxuryItems;
  @override
  PaginatedResponse<LuckyDrawItem> get drawHistory;
  @override
  int get economyDrawCount;
  @override
  int get luxuryDrawCount;
  @override
  @JsonKey(ignore: true)
  _$$LuckyDrawStateImplCopyWith<_$LuckyDrawStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
