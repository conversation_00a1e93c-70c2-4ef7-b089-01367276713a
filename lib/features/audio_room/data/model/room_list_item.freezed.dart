// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'room_list_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RoomListItem _$RoomListItemFromJson(Map<String, dynamic> json) {
  return _RoomListItem.fromJson(json);
}

/// @nodoc
mixin _$RoomListItem {
  @JsonKey(name: 'roomVO')
  RoomInfo? get room => throw _privateConstructorUsedError;
  List<ProfileModel> get memberInfos => throw _privateConstructorUsedError;
  int get memberCount => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RoomListItemCopyWith<RoomListItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RoomListItemCopyWith<$Res> {
  factory $RoomListItemCopyWith(
          RoomListItem value, $Res Function(RoomListItem) then) =
      _$RoomListItemCopyWithImpl<$Res, RoomListItem>;
  @useResult
  $Res call(
      {@JsonKey(name: 'roomVO') RoomInfo? room,
      List<ProfileModel> memberInfos,
      int memberCount});

  $RoomInfoCopyWith<$Res>? get room;
}

/// @nodoc
class _$RoomListItemCopyWithImpl<$Res, $Val extends RoomListItem>
    implements $RoomListItemCopyWith<$Res> {
  _$RoomListItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? room = freezed,
    Object? memberInfos = null,
    Object? memberCount = null,
  }) {
    return _then(_value.copyWith(
      room: freezed == room
          ? _value.room
          : room // ignore: cast_nullable_to_non_nullable
              as RoomInfo?,
      memberInfos: null == memberInfos
          ? _value.memberInfos
          : memberInfos // ignore: cast_nullable_to_non_nullable
              as List<ProfileModel>,
      memberCount: null == memberCount
          ? _value.memberCount
          : memberCount // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $RoomInfoCopyWith<$Res>? get room {
    if (_value.room == null) {
      return null;
    }

    return $RoomInfoCopyWith<$Res>(_value.room!, (value) {
      return _then(_value.copyWith(room: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RoomListItemImplCopyWith<$Res>
    implements $RoomListItemCopyWith<$Res> {
  factory _$$RoomListItemImplCopyWith(
          _$RoomListItemImpl value, $Res Function(_$RoomListItemImpl) then) =
      __$$RoomListItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'roomVO') RoomInfo? room,
      List<ProfileModel> memberInfos,
      int memberCount});

  @override
  $RoomInfoCopyWith<$Res>? get room;
}

/// @nodoc
class __$$RoomListItemImplCopyWithImpl<$Res>
    extends _$RoomListItemCopyWithImpl<$Res, _$RoomListItemImpl>
    implements _$$RoomListItemImplCopyWith<$Res> {
  __$$RoomListItemImplCopyWithImpl(
      _$RoomListItemImpl _value, $Res Function(_$RoomListItemImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? room = freezed,
    Object? memberInfos = null,
    Object? memberCount = null,
  }) {
    return _then(_$RoomListItemImpl(
      room: freezed == room
          ? _value.room
          : room // ignore: cast_nullable_to_non_nullable
              as RoomInfo?,
      memberInfos: null == memberInfos
          ? _value._memberInfos
          : memberInfos // ignore: cast_nullable_to_non_nullable
              as List<ProfileModel>,
      memberCount: null == memberCount
          ? _value.memberCount
          : memberCount // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$RoomListItemImpl implements _RoomListItem {
  const _$RoomListItemImpl(
      {@JsonKey(name: 'roomVO') this.room,
      final List<ProfileModel> memberInfos = const [],
      this.memberCount = 0})
      : _memberInfos = memberInfos;

  factory _$RoomListItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$RoomListItemImplFromJson(json);

  @override
  @JsonKey(name: 'roomVO')
  final RoomInfo? room;
  final List<ProfileModel> _memberInfos;
  @override
  @JsonKey()
  List<ProfileModel> get memberInfos {
    if (_memberInfos is EqualUnmodifiableListView) return _memberInfos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_memberInfos);
  }

  @override
  @JsonKey()
  final int memberCount;

  @override
  String toString() {
    return 'RoomListItem(room: $room, memberInfos: $memberInfos, memberCount: $memberCount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RoomListItemImpl &&
            (identical(other.room, room) || other.room == room) &&
            const DeepCollectionEquality()
                .equals(other._memberInfos, _memberInfos) &&
            (identical(other.memberCount, memberCount) ||
                other.memberCount == memberCount));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, room,
      const DeepCollectionEquality().hash(_memberInfos), memberCount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RoomListItemImplCopyWith<_$RoomListItemImpl> get copyWith =>
      __$$RoomListItemImplCopyWithImpl<_$RoomListItemImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RoomListItemImplToJson(
      this,
    );
  }
}

abstract class _RoomListItem implements RoomListItem {
  const factory _RoomListItem(
      {@JsonKey(name: 'roomVO') final RoomInfo? room,
      final List<ProfileModel> memberInfos,
      final int memberCount}) = _$RoomListItemImpl;

  factory _RoomListItem.fromJson(Map<String, dynamic> json) =
      _$RoomListItemImpl.fromJson;

  @override
  @JsonKey(name: 'roomVO')
  RoomInfo? get room;
  @override
  List<ProfileModel> get memberInfos;
  @override
  int get memberCount;
  @override
  @JsonKey(ignore: true)
  _$$RoomListItemImplCopyWith<_$RoomListItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
