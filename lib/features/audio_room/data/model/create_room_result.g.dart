// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_room_result.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CreateRoomResultImpl _$$CreateRoomResultImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateRoomResultImpl(
      roomInfo: json['roomVO'] == null
          ? null
          : RoomInfo.fromJson(json['roomVO'] as Map<String, dynamic>),
      roomAccessInfo: json['accessInfo'] == null
          ? null
          : RoomAccessInfo.fromJson(json['accessInfo'] as Map<String, dynamic>),
      punishment: json['punishmentVO'] == null
          ? null
          : PunishmentModel.fromJson(
              json['punishmentVO'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CreateRoomResultImplToJson(
        _$CreateRoomResultImpl instance) =>
    <String, dynamic>{
      'roomVO': instance.roomInfo?.toJson(),
      'accessInfo': instance.roomAccessInfo?.toJson(),
      'punishmentVO': instance.punishment?.toJson(),
    };
