// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'room_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RoomInfoImpl _$$RoomInfoImplFromJson(Map<String, dynamic> json) =>
    _$RoomInfoImpl(
      id: json['id'] as String?,
      creatorId: json['creatorId'] as String?,
      searchId: json['searchId'] as String?,
      title: json['title'] as String?,
      category: json['category'] == null
          ? null
          : RoomCategory.fromJson(json['category'] as String),
      announcement: json['announcement'] as String?,
      background: json['background'] as String?,
      type: json['type'] == null
          ? null
          : RoomType.fromJson(json['type'] as String),
      videoVendor: json['videoVendor'] as String?,
      isPlayingVideo: json['isPlayingVideo'] as bool?,
      ageSegment: json['ageSegment'] as String?,
      locationBucket: json['locationBucket'] as String?,
      expireTime: json['expireTime'] == null
          ? null
          : DateTime.parse(json['expireTime'] as String),
      createTime: json['createTime'] == null
          ? null
          : DateTime.parse(json['createTime'] as String),
      updateTime: json['updateTime'] == null
          ? null
          : DateTime.parse(json['updateTime'] as String),
      activityInfo: json['activityInfo'] == null
          ? null
          : RoomActivityInfo.fromJson(
              json['activityInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$RoomInfoImplToJson(_$RoomInfoImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'creatorId': instance.creatorId,
      'searchId': instance.searchId,
      'title': instance.title,
      'category': _$RoomCategoryEnumMap[instance.category],
      'announcement': instance.announcement,
      'background': instance.background,
      'type': _$RoomTypeEnumMap[instance.type],
      'videoVendor': instance.videoVendor,
      'isPlayingVideo': instance.isPlayingVideo,
      'ageSegment': instance.ageSegment,
      'locationBucket': instance.locationBucket,
      'expireTime': instance.expireTime?.toIso8601String(),
      'createTime': instance.createTime?.toIso8601String(),
      'updateTime': instance.updateTime?.toIso8601String(),
      'activityInfo': instance.activityInfo?.toJson(),
    };

const _$RoomCategoryEnumMap = {
  RoomCategory.Interests: 'Interests',
  RoomCategory.Discussion: 'Discussion',
  RoomCategory.Music: 'Music',
  RoomCategory.Gaming: 'Gaming',
  RoomCategory.Motivational: 'Motivational',
  RoomCategory.Casual: 'Casual',
  RoomCategory.Relationships: 'Relationships',
};

const _$RoomTypeEnumMap = {
  RoomType.public: 'public',
  RoomType.private: 'private',
};
