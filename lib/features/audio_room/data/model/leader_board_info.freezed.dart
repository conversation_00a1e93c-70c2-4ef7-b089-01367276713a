// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'leader_board_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LeaderBoardInfo _$LeaderBoardInfoFromJson(Map<String, dynamic> json) {
  return _LeaderBoardInfo.fromJson(json);
}

/// @nodoc
mixin _$LeaderBoardInfo {
  int? get gemValue => throw _privateConstructorUsedError;
  int? get count => throw _privateConstructorUsedError;
  @JsonKey(name: 'profileVO')
  ProfileModel? get profileInfo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LeaderBoardInfoCopyWith<LeaderBoardInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LeaderBoardInfoCopyWith<$Res> {
  factory $LeaderBoardInfoCopyWith(
          LeaderBoardInfo value, $Res Function(LeaderBoardInfo) then) =
      _$LeaderBoardInfoCopyWithImpl<$Res, LeaderBoardInfo>;
  @useResult
  $Res call(
      {int? gemValue,
      int? count,
      @JsonKey(name: 'profileVO') ProfileModel? profileInfo});

  $ProfileModelCopyWith<$Res>? get profileInfo;
}

/// @nodoc
class _$LeaderBoardInfoCopyWithImpl<$Res, $Val extends LeaderBoardInfo>
    implements $LeaderBoardInfoCopyWith<$Res> {
  _$LeaderBoardInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gemValue = freezed,
    Object? count = freezed,
    Object? profileInfo = freezed,
  }) {
    return _then(_value.copyWith(
      gemValue: freezed == gemValue
          ? _value.gemValue
          : gemValue // ignore: cast_nullable_to_non_nullable
              as int?,
      count: freezed == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int?,
      profileInfo: freezed == profileInfo
          ? _value.profileInfo
          : profileInfo // ignore: cast_nullable_to_non_nullable
              as ProfileModel?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ProfileModelCopyWith<$Res>? get profileInfo {
    if (_value.profileInfo == null) {
      return null;
    }

    return $ProfileModelCopyWith<$Res>(_value.profileInfo!, (value) {
      return _then(_value.copyWith(profileInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LeaderBoardInfoImplCopyWith<$Res>
    implements $LeaderBoardInfoCopyWith<$Res> {
  factory _$$LeaderBoardInfoImplCopyWith(_$LeaderBoardInfoImpl value,
          $Res Function(_$LeaderBoardInfoImpl) then) =
      __$$LeaderBoardInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? gemValue,
      int? count,
      @JsonKey(name: 'profileVO') ProfileModel? profileInfo});

  @override
  $ProfileModelCopyWith<$Res>? get profileInfo;
}

/// @nodoc
class __$$LeaderBoardInfoImplCopyWithImpl<$Res>
    extends _$LeaderBoardInfoCopyWithImpl<$Res, _$LeaderBoardInfoImpl>
    implements _$$LeaderBoardInfoImplCopyWith<$Res> {
  __$$LeaderBoardInfoImplCopyWithImpl(
      _$LeaderBoardInfoImpl _value, $Res Function(_$LeaderBoardInfoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gemValue = freezed,
    Object? count = freezed,
    Object? profileInfo = freezed,
  }) {
    return _then(_$LeaderBoardInfoImpl(
      gemValue: freezed == gemValue
          ? _value.gemValue
          : gemValue // ignore: cast_nullable_to_non_nullable
              as int?,
      count: freezed == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int?,
      profileInfo: freezed == profileInfo
          ? _value.profileInfo
          : profileInfo // ignore: cast_nullable_to_non_nullable
              as ProfileModel?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LeaderBoardInfoImpl implements _LeaderBoardInfo {
  const _$LeaderBoardInfoImpl(
      {this.gemValue,
      this.count,
      @JsonKey(name: 'profileVO') this.profileInfo});

  factory _$LeaderBoardInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$LeaderBoardInfoImplFromJson(json);

  @override
  final int? gemValue;
  @override
  final int? count;
  @override
  @JsonKey(name: 'profileVO')
  final ProfileModel? profileInfo;

  @override
  String toString() {
    return 'LeaderBoardInfo(gemValue: $gemValue, count: $count, profileInfo: $profileInfo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LeaderBoardInfoImpl &&
            (identical(other.gemValue, gemValue) ||
                other.gemValue == gemValue) &&
            (identical(other.count, count) || other.count == count) &&
            (identical(other.profileInfo, profileInfo) ||
                other.profileInfo == profileInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, gemValue, count, profileInfo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LeaderBoardInfoImplCopyWith<_$LeaderBoardInfoImpl> get copyWith =>
      __$$LeaderBoardInfoImplCopyWithImpl<_$LeaderBoardInfoImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LeaderBoardInfoImplToJson(
      this,
    );
  }
}

abstract class _LeaderBoardInfo implements LeaderBoardInfo {
  const factory _LeaderBoardInfo(
          {final int? gemValue,
          final int? count,
          @JsonKey(name: 'profileVO') final ProfileModel? profileInfo}) =
      _$LeaderBoardInfoImpl;

  factory _LeaderBoardInfo.fromJson(Map<String, dynamic> json) =
      _$LeaderBoardInfoImpl.fromJson;

  @override
  int? get gemValue;
  @override
  int? get count;
  @override
  @JsonKey(name: 'profileVO')
  ProfileModel? get profileInfo;
  @override
  @JsonKey(ignore: true)
  _$$LeaderBoardInfoImplCopyWith<_$LeaderBoardInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
