// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'room_user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RoomUserImpl _$$RoomUserImplFromJson(Map<String, dynamic> json) =>
    _$RoomUserImpl(
      userId: json['userId'] as String?,
      uid: (json['uid'] as num?)?.toInt(),
      firstName: json['firstName'] as String?,
      avatarUrl: json['avatarUrl'] as String?,
      lastEnterTime: (json['lastEnterTime'] as num?)?.toInt(),
      level: (json['level'] as num?)?.toInt(),
      position: (json['position'] as num?)?.toInt(),
      isSpeaking: json['isSpeaking'] as bool?,
      isMutedByManager: json['isMutedByManager'] as bool?,
      isMutedBySelf: json['isMutedBySelf'] as bool?,
      isSelected: json['isSelected'] as bool?,
      avatarFrame: json['avatarFrame'] == null
          ? null
          : AvatarFrameModel.fromJson(
              json['avatarFrame'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$RoomUserImplToJson(_$RoomUserImpl instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'uid': instance.uid,
      'firstName': instance.firstName,
      'avatarUrl': instance.avatarUrl,
      'lastEnterTime': instance.lastEnterTime,
      'level': instance.level,
      'position': instance.position,
      'isSpeaking': instance.isSpeaking,
      'isMutedByManager': instance.isMutedByManager,
      'isMutedBySelf': instance.isMutedBySelf,
      'isSelected': instance.isSelected,
      'avatarFrame': instance.avatarFrame?.toJson(),
    };
