// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'room_list_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RoomListItemImpl _$$RoomListItemImplFromJson(Map<String, dynamic> json) =>
    _$RoomListItemImpl(
      room: json['roomVO'] == null
          ? null
          : RoomInfo.fromJson(json['roomVO'] as Map<String, dynamic>),
      memberInfos: (json['memberInfos'] as List<dynamic>?)
              ?.map((e) => ProfileModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      memberCount: (json['memberCount'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$RoomListItemImplToJson(_$RoomListItemImpl instance) =>
    <String, dynamic>{
      'roomVO': instance.room?.toJson(),
      'memberInfos': instance.memberInfos.map((e) => e.toJson()).toList(),
      'memberCount': instance.memberCount,
    };
