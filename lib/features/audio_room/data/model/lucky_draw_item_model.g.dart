// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lucky_draw_item_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LuckyDrawItemImpl _$$LuckyDrawItemImplFromJson(Map<String, dynamic> json) =>
    _$LuckyDrawItemImpl(
      id: (json['id'] as num?)?.toInt(),
      gift: json['giftVO'] == null
          ? null
          : GiftModel.fromJson(json['giftVO'] as Map<String, dynamic>),
      lotteryType: json['lotteryType'] == null
          ? null
          : LotteryType.fromJson(json['lotteryType'] as String),
      showOrder: (json['showOrder'] as num?)?.toInt(),
      isDaily: json['isDaily'] as bool?,
      userId: json['userId'] as String?,
      createdTime: json['createdTime'] == null
          ? null
          : DateTime.parse(json['createdTime'] as String),
      batchIndex: (json['batchIndex'] as num?)?.toInt(),
      batchCount: (json['batchCount'] as num?)?.toInt(),
      batchId: json['batchId'] as String?,
    );

Map<String, dynamic> _$$LuckyDrawItemImplToJson(_$LuckyDrawItemImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'giftVO': instance.gift?.toJson(),
      'lotteryType': _$LotteryTypeEnumMap[instance.lotteryType],
      'showOrder': instance.showOrder,
      'isDaily': instance.isDaily,
      'userId': instance.userId,
      'createdTime': instance.createdTime?.toIso8601String(),
      'batchIndex': instance.batchIndex,
      'batchCount': instance.batchCount,
      'batchId': instance.batchId,
    };

const _$LotteryTypeEnumMap = {
  LotteryType.economy: 'economy',
  LotteryType.luxury: 'luxury',
};
