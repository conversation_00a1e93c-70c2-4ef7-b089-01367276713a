// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'other_user_info_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OtherUserInfoModel _$OtherUserInfoModelFromJson(Map<String, dynamic> json) {
  return _OtherUserInfoModel.fromJson(json);
}

/// @nodoc
mixin _$OtherUserInfoModel {
  @JsonKey(name: 'profileVO')
  ProfileModel? get profile => throw _privateConstructorUsedError;
  int get followeeCount => throw _privateConstructorUsedError;
  int get followerCount => throw _privateConstructorUsedError;
  int get mutualFollowCount => throw _privateConstructorUsedError;
  int get gemCosts => throw _privateConstructorUsedError;
  bool get isBlocked => throw _privateConstructorUsedError;
  bool get isFollowing => throw _privateConstructorUsedError;
  bool get isFollowed => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OtherUserInfoModelCopyWith<OtherUserInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OtherUserInfoModelCopyWith<$Res> {
  factory $OtherUserInfoModelCopyWith(
          OtherUserInfoModel value, $Res Function(OtherUserInfoModel) then) =
      _$OtherUserInfoModelCopyWithImpl<$Res, OtherUserInfoModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'profileVO') ProfileModel? profile,
      int followeeCount,
      int followerCount,
      int mutualFollowCount,
      int gemCosts,
      bool isBlocked,
      bool isFollowing,
      bool isFollowed});

  $ProfileModelCopyWith<$Res>? get profile;
}

/// @nodoc
class _$OtherUserInfoModelCopyWithImpl<$Res, $Val extends OtherUserInfoModel>
    implements $OtherUserInfoModelCopyWith<$Res> {
  _$OtherUserInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profile = freezed,
    Object? followeeCount = null,
    Object? followerCount = null,
    Object? mutualFollowCount = null,
    Object? gemCosts = null,
    Object? isBlocked = null,
    Object? isFollowing = null,
    Object? isFollowed = null,
  }) {
    return _then(_value.copyWith(
      profile: freezed == profile
          ? _value.profile
          : profile // ignore: cast_nullable_to_non_nullable
              as ProfileModel?,
      followeeCount: null == followeeCount
          ? _value.followeeCount
          : followeeCount // ignore: cast_nullable_to_non_nullable
              as int,
      followerCount: null == followerCount
          ? _value.followerCount
          : followerCount // ignore: cast_nullable_to_non_nullable
              as int,
      mutualFollowCount: null == mutualFollowCount
          ? _value.mutualFollowCount
          : mutualFollowCount // ignore: cast_nullable_to_non_nullable
              as int,
      gemCosts: null == gemCosts
          ? _value.gemCosts
          : gemCosts // ignore: cast_nullable_to_non_nullable
              as int,
      isBlocked: null == isBlocked
          ? _value.isBlocked
          : isBlocked // ignore: cast_nullable_to_non_nullable
              as bool,
      isFollowing: null == isFollowing
          ? _value.isFollowing
          : isFollowing // ignore: cast_nullable_to_non_nullable
              as bool,
      isFollowed: null == isFollowed
          ? _value.isFollowed
          : isFollowed // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ProfileModelCopyWith<$Res>? get profile {
    if (_value.profile == null) {
      return null;
    }

    return $ProfileModelCopyWith<$Res>(_value.profile!, (value) {
      return _then(_value.copyWith(profile: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OtherUserInfoModelImplCopyWith<$Res>
    implements $OtherUserInfoModelCopyWith<$Res> {
  factory _$$OtherUserInfoModelImplCopyWith(_$OtherUserInfoModelImpl value,
          $Res Function(_$OtherUserInfoModelImpl) then) =
      __$$OtherUserInfoModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'profileVO') ProfileModel? profile,
      int followeeCount,
      int followerCount,
      int mutualFollowCount,
      int gemCosts,
      bool isBlocked,
      bool isFollowing,
      bool isFollowed});

  @override
  $ProfileModelCopyWith<$Res>? get profile;
}

/// @nodoc
class __$$OtherUserInfoModelImplCopyWithImpl<$Res>
    extends _$OtherUserInfoModelCopyWithImpl<$Res, _$OtherUserInfoModelImpl>
    implements _$$OtherUserInfoModelImplCopyWith<$Res> {
  __$$OtherUserInfoModelImplCopyWithImpl(_$OtherUserInfoModelImpl _value,
      $Res Function(_$OtherUserInfoModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profile = freezed,
    Object? followeeCount = null,
    Object? followerCount = null,
    Object? mutualFollowCount = null,
    Object? gemCosts = null,
    Object? isBlocked = null,
    Object? isFollowing = null,
    Object? isFollowed = null,
  }) {
    return _then(_$OtherUserInfoModelImpl(
      profile: freezed == profile
          ? _value.profile
          : profile // ignore: cast_nullable_to_non_nullable
              as ProfileModel?,
      followeeCount: null == followeeCount
          ? _value.followeeCount
          : followeeCount // ignore: cast_nullable_to_non_nullable
              as int,
      followerCount: null == followerCount
          ? _value.followerCount
          : followerCount // ignore: cast_nullable_to_non_nullable
              as int,
      mutualFollowCount: null == mutualFollowCount
          ? _value.mutualFollowCount
          : mutualFollowCount // ignore: cast_nullable_to_non_nullable
              as int,
      gemCosts: null == gemCosts
          ? _value.gemCosts
          : gemCosts // ignore: cast_nullable_to_non_nullable
              as int,
      isBlocked: null == isBlocked
          ? _value.isBlocked
          : isBlocked // ignore: cast_nullable_to_non_nullable
              as bool,
      isFollowing: null == isFollowing
          ? _value.isFollowing
          : isFollowing // ignore: cast_nullable_to_non_nullable
              as bool,
      isFollowed: null == isFollowed
          ? _value.isFollowed
          : isFollowed // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$OtherUserInfoModelImpl extends _OtherUserInfoModel {
  const _$OtherUserInfoModelImpl(
      {@JsonKey(name: 'profileVO') this.profile,
      this.followeeCount = 0,
      this.followerCount = 0,
      this.mutualFollowCount = 0,
      this.gemCosts = 0,
      this.isBlocked = false,
      this.isFollowing = false,
      this.isFollowed = false})
      : super._();

  factory _$OtherUserInfoModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$OtherUserInfoModelImplFromJson(json);

  @override
  @JsonKey(name: 'profileVO')
  final ProfileModel? profile;
  @override
  @JsonKey()
  final int followeeCount;
  @override
  @JsonKey()
  final int followerCount;
  @override
  @JsonKey()
  final int mutualFollowCount;
  @override
  @JsonKey()
  final int gemCosts;
  @override
  @JsonKey()
  final bool isBlocked;
  @override
  @JsonKey()
  final bool isFollowing;
  @override
  @JsonKey()
  final bool isFollowed;

  @override
  String toString() {
    return 'OtherUserInfoModel(profile: $profile, followeeCount: $followeeCount, followerCount: $followerCount, mutualFollowCount: $mutualFollowCount, gemCosts: $gemCosts, isBlocked: $isBlocked, isFollowing: $isFollowing, isFollowed: $isFollowed)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OtherUserInfoModelImpl &&
            (identical(other.profile, profile) || other.profile == profile) &&
            (identical(other.followeeCount, followeeCount) ||
                other.followeeCount == followeeCount) &&
            (identical(other.followerCount, followerCount) ||
                other.followerCount == followerCount) &&
            (identical(other.mutualFollowCount, mutualFollowCount) ||
                other.mutualFollowCount == mutualFollowCount) &&
            (identical(other.gemCosts, gemCosts) ||
                other.gemCosts == gemCosts) &&
            (identical(other.isBlocked, isBlocked) ||
                other.isBlocked == isBlocked) &&
            (identical(other.isFollowing, isFollowing) ||
                other.isFollowing == isFollowing) &&
            (identical(other.isFollowed, isFollowed) ||
                other.isFollowed == isFollowed));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      profile,
      followeeCount,
      followerCount,
      mutualFollowCount,
      gemCosts,
      isBlocked,
      isFollowing,
      isFollowed);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OtherUserInfoModelImplCopyWith<_$OtherUserInfoModelImpl> get copyWith =>
      __$$OtherUserInfoModelImplCopyWithImpl<_$OtherUserInfoModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OtherUserInfoModelImplToJson(
      this,
    );
  }
}

abstract class _OtherUserInfoModel extends OtherUserInfoModel {
  const factory _OtherUserInfoModel(
      {@JsonKey(name: 'profileVO') final ProfileModel? profile,
      final int followeeCount,
      final int followerCount,
      final int mutualFollowCount,
      final int gemCosts,
      final bool isBlocked,
      final bool isFollowing,
      final bool isFollowed}) = _$OtherUserInfoModelImpl;
  const _OtherUserInfoModel._() : super._();

  factory _OtherUserInfoModel.fromJson(Map<String, dynamic> json) =
      _$OtherUserInfoModelImpl.fromJson;

  @override
  @JsonKey(name: 'profileVO')
  ProfileModel? get profile;
  @override
  int get followeeCount;
  @override
  int get followerCount;
  @override
  int get mutualFollowCount;
  @override
  int get gemCosts;
  @override
  bool get isBlocked;
  @override
  bool get isFollowing;
  @override
  bool get isFollowed;
  @override
  @JsonKey(ignore: true)
  _$$OtherUserInfoModelImplCopyWith<_$OtherUserInfoModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
