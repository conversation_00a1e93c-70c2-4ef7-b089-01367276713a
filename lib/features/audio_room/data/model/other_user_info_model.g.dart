// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'other_user_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OtherUserInfoModelImpl _$$OtherUserInfoModelImplFromJson(
        Map<String, dynamic> json) =>
    _$OtherUserInfoModelImpl(
      profile: json['profileVO'] == null
          ? null
          : ProfileModel.fromJson(json['profileVO'] as Map<String, dynamic>),
      followeeCount: (json['followeeCount'] as num?)?.toInt() ?? 0,
      followerCount: (json['followerCount'] as num?)?.toInt() ?? 0,
      mutualFollowCount: (json['mutualFollowCount'] as num?)?.toInt() ?? 0,
      gemCosts: (json['gemCosts'] as num?)?.toInt() ?? 0,
      isBlocked: json['isBlocked'] as bool? ?? false,
      isFollowing: json['isFollowing'] as bool? ?? false,
      isFollowed: json['isFollowed'] as bool? ?? false,
    );

Map<String, dynamic> _$$OtherUserInfoModelImplToJson(
        _$OtherUserInfoModelImpl instance) =>
    <String, dynamic>{
      'profileVO': instance.profile?.toJson(),
      'followeeCount': instance.followeeCount,
      'followerCount': instance.followerCount,
      'mutualFollowCount': instance.mutualFollowCount,
      'gemCosts': instance.gemCosts,
      'isBlocked': instance.isBlocked,
      'isFollowing': instance.isFollowing,
      'isFollowed': instance.isFollowed,
    };
