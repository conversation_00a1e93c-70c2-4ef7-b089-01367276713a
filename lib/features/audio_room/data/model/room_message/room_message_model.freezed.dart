// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'room_message_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RoomMessageModel _$RoomMessageModelFromJson(Map<String, dynamic> json) {
  return _RoomMessageModel.fromJson(json);
}

/// @nodoc
mixin _$RoomMessageModel {
  String? get id => throw _privateConstructorUsedError;
  String? get roomId => throw _privateConstructorUsedError;
  int? get createAt => throw _privateConstructorUsedError;
  int? get senderId => throw _privateConstructorUsedError; // agora uid
  RoomUser? get sender => throw _privateConstructorUsedError; // sender info
  RoomMessageEvent? get event => throw _privateConstructorUsedError;
  RoomMessageEventSubtype? get eventSubtype =>
      throw _privateConstructorUsedError;
  RoomUser? get mention =>
      throw _privateConstructorUsedError; // {id, firstName}
// invitee id, agreeMicApply, rejectMicApply
  int? get targetId => throw _privateConstructorUsedError; // agora uid
  RoomUser? get targetUser => throw _privateConstructorUsedError;
  RoomMessageFollowActionSource? get followActionSource =>
      throw _privateConstructorUsedError;
  String? get extra => throw _privateConstructorUsedError;
  RoomMessageExtraType? get extraType => throw _privateConstructorUsedError;
  String? get content => throw _privateConstructorUsedError;
  RoomPosition? get position => throw _privateConstructorUsedError;
  RoomMessageGift? get gift => throw _privateConstructorUsedError;
  List<int>? get giftReceives => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RoomMessageModelCopyWith<RoomMessageModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RoomMessageModelCopyWith<$Res> {
  factory $RoomMessageModelCopyWith(
          RoomMessageModel value, $Res Function(RoomMessageModel) then) =
      _$RoomMessageModelCopyWithImpl<$Res, RoomMessageModel>;
  @useResult
  $Res call(
      {String? id,
      String? roomId,
      int? createAt,
      int? senderId,
      RoomUser? sender,
      RoomMessageEvent? event,
      RoomMessageEventSubtype? eventSubtype,
      RoomUser? mention,
      int? targetId,
      RoomUser? targetUser,
      RoomMessageFollowActionSource? followActionSource,
      String? extra,
      RoomMessageExtraType? extraType,
      String? content,
      RoomPosition? position,
      RoomMessageGift? gift,
      List<int>? giftReceives});

  $RoomUserCopyWith<$Res>? get sender;
  $RoomUserCopyWith<$Res>? get mention;
  $RoomUserCopyWith<$Res>? get targetUser;
  $RoomPositionCopyWith<$Res>? get position;
  $RoomMessageGiftCopyWith<$Res>? get gift;
}

/// @nodoc
class _$RoomMessageModelCopyWithImpl<$Res, $Val extends RoomMessageModel>
    implements $RoomMessageModelCopyWith<$Res> {
  _$RoomMessageModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? roomId = freezed,
    Object? createAt = freezed,
    Object? senderId = freezed,
    Object? sender = freezed,
    Object? event = freezed,
    Object? eventSubtype = freezed,
    Object? mention = freezed,
    Object? targetId = freezed,
    Object? targetUser = freezed,
    Object? followActionSource = freezed,
    Object? extra = freezed,
    Object? extraType = freezed,
    Object? content = freezed,
    Object? position = freezed,
    Object? gift = freezed,
    Object? giftReceives = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      roomId: freezed == roomId
          ? _value.roomId
          : roomId // ignore: cast_nullable_to_non_nullable
              as String?,
      createAt: freezed == createAt
          ? _value.createAt
          : createAt // ignore: cast_nullable_to_non_nullable
              as int?,
      senderId: freezed == senderId
          ? _value.senderId
          : senderId // ignore: cast_nullable_to_non_nullable
              as int?,
      sender: freezed == sender
          ? _value.sender
          : sender // ignore: cast_nullable_to_non_nullable
              as RoomUser?,
      event: freezed == event
          ? _value.event
          : event // ignore: cast_nullable_to_non_nullable
              as RoomMessageEvent?,
      eventSubtype: freezed == eventSubtype
          ? _value.eventSubtype
          : eventSubtype // ignore: cast_nullable_to_non_nullable
              as RoomMessageEventSubtype?,
      mention: freezed == mention
          ? _value.mention
          : mention // ignore: cast_nullable_to_non_nullable
              as RoomUser?,
      targetId: freezed == targetId
          ? _value.targetId
          : targetId // ignore: cast_nullable_to_non_nullable
              as int?,
      targetUser: freezed == targetUser
          ? _value.targetUser
          : targetUser // ignore: cast_nullable_to_non_nullable
              as RoomUser?,
      followActionSource: freezed == followActionSource
          ? _value.followActionSource
          : followActionSource // ignore: cast_nullable_to_non_nullable
              as RoomMessageFollowActionSource?,
      extra: freezed == extra
          ? _value.extra
          : extra // ignore: cast_nullable_to_non_nullable
              as String?,
      extraType: freezed == extraType
          ? _value.extraType
          : extraType // ignore: cast_nullable_to_non_nullable
              as RoomMessageExtraType?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      position: freezed == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as RoomPosition?,
      gift: freezed == gift
          ? _value.gift
          : gift // ignore: cast_nullable_to_non_nullable
              as RoomMessageGift?,
      giftReceives: freezed == giftReceives
          ? _value.giftReceives
          : giftReceives // ignore: cast_nullable_to_non_nullable
              as List<int>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $RoomUserCopyWith<$Res>? get sender {
    if (_value.sender == null) {
      return null;
    }

    return $RoomUserCopyWith<$Res>(_value.sender!, (value) {
      return _then(_value.copyWith(sender: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $RoomUserCopyWith<$Res>? get mention {
    if (_value.mention == null) {
      return null;
    }

    return $RoomUserCopyWith<$Res>(_value.mention!, (value) {
      return _then(_value.copyWith(mention: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $RoomUserCopyWith<$Res>? get targetUser {
    if (_value.targetUser == null) {
      return null;
    }

    return $RoomUserCopyWith<$Res>(_value.targetUser!, (value) {
      return _then(_value.copyWith(targetUser: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $RoomPositionCopyWith<$Res>? get position {
    if (_value.position == null) {
      return null;
    }

    return $RoomPositionCopyWith<$Res>(_value.position!, (value) {
      return _then(_value.copyWith(position: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $RoomMessageGiftCopyWith<$Res>? get gift {
    if (_value.gift == null) {
      return null;
    }

    return $RoomMessageGiftCopyWith<$Res>(_value.gift!, (value) {
      return _then(_value.copyWith(gift: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RoomMessageModelImplCopyWith<$Res>
    implements $RoomMessageModelCopyWith<$Res> {
  factory _$$RoomMessageModelImplCopyWith(_$RoomMessageModelImpl value,
          $Res Function(_$RoomMessageModelImpl) then) =
      __$$RoomMessageModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      String? roomId,
      int? createAt,
      int? senderId,
      RoomUser? sender,
      RoomMessageEvent? event,
      RoomMessageEventSubtype? eventSubtype,
      RoomUser? mention,
      int? targetId,
      RoomUser? targetUser,
      RoomMessageFollowActionSource? followActionSource,
      String? extra,
      RoomMessageExtraType? extraType,
      String? content,
      RoomPosition? position,
      RoomMessageGift? gift,
      List<int>? giftReceives});

  @override
  $RoomUserCopyWith<$Res>? get sender;
  @override
  $RoomUserCopyWith<$Res>? get mention;
  @override
  $RoomUserCopyWith<$Res>? get targetUser;
  @override
  $RoomPositionCopyWith<$Res>? get position;
  @override
  $RoomMessageGiftCopyWith<$Res>? get gift;
}

/// @nodoc
class __$$RoomMessageModelImplCopyWithImpl<$Res>
    extends _$RoomMessageModelCopyWithImpl<$Res, _$RoomMessageModelImpl>
    implements _$$RoomMessageModelImplCopyWith<$Res> {
  __$$RoomMessageModelImplCopyWithImpl(_$RoomMessageModelImpl _value,
      $Res Function(_$RoomMessageModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? roomId = freezed,
    Object? createAt = freezed,
    Object? senderId = freezed,
    Object? sender = freezed,
    Object? event = freezed,
    Object? eventSubtype = freezed,
    Object? mention = freezed,
    Object? targetId = freezed,
    Object? targetUser = freezed,
    Object? followActionSource = freezed,
    Object? extra = freezed,
    Object? extraType = freezed,
    Object? content = freezed,
    Object? position = freezed,
    Object? gift = freezed,
    Object? giftReceives = freezed,
  }) {
    return _then(_$RoomMessageModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      roomId: freezed == roomId
          ? _value.roomId
          : roomId // ignore: cast_nullable_to_non_nullable
              as String?,
      createAt: freezed == createAt
          ? _value.createAt
          : createAt // ignore: cast_nullable_to_non_nullable
              as int?,
      senderId: freezed == senderId
          ? _value.senderId
          : senderId // ignore: cast_nullable_to_non_nullable
              as int?,
      sender: freezed == sender
          ? _value.sender
          : sender // ignore: cast_nullable_to_non_nullable
              as RoomUser?,
      event: freezed == event
          ? _value.event
          : event // ignore: cast_nullable_to_non_nullable
              as RoomMessageEvent?,
      eventSubtype: freezed == eventSubtype
          ? _value.eventSubtype
          : eventSubtype // ignore: cast_nullable_to_non_nullable
              as RoomMessageEventSubtype?,
      mention: freezed == mention
          ? _value.mention
          : mention // ignore: cast_nullable_to_non_nullable
              as RoomUser?,
      targetId: freezed == targetId
          ? _value.targetId
          : targetId // ignore: cast_nullable_to_non_nullable
              as int?,
      targetUser: freezed == targetUser
          ? _value.targetUser
          : targetUser // ignore: cast_nullable_to_non_nullable
              as RoomUser?,
      followActionSource: freezed == followActionSource
          ? _value.followActionSource
          : followActionSource // ignore: cast_nullable_to_non_nullable
              as RoomMessageFollowActionSource?,
      extra: freezed == extra
          ? _value.extra
          : extra // ignore: cast_nullable_to_non_nullable
              as String?,
      extraType: freezed == extraType
          ? _value.extraType
          : extraType // ignore: cast_nullable_to_non_nullable
              as RoomMessageExtraType?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      position: freezed == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as RoomPosition?,
      gift: freezed == gift
          ? _value.gift
          : gift // ignore: cast_nullable_to_non_nullable
              as RoomMessageGift?,
      giftReceives: freezed == giftReceives
          ? _value._giftReceives
          : giftReceives // ignore: cast_nullable_to_non_nullable
              as List<int>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RoomMessageModelImpl implements _RoomMessageModel {
  const _$RoomMessageModelImpl(
      {this.id,
      this.roomId,
      this.createAt,
      this.senderId,
      this.sender,
      this.event,
      this.eventSubtype,
      this.mention,
      this.targetId,
      this.targetUser,
      this.followActionSource,
      this.extra,
      this.extraType,
      this.content,
      this.position,
      this.gift,
      final List<int>? giftReceives})
      : _giftReceives = giftReceives;

  factory _$RoomMessageModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$RoomMessageModelImplFromJson(json);

  @override
  final String? id;
  @override
  final String? roomId;
  @override
  final int? createAt;
  @override
  final int? senderId;
// agora uid
  @override
  final RoomUser? sender;
// sender info
  @override
  final RoomMessageEvent? event;
  @override
  final RoomMessageEventSubtype? eventSubtype;
  @override
  final RoomUser? mention;
// {id, firstName}
// invitee id, agreeMicApply, rejectMicApply
  @override
  final int? targetId;
// agora uid
  @override
  final RoomUser? targetUser;
  @override
  final RoomMessageFollowActionSource? followActionSource;
  @override
  final String? extra;
  @override
  final RoomMessageExtraType? extraType;
  @override
  final String? content;
  @override
  final RoomPosition? position;
  @override
  final RoomMessageGift? gift;
  final List<int>? _giftReceives;
  @override
  List<int>? get giftReceives {
    final value = _giftReceives;
    if (value == null) return null;
    if (_giftReceives is EqualUnmodifiableListView) return _giftReceives;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'RoomMessageModel(id: $id, roomId: $roomId, createAt: $createAt, senderId: $senderId, sender: $sender, event: $event, eventSubtype: $eventSubtype, mention: $mention, targetId: $targetId, targetUser: $targetUser, followActionSource: $followActionSource, extra: $extra, extraType: $extraType, content: $content, position: $position, gift: $gift, giftReceives: $giftReceives)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RoomMessageModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.roomId, roomId) || other.roomId == roomId) &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.senderId, senderId) ||
                other.senderId == senderId) &&
            (identical(other.sender, sender) || other.sender == sender) &&
            (identical(other.event, event) || other.event == event) &&
            (identical(other.eventSubtype, eventSubtype) ||
                other.eventSubtype == eventSubtype) &&
            (identical(other.mention, mention) || other.mention == mention) &&
            (identical(other.targetId, targetId) ||
                other.targetId == targetId) &&
            (identical(other.targetUser, targetUser) ||
                other.targetUser == targetUser) &&
            (identical(other.followActionSource, followActionSource) ||
                other.followActionSource == followActionSource) &&
            (identical(other.extra, extra) || other.extra == extra) &&
            (identical(other.extraType, extraType) ||
                other.extraType == extraType) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.position, position) ||
                other.position == position) &&
            (identical(other.gift, gift) || other.gift == gift) &&
            const DeepCollectionEquality()
                .equals(other._giftReceives, _giftReceives));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      roomId,
      createAt,
      senderId,
      sender,
      event,
      eventSubtype,
      mention,
      targetId,
      targetUser,
      followActionSource,
      extra,
      extraType,
      content,
      position,
      gift,
      const DeepCollectionEquality().hash(_giftReceives));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RoomMessageModelImplCopyWith<_$RoomMessageModelImpl> get copyWith =>
      __$$RoomMessageModelImplCopyWithImpl<_$RoomMessageModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RoomMessageModelImplToJson(
      this,
    );
  }
}

abstract class _RoomMessageModel implements RoomMessageModel {
  const factory _RoomMessageModel(
      {final String? id,
      final String? roomId,
      final int? createAt,
      final int? senderId,
      final RoomUser? sender,
      final RoomMessageEvent? event,
      final RoomMessageEventSubtype? eventSubtype,
      final RoomUser? mention,
      final int? targetId,
      final RoomUser? targetUser,
      final RoomMessageFollowActionSource? followActionSource,
      final String? extra,
      final RoomMessageExtraType? extraType,
      final String? content,
      final RoomPosition? position,
      final RoomMessageGift? gift,
      final List<int>? giftReceives}) = _$RoomMessageModelImpl;

  factory _RoomMessageModel.fromJson(Map<String, dynamic> json) =
      _$RoomMessageModelImpl.fromJson;

  @override
  String? get id;
  @override
  String? get roomId;
  @override
  int? get createAt;
  @override
  int? get senderId;
  @override // agora uid
  RoomUser? get sender;
  @override // sender info
  RoomMessageEvent? get event;
  @override
  RoomMessageEventSubtype? get eventSubtype;
  @override
  RoomUser? get mention;
  @override // {id, firstName}
// invitee id, agreeMicApply, rejectMicApply
  int? get targetId;
  @override // agora uid
  RoomUser? get targetUser;
  @override
  RoomMessageFollowActionSource? get followActionSource;
  @override
  String? get extra;
  @override
  RoomMessageExtraType? get extraType;
  @override
  String? get content;
  @override
  RoomPosition? get position;
  @override
  RoomMessageGift? get gift;
  @override
  List<int>? get giftReceives;
  @override
  @JsonKey(ignore: true)
  _$$RoomMessageModelImplCopyWith<_$RoomMessageModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
