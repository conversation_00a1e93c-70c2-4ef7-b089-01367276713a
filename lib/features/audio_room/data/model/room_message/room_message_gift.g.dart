// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'room_message_gift.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RoomMessageGiftImpl _$$RoomMessageGiftImplFromJson(
        Map<String, dynamic> json) =>
    _$RoomMessageGiftImpl(
      id: (json['id'] as num?)?.toInt(),
      sendGiftResults: (json['sendGiftResults'] as List<dynamic>?)
          ?.map((e) => SendGiftResult.fromJson(e as Map<String, dynamic>))
          .toList(),
      name: json['name'] as String?,
      isFree: json['isFree'] as bool?,
      price: (json['price'] as num?)?.toInt(),
      isMystery: json['isMystery'] as bool?,
      svgaUrl: json['svgaUrl'] as String?,
      imageUrl: json['imageUrl'] as String?,
      refreshLeaderboard: json['refreshLeaderboard'] as bool?,
      giftType: $enumDecodeNullable(_$GiftTypeEnumMap, json['giftType']),
      giftExpiryDays: (json['giftExpiryDays'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$RoomMessageGiftImplToJson(
        _$RoomMessageGiftImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sendGiftResults':
          instance.sendGiftResults?.map((e) => e.toJson()).toList(),
      'name': instance.name,
      'isFree': instance.isFree,
      'price': instance.price,
      'isMystery': instance.isMystery,
      'svgaUrl': instance.svgaUrl,
      'imageUrl': instance.imageUrl,
      'refreshLeaderboard': instance.refreshLeaderboard,
      'giftType': _$GiftTypeEnumMap[instance.giftType],
      'giftExpiryDays': instance.giftExpiryDays,
    };

const _$GiftTypeEnumMap = {
  GiftType.gift: 'gift',
  GiftType.frame: 'avatar_frame',
};
