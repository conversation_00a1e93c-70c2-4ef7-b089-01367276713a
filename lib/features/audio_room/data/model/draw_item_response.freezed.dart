// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'draw_item_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DrawItemsResponse _$DrawItemsResponseFromJson(Map<String, dynamic> json) {
  return _DrawItemsResponse.fromJson(json);
}

/// @nodoc
mixin _$DrawItemsResponse {
  EconomyLotteryGifts? get economyLotteryGifts =>
      throw _privateConstructorUsedError;
  LuxuryLotteryGifts? get luxuryLotteryGifts =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DrawItemsResponseCopyWith<DrawItemsResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DrawItemsResponseCopyWith<$Res> {
  factory $DrawItemsResponseCopyWith(
          DrawItemsResponse value, $Res Function(DrawItemsResponse) then) =
      _$DrawItemsResponseCopyWithImpl<$Res, DrawItemsResponse>;
  @useResult
  $Res call(
      {EconomyLotteryGifts? economyLotteryGifts,
      LuxuryLotteryGifts? luxuryLotteryGifts});

  $EconomyLotteryGiftsCopyWith<$Res>? get economyLotteryGifts;
  $LuxuryLotteryGiftsCopyWith<$Res>? get luxuryLotteryGifts;
}

/// @nodoc
class _$DrawItemsResponseCopyWithImpl<$Res, $Val extends DrawItemsResponse>
    implements $DrawItemsResponseCopyWith<$Res> {
  _$DrawItemsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? economyLotteryGifts = freezed,
    Object? luxuryLotteryGifts = freezed,
  }) {
    return _then(_value.copyWith(
      economyLotteryGifts: freezed == economyLotteryGifts
          ? _value.economyLotteryGifts
          : economyLotteryGifts // ignore: cast_nullable_to_non_nullable
              as EconomyLotteryGifts?,
      luxuryLotteryGifts: freezed == luxuryLotteryGifts
          ? _value.luxuryLotteryGifts
          : luxuryLotteryGifts // ignore: cast_nullable_to_non_nullable
              as LuxuryLotteryGifts?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $EconomyLotteryGiftsCopyWith<$Res>? get economyLotteryGifts {
    if (_value.economyLotteryGifts == null) {
      return null;
    }

    return $EconomyLotteryGiftsCopyWith<$Res>(_value.economyLotteryGifts!,
        (value) {
      return _then(_value.copyWith(economyLotteryGifts: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $LuxuryLotteryGiftsCopyWith<$Res>? get luxuryLotteryGifts {
    if (_value.luxuryLotteryGifts == null) {
      return null;
    }

    return $LuxuryLotteryGiftsCopyWith<$Res>(_value.luxuryLotteryGifts!,
        (value) {
      return _then(_value.copyWith(luxuryLotteryGifts: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DrawItemsResponseImplCopyWith<$Res>
    implements $DrawItemsResponseCopyWith<$Res> {
  factory _$$DrawItemsResponseImplCopyWith(_$DrawItemsResponseImpl value,
          $Res Function(_$DrawItemsResponseImpl) then) =
      __$$DrawItemsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {EconomyLotteryGifts? economyLotteryGifts,
      LuxuryLotteryGifts? luxuryLotteryGifts});

  @override
  $EconomyLotteryGiftsCopyWith<$Res>? get economyLotteryGifts;
  @override
  $LuxuryLotteryGiftsCopyWith<$Res>? get luxuryLotteryGifts;
}

/// @nodoc
class __$$DrawItemsResponseImplCopyWithImpl<$Res>
    extends _$DrawItemsResponseCopyWithImpl<$Res, _$DrawItemsResponseImpl>
    implements _$$DrawItemsResponseImplCopyWith<$Res> {
  __$$DrawItemsResponseImplCopyWithImpl(_$DrawItemsResponseImpl _value,
      $Res Function(_$DrawItemsResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? economyLotteryGifts = freezed,
    Object? luxuryLotteryGifts = freezed,
  }) {
    return _then(_$DrawItemsResponseImpl(
      economyLotteryGifts: freezed == economyLotteryGifts
          ? _value.economyLotteryGifts
          : economyLotteryGifts // ignore: cast_nullable_to_non_nullable
              as EconomyLotteryGifts?,
      luxuryLotteryGifts: freezed == luxuryLotteryGifts
          ? _value.luxuryLotteryGifts
          : luxuryLotteryGifts // ignore: cast_nullable_to_non_nullable
              as LuxuryLotteryGifts?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DrawItemsResponseImpl implements _DrawItemsResponse {
  const _$DrawItemsResponseImpl(
      {this.economyLotteryGifts, this.luxuryLotteryGifts});

  factory _$DrawItemsResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$DrawItemsResponseImplFromJson(json);

  @override
  final EconomyLotteryGifts? economyLotteryGifts;
  @override
  final LuxuryLotteryGifts? luxuryLotteryGifts;

  @override
  String toString() {
    return 'DrawItemsResponse(economyLotteryGifts: $economyLotteryGifts, luxuryLotteryGifts: $luxuryLotteryGifts)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DrawItemsResponseImpl &&
            (identical(other.economyLotteryGifts, economyLotteryGifts) ||
                other.economyLotteryGifts == economyLotteryGifts) &&
            (identical(other.luxuryLotteryGifts, luxuryLotteryGifts) ||
                other.luxuryLotteryGifts == luxuryLotteryGifts));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, economyLotteryGifts, luxuryLotteryGifts);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DrawItemsResponseImplCopyWith<_$DrawItemsResponseImpl> get copyWith =>
      __$$DrawItemsResponseImplCopyWithImpl<_$DrawItemsResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DrawItemsResponseImplToJson(
      this,
    );
  }
}

abstract class _DrawItemsResponse implements DrawItemsResponse {
  const factory _DrawItemsResponse(
      {final EconomyLotteryGifts? economyLotteryGifts,
      final LuxuryLotteryGifts? luxuryLotteryGifts}) = _$DrawItemsResponseImpl;

  factory _DrawItemsResponse.fromJson(Map<String, dynamic> json) =
      _$DrawItemsResponseImpl.fromJson;

  @override
  EconomyLotteryGifts? get economyLotteryGifts;
  @override
  LuxuryLotteryGifts? get luxuryLotteryGifts;
  @override
  @JsonKey(ignore: true)
  _$$DrawItemsResponseImplCopyWith<_$DrawItemsResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

EconomyLotteryGifts _$EconomyLotteryGiftsFromJson(Map<String, dynamic> json) {
  return _EconomyLotteryGifts.fromJson(json);
}

/// @nodoc
mixin _$EconomyLotteryGifts {
  List<LuckyDrawItem>? get lotteryGifts => throw _privateConstructorUsedError;
  int? get cost => throw _privateConstructorUsedError;
  int? get requiredDraws => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $EconomyLotteryGiftsCopyWith<EconomyLotteryGifts> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EconomyLotteryGiftsCopyWith<$Res> {
  factory $EconomyLotteryGiftsCopyWith(
          EconomyLotteryGifts value, $Res Function(EconomyLotteryGifts) then) =
      _$EconomyLotteryGiftsCopyWithImpl<$Res, EconomyLotteryGifts>;
  @useResult
  $Res call({List<LuckyDrawItem>? lotteryGifts, int? cost, int? requiredDraws});
}

/// @nodoc
class _$EconomyLotteryGiftsCopyWithImpl<$Res, $Val extends EconomyLotteryGifts>
    implements $EconomyLotteryGiftsCopyWith<$Res> {
  _$EconomyLotteryGiftsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lotteryGifts = freezed,
    Object? cost = freezed,
    Object? requiredDraws = freezed,
  }) {
    return _then(_value.copyWith(
      lotteryGifts: freezed == lotteryGifts
          ? _value.lotteryGifts
          : lotteryGifts // ignore: cast_nullable_to_non_nullable
              as List<LuckyDrawItem>?,
      cost: freezed == cost
          ? _value.cost
          : cost // ignore: cast_nullable_to_non_nullable
              as int?,
      requiredDraws: freezed == requiredDraws
          ? _value.requiredDraws
          : requiredDraws // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EconomyLotteryGiftsImplCopyWith<$Res>
    implements $EconomyLotteryGiftsCopyWith<$Res> {
  factory _$$EconomyLotteryGiftsImplCopyWith(_$EconomyLotteryGiftsImpl value,
          $Res Function(_$EconomyLotteryGiftsImpl) then) =
      __$$EconomyLotteryGiftsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<LuckyDrawItem>? lotteryGifts, int? cost, int? requiredDraws});
}

/// @nodoc
class __$$EconomyLotteryGiftsImplCopyWithImpl<$Res>
    extends _$EconomyLotteryGiftsCopyWithImpl<$Res, _$EconomyLotteryGiftsImpl>
    implements _$$EconomyLotteryGiftsImplCopyWith<$Res> {
  __$$EconomyLotteryGiftsImplCopyWithImpl(_$EconomyLotteryGiftsImpl _value,
      $Res Function(_$EconomyLotteryGiftsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lotteryGifts = freezed,
    Object? cost = freezed,
    Object? requiredDraws = freezed,
  }) {
    return _then(_$EconomyLotteryGiftsImpl(
      lotteryGifts: freezed == lotteryGifts
          ? _value._lotteryGifts
          : lotteryGifts // ignore: cast_nullable_to_non_nullable
              as List<LuckyDrawItem>?,
      cost: freezed == cost
          ? _value.cost
          : cost // ignore: cast_nullable_to_non_nullable
              as int?,
      requiredDraws: freezed == requiredDraws
          ? _value.requiredDraws
          : requiredDraws // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$EconomyLotteryGiftsImpl implements _EconomyLotteryGifts {
  const _$EconomyLotteryGiftsImpl(
      {final List<LuckyDrawItem>? lotteryGifts, this.cost, this.requiredDraws})
      : _lotteryGifts = lotteryGifts;

  factory _$EconomyLotteryGiftsImpl.fromJson(Map<String, dynamic> json) =>
      _$$EconomyLotteryGiftsImplFromJson(json);

  final List<LuckyDrawItem>? _lotteryGifts;
  @override
  List<LuckyDrawItem>? get lotteryGifts {
    final value = _lotteryGifts;
    if (value == null) return null;
    if (_lotteryGifts is EqualUnmodifiableListView) return _lotteryGifts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? cost;
  @override
  final int? requiredDraws;

  @override
  String toString() {
    return 'EconomyLotteryGifts(lotteryGifts: $lotteryGifts, cost: $cost, requiredDraws: $requiredDraws)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EconomyLotteryGiftsImpl &&
            const DeepCollectionEquality()
                .equals(other._lotteryGifts, _lotteryGifts) &&
            (identical(other.cost, cost) || other.cost == cost) &&
            (identical(other.requiredDraws, requiredDraws) ||
                other.requiredDraws == requiredDraws));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_lotteryGifts), cost, requiredDraws);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$EconomyLotteryGiftsImplCopyWith<_$EconomyLotteryGiftsImpl> get copyWith =>
      __$$EconomyLotteryGiftsImplCopyWithImpl<_$EconomyLotteryGiftsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EconomyLotteryGiftsImplToJson(
      this,
    );
  }
}

abstract class _EconomyLotteryGifts implements EconomyLotteryGifts {
  const factory _EconomyLotteryGifts(
      {final List<LuckyDrawItem>? lotteryGifts,
      final int? cost,
      final int? requiredDraws}) = _$EconomyLotteryGiftsImpl;

  factory _EconomyLotteryGifts.fromJson(Map<String, dynamic> json) =
      _$EconomyLotteryGiftsImpl.fromJson;

  @override
  List<LuckyDrawItem>? get lotteryGifts;
  @override
  int? get cost;
  @override
  int? get requiredDraws;
  @override
  @JsonKey(ignore: true)
  _$$EconomyLotteryGiftsImplCopyWith<_$EconomyLotteryGiftsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LuxuryLotteryGifts _$LuxuryLotteryGiftsFromJson(Map<String, dynamic> json) {
  return _LuxuryLotteryGifts.fromJson(json);
}

/// @nodoc
mixin _$LuxuryLotteryGifts {
  List<LuckyDrawItem>? get lotteryGifts => throw _privateConstructorUsedError;
  int? get cost => throw _privateConstructorUsedError;
  int? get requiredDraws => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LuxuryLotteryGiftsCopyWith<LuxuryLotteryGifts> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LuxuryLotteryGiftsCopyWith<$Res> {
  factory $LuxuryLotteryGiftsCopyWith(
          LuxuryLotteryGifts value, $Res Function(LuxuryLotteryGifts) then) =
      _$LuxuryLotteryGiftsCopyWithImpl<$Res, LuxuryLotteryGifts>;
  @useResult
  $Res call({List<LuckyDrawItem>? lotteryGifts, int? cost, int? requiredDraws});
}

/// @nodoc
class _$LuxuryLotteryGiftsCopyWithImpl<$Res, $Val extends LuxuryLotteryGifts>
    implements $LuxuryLotteryGiftsCopyWith<$Res> {
  _$LuxuryLotteryGiftsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lotteryGifts = freezed,
    Object? cost = freezed,
    Object? requiredDraws = freezed,
  }) {
    return _then(_value.copyWith(
      lotteryGifts: freezed == lotteryGifts
          ? _value.lotteryGifts
          : lotteryGifts // ignore: cast_nullable_to_non_nullable
              as List<LuckyDrawItem>?,
      cost: freezed == cost
          ? _value.cost
          : cost // ignore: cast_nullable_to_non_nullable
              as int?,
      requiredDraws: freezed == requiredDraws
          ? _value.requiredDraws
          : requiredDraws // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LuxuryLotteryGiftsImplCopyWith<$Res>
    implements $LuxuryLotteryGiftsCopyWith<$Res> {
  factory _$$LuxuryLotteryGiftsImplCopyWith(_$LuxuryLotteryGiftsImpl value,
          $Res Function(_$LuxuryLotteryGiftsImpl) then) =
      __$$LuxuryLotteryGiftsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<LuckyDrawItem>? lotteryGifts, int? cost, int? requiredDraws});
}

/// @nodoc
class __$$LuxuryLotteryGiftsImplCopyWithImpl<$Res>
    extends _$LuxuryLotteryGiftsCopyWithImpl<$Res, _$LuxuryLotteryGiftsImpl>
    implements _$$LuxuryLotteryGiftsImplCopyWith<$Res> {
  __$$LuxuryLotteryGiftsImplCopyWithImpl(_$LuxuryLotteryGiftsImpl _value,
      $Res Function(_$LuxuryLotteryGiftsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lotteryGifts = freezed,
    Object? cost = freezed,
    Object? requiredDraws = freezed,
  }) {
    return _then(_$LuxuryLotteryGiftsImpl(
      lotteryGifts: freezed == lotteryGifts
          ? _value._lotteryGifts
          : lotteryGifts // ignore: cast_nullable_to_non_nullable
              as List<LuckyDrawItem>?,
      cost: freezed == cost
          ? _value.cost
          : cost // ignore: cast_nullable_to_non_nullable
              as int?,
      requiredDraws: freezed == requiredDraws
          ? _value.requiredDraws
          : requiredDraws // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LuxuryLotteryGiftsImpl implements _LuxuryLotteryGifts {
  const _$LuxuryLotteryGiftsImpl(
      {final List<LuckyDrawItem>? lotteryGifts, this.cost, this.requiredDraws})
      : _lotteryGifts = lotteryGifts;

  factory _$LuxuryLotteryGiftsImpl.fromJson(Map<String, dynamic> json) =>
      _$$LuxuryLotteryGiftsImplFromJson(json);

  final List<LuckyDrawItem>? _lotteryGifts;
  @override
  List<LuckyDrawItem>? get lotteryGifts {
    final value = _lotteryGifts;
    if (value == null) return null;
    if (_lotteryGifts is EqualUnmodifiableListView) return _lotteryGifts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? cost;
  @override
  final int? requiredDraws;

  @override
  String toString() {
    return 'LuxuryLotteryGifts(lotteryGifts: $lotteryGifts, cost: $cost, requiredDraws: $requiredDraws)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LuxuryLotteryGiftsImpl &&
            const DeepCollectionEquality()
                .equals(other._lotteryGifts, _lotteryGifts) &&
            (identical(other.cost, cost) || other.cost == cost) &&
            (identical(other.requiredDraws, requiredDraws) ||
                other.requiredDraws == requiredDraws));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_lotteryGifts), cost, requiredDraws);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LuxuryLotteryGiftsImplCopyWith<_$LuxuryLotteryGiftsImpl> get copyWith =>
      __$$LuxuryLotteryGiftsImplCopyWithImpl<_$LuxuryLotteryGiftsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LuxuryLotteryGiftsImplToJson(
      this,
    );
  }
}

abstract class _LuxuryLotteryGifts implements LuxuryLotteryGifts {
  const factory _LuxuryLotteryGifts(
      {final List<LuckyDrawItem>? lotteryGifts,
      final int? cost,
      final int? requiredDraws}) = _$LuxuryLotteryGiftsImpl;

  factory _LuxuryLotteryGifts.fromJson(Map<String, dynamic> json) =
      _$LuxuryLotteryGiftsImpl.fromJson;

  @override
  List<LuckyDrawItem>? get lotteryGifts;
  @override
  int? get cost;
  @override
  int? get requiredDraws;
  @override
  @JsonKey(ignore: true)
  _$$LuxuryLotteryGiftsImplCopyWith<_$LuxuryLotteryGiftsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
