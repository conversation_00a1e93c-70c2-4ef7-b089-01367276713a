// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'lucky_draw_item_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LuckyDrawItem _$LuckyDrawItemFromJson(Map<String, dynamic> json) {
  return _LuckyDrawItem.fromJson(json);
}

/// @nodoc
mixin _$LuckyDrawItem {
  int? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'giftVO')
  GiftModel? get gift => throw _privateConstructorUsedError;
  LotteryType? get lotteryType => throw _privateConstructorUsedError;
  int? get showOrder => throw _privateConstructorUsedError;
  bool? get isDaily => throw _privateConstructorUsedError;

  /// for history
  String? get userId => throw _privateConstructorUsedError;
  DateTime? get createdTime => throw _privateConstructorUsedError;
  int? get batchIndex => throw _privateConstructorUsedError;
  int? get batchCount => throw _privateConstructorUsedError;
  String? get batchId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LuckyDrawItemCopyWith<LuckyDrawItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LuckyDrawItemCopyWith<$Res> {
  factory $LuckyDrawItemCopyWith(
          LuckyDrawItem value, $Res Function(LuckyDrawItem) then) =
      _$LuckyDrawItemCopyWithImpl<$Res, LuckyDrawItem>;
  @useResult
  $Res call(
      {int? id,
      @JsonKey(name: 'giftVO') GiftModel? gift,
      LotteryType? lotteryType,
      int? showOrder,
      bool? isDaily,
      String? userId,
      DateTime? createdTime,
      int? batchIndex,
      int? batchCount,
      String? batchId});

  $GiftModelCopyWith<$Res>? get gift;
}

/// @nodoc
class _$LuckyDrawItemCopyWithImpl<$Res, $Val extends LuckyDrawItem>
    implements $LuckyDrawItemCopyWith<$Res> {
  _$LuckyDrawItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? gift = freezed,
    Object? lotteryType = freezed,
    Object? showOrder = freezed,
    Object? isDaily = freezed,
    Object? userId = freezed,
    Object? createdTime = freezed,
    Object? batchIndex = freezed,
    Object? batchCount = freezed,
    Object? batchId = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      gift: freezed == gift
          ? _value.gift
          : gift // ignore: cast_nullable_to_non_nullable
              as GiftModel?,
      lotteryType: freezed == lotteryType
          ? _value.lotteryType
          : lotteryType // ignore: cast_nullable_to_non_nullable
              as LotteryType?,
      showOrder: freezed == showOrder
          ? _value.showOrder
          : showOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      isDaily: freezed == isDaily
          ? _value.isDaily
          : isDaily // ignore: cast_nullable_to_non_nullable
              as bool?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      createdTime: freezed == createdTime
          ? _value.createdTime
          : createdTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      batchIndex: freezed == batchIndex
          ? _value.batchIndex
          : batchIndex // ignore: cast_nullable_to_non_nullable
              as int?,
      batchCount: freezed == batchCount
          ? _value.batchCount
          : batchCount // ignore: cast_nullable_to_non_nullable
              as int?,
      batchId: freezed == batchId
          ? _value.batchId
          : batchId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $GiftModelCopyWith<$Res>? get gift {
    if (_value.gift == null) {
      return null;
    }

    return $GiftModelCopyWith<$Res>(_value.gift!, (value) {
      return _then(_value.copyWith(gift: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LuckyDrawItemImplCopyWith<$Res>
    implements $LuckyDrawItemCopyWith<$Res> {
  factory _$$LuckyDrawItemImplCopyWith(
          _$LuckyDrawItemImpl value, $Res Function(_$LuckyDrawItemImpl) then) =
      __$$LuckyDrawItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      @JsonKey(name: 'giftVO') GiftModel? gift,
      LotteryType? lotteryType,
      int? showOrder,
      bool? isDaily,
      String? userId,
      DateTime? createdTime,
      int? batchIndex,
      int? batchCount,
      String? batchId});

  @override
  $GiftModelCopyWith<$Res>? get gift;
}

/// @nodoc
class __$$LuckyDrawItemImplCopyWithImpl<$Res>
    extends _$LuckyDrawItemCopyWithImpl<$Res, _$LuckyDrawItemImpl>
    implements _$$LuckyDrawItemImplCopyWith<$Res> {
  __$$LuckyDrawItemImplCopyWithImpl(
      _$LuckyDrawItemImpl _value, $Res Function(_$LuckyDrawItemImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? gift = freezed,
    Object? lotteryType = freezed,
    Object? showOrder = freezed,
    Object? isDaily = freezed,
    Object? userId = freezed,
    Object? createdTime = freezed,
    Object? batchIndex = freezed,
    Object? batchCount = freezed,
    Object? batchId = freezed,
  }) {
    return _then(_$LuckyDrawItemImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      gift: freezed == gift
          ? _value.gift
          : gift // ignore: cast_nullable_to_non_nullable
              as GiftModel?,
      lotteryType: freezed == lotteryType
          ? _value.lotteryType
          : lotteryType // ignore: cast_nullable_to_non_nullable
              as LotteryType?,
      showOrder: freezed == showOrder
          ? _value.showOrder
          : showOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      isDaily: freezed == isDaily
          ? _value.isDaily
          : isDaily // ignore: cast_nullable_to_non_nullable
              as bool?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      createdTime: freezed == createdTime
          ? _value.createdTime
          : createdTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      batchIndex: freezed == batchIndex
          ? _value.batchIndex
          : batchIndex // ignore: cast_nullable_to_non_nullable
              as int?,
      batchCount: freezed == batchCount
          ? _value.batchCount
          : batchCount // ignore: cast_nullable_to_non_nullable
              as int?,
      batchId: freezed == batchId
          ? _value.batchId
          : batchId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$LuckyDrawItemImpl implements _LuckyDrawItem {
  const _$LuckyDrawItemImpl(
      {this.id,
      @JsonKey(name: 'giftVO') this.gift,
      this.lotteryType,
      this.showOrder,
      this.isDaily,
      this.userId,
      this.createdTime,
      this.batchIndex,
      this.batchCount,
      this.batchId});

  factory _$LuckyDrawItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$LuckyDrawItemImplFromJson(json);

  @override
  final int? id;
  @override
  @JsonKey(name: 'giftVO')
  final GiftModel? gift;
  @override
  final LotteryType? lotteryType;
  @override
  final int? showOrder;
  @override
  final bool? isDaily;

  /// for history
  @override
  final String? userId;
  @override
  final DateTime? createdTime;
  @override
  final int? batchIndex;
  @override
  final int? batchCount;
  @override
  final String? batchId;

  @override
  String toString() {
    return 'LuckyDrawItem(id: $id, gift: $gift, lotteryType: $lotteryType, showOrder: $showOrder, isDaily: $isDaily, userId: $userId, createdTime: $createdTime, batchIndex: $batchIndex, batchCount: $batchCount, batchId: $batchId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LuckyDrawItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.gift, gift) || other.gift == gift) &&
            (identical(other.lotteryType, lotteryType) ||
                other.lotteryType == lotteryType) &&
            (identical(other.showOrder, showOrder) ||
                other.showOrder == showOrder) &&
            (identical(other.isDaily, isDaily) || other.isDaily == isDaily) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.createdTime, createdTime) ||
                other.createdTime == createdTime) &&
            (identical(other.batchIndex, batchIndex) ||
                other.batchIndex == batchIndex) &&
            (identical(other.batchCount, batchCount) ||
                other.batchCount == batchCount) &&
            (identical(other.batchId, batchId) || other.batchId == batchId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, gift, lotteryType, showOrder,
      isDaily, userId, createdTime, batchIndex, batchCount, batchId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LuckyDrawItemImplCopyWith<_$LuckyDrawItemImpl> get copyWith =>
      __$$LuckyDrawItemImplCopyWithImpl<_$LuckyDrawItemImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LuckyDrawItemImplToJson(
      this,
    );
  }
}

abstract class _LuckyDrawItem implements LuckyDrawItem {
  const factory _LuckyDrawItem(
      {final int? id,
      @JsonKey(name: 'giftVO') final GiftModel? gift,
      final LotteryType? lotteryType,
      final int? showOrder,
      final bool? isDaily,
      final String? userId,
      final DateTime? createdTime,
      final int? batchIndex,
      final int? batchCount,
      final String? batchId}) = _$LuckyDrawItemImpl;

  factory _LuckyDrawItem.fromJson(Map<String, dynamic> json) =
      _$LuckyDrawItemImpl.fromJson;

  @override
  int? get id;
  @override
  @JsonKey(name: 'giftVO')
  GiftModel? get gift;
  @override
  LotteryType? get lotteryType;
  @override
  int? get showOrder;
  @override
  bool? get isDaily;
  @override

  /// for history
  String? get userId;
  @override
  DateTime? get createdTime;
  @override
  int? get batchIndex;
  @override
  int? get batchCount;
  @override
  String? get batchId;
  @override
  @JsonKey(ignore: true)
  _$$LuckyDrawItemImplCopyWith<_$LuckyDrawItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
