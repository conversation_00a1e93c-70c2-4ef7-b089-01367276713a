// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'room_access_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RoomAccessInfoImpl _$$RoomAccessInfoImplFromJson(Map<String, dynamic> json) =>
    _$RoomAccessInfoImpl(
      token: json['token'] as String?,
      uid: (json['uid'] as num?)?.toInt(),
      punishment: json['punishmentVO'] == null
          ? null
          : PunishmentModel.fromJson(
              json['punishmentVO'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$RoomAccessInfoImplToJson(
        _$RoomAccessInfoImpl instance) =>
    <String, dynamic>{
      'token': instance.token,
      'uid': instance.uid,
      'punishmentVO': instance.punishment?.toJson(),
    };
