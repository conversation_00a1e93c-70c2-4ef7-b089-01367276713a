// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'room_user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RoomUser _$RoomUserFromJson(Map<String, dynamic> json) {
  return _RoomUser.fromJson(json);
}

/// @nodoc
mixin _$RoomUser {
  String? get userId => throw _privateConstructorUsedError; // user id
  int? get uid => throw _privateConstructorUsedError;
  String? get firstName => throw _privateConstructorUsedError;
  String? get avatarUrl => throw _privateConstructorUsedError;
  int? get lastEnterTime => throw _privateConstructorUsedError;
  int? get level => throw _privateConstructorUsedError; // level
  int? get position => throw _privateConstructorUsedError;
  bool? get isSpeaking => throw _privateConstructorUsedError;
  bool? get isMutedByManager => throw _privateConstructorUsedError;
  bool? get isMutedBySelf => throw _privateConstructorUsedError;
  bool? get isSelected => throw _privateConstructorUsedError;
  AvatarFrameModel? get avatarFrame => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RoomUserCopyWith<RoomUser> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RoomUserCopyWith<$Res> {
  factory $RoomUserCopyWith(RoomUser value, $Res Function(RoomUser) then) =
      _$RoomUserCopyWithImpl<$Res, RoomUser>;
  @useResult
  $Res call(
      {String? userId,
      int? uid,
      String? firstName,
      String? avatarUrl,
      int? lastEnterTime,
      int? level,
      int? position,
      bool? isSpeaking,
      bool? isMutedByManager,
      bool? isMutedBySelf,
      bool? isSelected,
      AvatarFrameModel? avatarFrame});

  $AvatarFrameModelCopyWith<$Res>? get avatarFrame;
}

/// @nodoc
class _$RoomUserCopyWithImpl<$Res, $Val extends RoomUser>
    implements $RoomUserCopyWith<$Res> {
  _$RoomUserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? uid = freezed,
    Object? firstName = freezed,
    Object? avatarUrl = freezed,
    Object? lastEnterTime = freezed,
    Object? level = freezed,
    Object? position = freezed,
    Object? isSpeaking = freezed,
    Object? isMutedByManager = freezed,
    Object? isMutedBySelf = freezed,
    Object? isSelected = freezed,
    Object? avatarFrame = freezed,
  }) {
    return _then(_value.copyWith(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      uid: freezed == uid
          ? _value.uid
          : uid // ignore: cast_nullable_to_non_nullable
              as int?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      lastEnterTime: freezed == lastEnterTime
          ? _value.lastEnterTime
          : lastEnterTime // ignore: cast_nullable_to_non_nullable
              as int?,
      level: freezed == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int?,
      position: freezed == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as int?,
      isSpeaking: freezed == isSpeaking
          ? _value.isSpeaking
          : isSpeaking // ignore: cast_nullable_to_non_nullable
              as bool?,
      isMutedByManager: freezed == isMutedByManager
          ? _value.isMutedByManager
          : isMutedByManager // ignore: cast_nullable_to_non_nullable
              as bool?,
      isMutedBySelf: freezed == isMutedBySelf
          ? _value.isMutedBySelf
          : isMutedBySelf // ignore: cast_nullable_to_non_nullable
              as bool?,
      isSelected: freezed == isSelected
          ? _value.isSelected
          : isSelected // ignore: cast_nullable_to_non_nullable
              as bool?,
      avatarFrame: freezed == avatarFrame
          ? _value.avatarFrame
          : avatarFrame // ignore: cast_nullable_to_non_nullable
              as AvatarFrameModel?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AvatarFrameModelCopyWith<$Res>? get avatarFrame {
    if (_value.avatarFrame == null) {
      return null;
    }

    return $AvatarFrameModelCopyWith<$Res>(_value.avatarFrame!, (value) {
      return _then(_value.copyWith(avatarFrame: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RoomUserImplCopyWith<$Res>
    implements $RoomUserCopyWith<$Res> {
  factory _$$RoomUserImplCopyWith(
          _$RoomUserImpl value, $Res Function(_$RoomUserImpl) then) =
      __$$RoomUserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? userId,
      int? uid,
      String? firstName,
      String? avatarUrl,
      int? lastEnterTime,
      int? level,
      int? position,
      bool? isSpeaking,
      bool? isMutedByManager,
      bool? isMutedBySelf,
      bool? isSelected,
      AvatarFrameModel? avatarFrame});

  @override
  $AvatarFrameModelCopyWith<$Res>? get avatarFrame;
}

/// @nodoc
class __$$RoomUserImplCopyWithImpl<$Res>
    extends _$RoomUserCopyWithImpl<$Res, _$RoomUserImpl>
    implements _$$RoomUserImplCopyWith<$Res> {
  __$$RoomUserImplCopyWithImpl(
      _$RoomUserImpl _value, $Res Function(_$RoomUserImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? uid = freezed,
    Object? firstName = freezed,
    Object? avatarUrl = freezed,
    Object? lastEnterTime = freezed,
    Object? level = freezed,
    Object? position = freezed,
    Object? isSpeaking = freezed,
    Object? isMutedByManager = freezed,
    Object? isMutedBySelf = freezed,
    Object? isSelected = freezed,
    Object? avatarFrame = freezed,
  }) {
    return _then(_$RoomUserImpl(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      uid: freezed == uid
          ? _value.uid
          : uid // ignore: cast_nullable_to_non_nullable
              as int?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      lastEnterTime: freezed == lastEnterTime
          ? _value.lastEnterTime
          : lastEnterTime // ignore: cast_nullable_to_non_nullable
              as int?,
      level: freezed == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int?,
      position: freezed == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as int?,
      isSpeaking: freezed == isSpeaking
          ? _value.isSpeaking
          : isSpeaking // ignore: cast_nullable_to_non_nullable
              as bool?,
      isMutedByManager: freezed == isMutedByManager
          ? _value.isMutedByManager
          : isMutedByManager // ignore: cast_nullable_to_non_nullable
              as bool?,
      isMutedBySelf: freezed == isMutedBySelf
          ? _value.isMutedBySelf
          : isMutedBySelf // ignore: cast_nullable_to_non_nullable
              as bool?,
      isSelected: freezed == isSelected
          ? _value.isSelected
          : isSelected // ignore: cast_nullable_to_non_nullable
              as bool?,
      avatarFrame: freezed == avatarFrame
          ? _value.avatarFrame
          : avatarFrame // ignore: cast_nullable_to_non_nullable
              as AvatarFrameModel?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$RoomUserImpl extends _RoomUser {
  const _$RoomUserImpl(
      {this.userId,
      this.uid,
      this.firstName,
      this.avatarUrl,
      this.lastEnterTime,
      this.level,
      this.position,
      this.isSpeaking,
      this.isMutedByManager,
      this.isMutedBySelf,
      this.isSelected,
      this.avatarFrame})
      : super._();

  factory _$RoomUserImpl.fromJson(Map<String, dynamic> json) =>
      _$$RoomUserImplFromJson(json);

  @override
  final String? userId;
// user id
  @override
  final int? uid;
  @override
  final String? firstName;
  @override
  final String? avatarUrl;
  @override
  final int? lastEnterTime;
  @override
  final int? level;
// level
  @override
  final int? position;
  @override
  final bool? isSpeaking;
  @override
  final bool? isMutedByManager;
  @override
  final bool? isMutedBySelf;
  @override
  final bool? isSelected;
  @override
  final AvatarFrameModel? avatarFrame;

  @override
  String toString() {
    return 'RoomUser(userId: $userId, uid: $uid, firstName: $firstName, avatarUrl: $avatarUrl, lastEnterTime: $lastEnterTime, level: $level, position: $position, isSpeaking: $isSpeaking, isMutedByManager: $isMutedByManager, isMutedBySelf: $isMutedBySelf, isSelected: $isSelected, avatarFrame: $avatarFrame)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RoomUserImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.uid, uid) || other.uid == uid) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.avatarUrl, avatarUrl) ||
                other.avatarUrl == avatarUrl) &&
            (identical(other.lastEnterTime, lastEnterTime) ||
                other.lastEnterTime == lastEnterTime) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.position, position) ||
                other.position == position) &&
            (identical(other.isSpeaking, isSpeaking) ||
                other.isSpeaking == isSpeaking) &&
            (identical(other.isMutedByManager, isMutedByManager) ||
                other.isMutedByManager == isMutedByManager) &&
            (identical(other.isMutedBySelf, isMutedBySelf) ||
                other.isMutedBySelf == isMutedBySelf) &&
            (identical(other.isSelected, isSelected) ||
                other.isSelected == isSelected) &&
            (identical(other.avatarFrame, avatarFrame) ||
                other.avatarFrame == avatarFrame));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      userId,
      uid,
      firstName,
      avatarUrl,
      lastEnterTime,
      level,
      position,
      isSpeaking,
      isMutedByManager,
      isMutedBySelf,
      isSelected,
      avatarFrame);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RoomUserImplCopyWith<_$RoomUserImpl> get copyWith =>
      __$$RoomUserImplCopyWithImpl<_$RoomUserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RoomUserImplToJson(
      this,
    );
  }
}

abstract class _RoomUser extends RoomUser {
  const factory _RoomUser(
      {final String? userId,
      final int? uid,
      final String? firstName,
      final String? avatarUrl,
      final int? lastEnterTime,
      final int? level,
      final int? position,
      final bool? isSpeaking,
      final bool? isMutedByManager,
      final bool? isMutedBySelf,
      final bool? isSelected,
      final AvatarFrameModel? avatarFrame}) = _$RoomUserImpl;
  const _RoomUser._() : super._();

  factory _RoomUser.fromJson(Map<String, dynamic> json) =
      _$RoomUserImpl.fromJson;

  @override
  String? get userId;
  @override // user id
  int? get uid;
  @override
  String? get firstName;
  @override
  String? get avatarUrl;
  @override
  int? get lastEnterTime;
  @override
  int? get level;
  @override // level
  int? get position;
  @override
  bool? get isSpeaking;
  @override
  bool? get isMutedByManager;
  @override
  bool? get isMutedBySelf;
  @override
  bool? get isSelected;
  @override
  AvatarFrameModel? get avatarFrame;
  @override
  @JsonKey(ignore: true)
  _$$RoomUserImplCopyWith<_$RoomUserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
