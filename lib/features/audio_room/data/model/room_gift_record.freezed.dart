// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'room_gift_record.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RoomGiftRecord _$RoomGiftRecordFromJson(Map<String, dynamic> json) {
  return _RoomGiftRecord.fromJson(json);
}

/// @nodoc
mixin _$RoomGiftRecord {
  @JsonKey(name: 'profileVO')
  ProfileModel? get profile => throw _privateConstructorUsedError;
  @JsonKey(name: 'giftVO')
  GiftModel? get gift => throw _privateConstructorUsedError;
  DateTime? get createTime => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RoomGiftRecordCopyWith<RoomGiftRecord> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RoomGiftRecordCopyWith<$Res> {
  factory $RoomGiftRecordCopyWith(
          RoomGiftRecord value, $Res Function(RoomGiftRecord) then) =
      _$RoomGiftRecordCopyWithImpl<$Res, RoomGiftRecord>;
  @useResult
  $Res call(
      {@JsonKey(name: 'profileVO') ProfileModel? profile,
      @JsonKey(name: 'giftVO') GiftModel? gift,
      DateTime? createTime});

  $ProfileModelCopyWith<$Res>? get profile;
  $GiftModelCopyWith<$Res>? get gift;
}

/// @nodoc
class _$RoomGiftRecordCopyWithImpl<$Res, $Val extends RoomGiftRecord>
    implements $RoomGiftRecordCopyWith<$Res> {
  _$RoomGiftRecordCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profile = freezed,
    Object? gift = freezed,
    Object? createTime = freezed,
  }) {
    return _then(_value.copyWith(
      profile: freezed == profile
          ? _value.profile
          : profile // ignore: cast_nullable_to_non_nullable
              as ProfileModel?,
      gift: freezed == gift
          ? _value.gift
          : gift // ignore: cast_nullable_to_non_nullable
              as GiftModel?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ProfileModelCopyWith<$Res>? get profile {
    if (_value.profile == null) {
      return null;
    }

    return $ProfileModelCopyWith<$Res>(_value.profile!, (value) {
      return _then(_value.copyWith(profile: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $GiftModelCopyWith<$Res>? get gift {
    if (_value.gift == null) {
      return null;
    }

    return $GiftModelCopyWith<$Res>(_value.gift!, (value) {
      return _then(_value.copyWith(gift: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RoomGiftRecordImplCopyWith<$Res>
    implements $RoomGiftRecordCopyWith<$Res> {
  factory _$$RoomGiftRecordImplCopyWith(_$RoomGiftRecordImpl value,
          $Res Function(_$RoomGiftRecordImpl) then) =
      __$$RoomGiftRecordImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'profileVO') ProfileModel? profile,
      @JsonKey(name: 'giftVO') GiftModel? gift,
      DateTime? createTime});

  @override
  $ProfileModelCopyWith<$Res>? get profile;
  @override
  $GiftModelCopyWith<$Res>? get gift;
}

/// @nodoc
class __$$RoomGiftRecordImplCopyWithImpl<$Res>
    extends _$RoomGiftRecordCopyWithImpl<$Res, _$RoomGiftRecordImpl>
    implements _$$RoomGiftRecordImplCopyWith<$Res> {
  __$$RoomGiftRecordImplCopyWithImpl(
      _$RoomGiftRecordImpl _value, $Res Function(_$RoomGiftRecordImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profile = freezed,
    Object? gift = freezed,
    Object? createTime = freezed,
  }) {
    return _then(_$RoomGiftRecordImpl(
      profile: freezed == profile
          ? _value.profile
          : profile // ignore: cast_nullable_to_non_nullable
              as ProfileModel?,
      gift: freezed == gift
          ? _value.gift
          : gift // ignore: cast_nullable_to_non_nullable
              as GiftModel?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$RoomGiftRecordImpl implements _RoomGiftRecord {
  const _$RoomGiftRecordImpl(
      {@JsonKey(name: 'profileVO') this.profile,
      @JsonKey(name: 'giftVO') this.gift,
      this.createTime});

  factory _$RoomGiftRecordImpl.fromJson(Map<String, dynamic> json) =>
      _$$RoomGiftRecordImplFromJson(json);

  @override
  @JsonKey(name: 'profileVO')
  final ProfileModel? profile;
  @override
  @JsonKey(name: 'giftVO')
  final GiftModel? gift;
  @override
  final DateTime? createTime;

  @override
  String toString() {
    return 'RoomGiftRecord(profile: $profile, gift: $gift, createTime: $createTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RoomGiftRecordImpl &&
            (identical(other.profile, profile) || other.profile == profile) &&
            (identical(other.gift, gift) || other.gift == gift) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, profile, gift, createTime);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RoomGiftRecordImplCopyWith<_$RoomGiftRecordImpl> get copyWith =>
      __$$RoomGiftRecordImplCopyWithImpl<_$RoomGiftRecordImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RoomGiftRecordImplToJson(
      this,
    );
  }
}

abstract class _RoomGiftRecord implements RoomGiftRecord {
  const factory _RoomGiftRecord(
      {@JsonKey(name: 'profileVO') final ProfileModel? profile,
      @JsonKey(name: 'giftVO') final GiftModel? gift,
      final DateTime? createTime}) = _$RoomGiftRecordImpl;

  factory _RoomGiftRecord.fromJson(Map<String, dynamic> json) =
      _$RoomGiftRecordImpl.fromJson;

  @override
  @JsonKey(name: 'profileVO')
  ProfileModel? get profile;
  @override
  @JsonKey(name: 'giftVO')
  GiftModel? get gift;
  @override
  DateTime? get createTime;
  @override
  @JsonKey(ignore: true)
  _$$RoomGiftRecordImplCopyWith<_$RoomGiftRecordImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
