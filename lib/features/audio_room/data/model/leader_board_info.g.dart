// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'leader_board_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LeaderBoardInfoImpl _$$LeaderBoardInfoImplFromJson(
        Map<String, dynamic> json) =>
    _$LeaderBoardInfoImpl(
      gemValue: (json['gemValue'] as num?)?.toInt(),
      count: (json['count'] as num?)?.toInt(),
      profileInfo: json['profileVO'] == null
          ? null
          : ProfileModel.fromJson(json['profileVO'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$LeaderBoardInfoImplToJson(
        _$LeaderBoardInfoImpl instance) =>
    <String, dynamic>{
      'gemValue': instance.gemValue,
      'count': instance.count,
      'profileVO': instance.profileInfo,
    };
