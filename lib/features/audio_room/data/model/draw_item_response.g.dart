// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'draw_item_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DrawItemsResponseImpl _$$DrawItemsResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$DrawItemsResponseImpl(
      economyLotteryGifts: json['economyLotteryGifts'] == null
          ? null
          : EconomyLotteryGifts.fromJson(
              json['economyLotteryGifts'] as Map<String, dynamic>),
      luxuryLotteryGifts: json['luxuryLotteryGifts'] == null
          ? null
          : LuxuryLotteryGifts.fromJson(
              json['luxuryLotteryGifts'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$DrawItemsResponseImplToJson(
        _$DrawItemsResponseImpl instance) =>
    <String, dynamic>{
      'economyLotteryGifts': instance.economyLotteryGifts,
      'luxuryLotteryGifts': instance.luxuryLotteryGifts,
    };

_$EconomyLotteryGiftsImpl _$$EconomyLotteryGiftsImplFromJson(
        Map<String, dynamic> json) =>
    _$EconomyLotteryGiftsImpl(
      lotteryGifts: (json['lotteryGifts'] as List<dynamic>?)
          ?.map((e) => LuckyDrawItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      cost: (json['cost'] as num?)?.toInt(),
      requiredDraws: (json['requiredDraws'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$EconomyLotteryGiftsImplToJson(
        _$EconomyLotteryGiftsImpl instance) =>
    <String, dynamic>{
      'lotteryGifts': instance.lotteryGifts,
      'cost': instance.cost,
      'requiredDraws': instance.requiredDraws,
    };

_$LuxuryLotteryGiftsImpl _$$LuxuryLotteryGiftsImplFromJson(
        Map<String, dynamic> json) =>
    _$LuxuryLotteryGiftsImpl(
      lotteryGifts: (json['lotteryGifts'] as List<dynamic>?)
          ?.map((e) => LuckyDrawItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      cost: (json['cost'] as num?)?.toInt(),
      requiredDraws: (json['requiredDraws'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$LuxuryLotteryGiftsImplToJson(
        _$LuxuryLotteryGiftsImpl instance) =>
    <String, dynamic>{
      'lotteryGifts': instance.lotteryGifts,
      'cost': instance.cost,
      'requiredDraws': instance.requiredDraws,
    };
