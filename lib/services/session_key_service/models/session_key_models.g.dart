// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'session_key_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SessionKeyConfigImpl _$$SessionKeyConfigImplFromJson(
        Map<String, dynamic> json) =>
    _$SessionKeyConfigImpl(
      maxMessagesPerKey: (json['maxMessagesPerKey'] as num?)?.toInt() ?? 100,
      maxKeyAge: json['maxKeyAge'] == null
          ? const Duration(hours: 24)
          : Duration(microseconds: (json['maxKeyAge'] as num).toInt()),
      gracePeriod: json['gracePeriod'] == null
          ? const Duration(minutes: 10)
          : Duration(microseconds: (json['gracePeriod'] as num).toInt()),
    );

Map<String, dynamic> _$$SessionKeyConfigImplToJson(
        _$SessionKeyConfigImpl instance) =>
    <String, dynamic>{
      'maxMessagesPerKey': instance.maxMessagesPerKey,
      'maxKeyAge': instance.maxKeyAge.inMicroseconds,
      'gracePeriod': instance.gracePeriod.inMicroseconds,
    };

_$HistoricalSessionKeyImpl _$$HistoricalSessionKeyImplFromJson(
        Map<String, dynamic> json) =>
    _$HistoricalSessionKeyImpl(
      sessionKey: json['sessionKey'] as String,
      version: (json['version'] as num).toInt(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      expiresAt: DateTime.parse(json['expiresAt'] as String),
      createdBy: json['createdBy'] as String,
      encryptedKeys: Map<String, String>.from(json['encryptedKeys'] as Map),
    );

Map<String, dynamic> _$$HistoricalSessionKeyImplToJson(
        _$HistoricalSessionKeyImpl instance) =>
    <String, dynamic>{
      'sessionKey': instance.sessionKey,
      'version': instance.version,
      'createdAt': instance.createdAt.toIso8601String(),
      'expiresAt': instance.expiresAt.toIso8601String(),
      'createdBy': instance.createdBy,
      'encryptedKeys': instance.encryptedKeys,
    };

_$SessionKeyMetadataImpl _$$SessionKeyMetadataImplFromJson(
        Map<String, dynamic> json) =>
    _$SessionKeyMetadataImpl(
      sessionKey: json['sessionKey'] as String,
      sessionId: json['sessionId'] as String,
      version: (json['version'] as num).toInt(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      expiresAt: DateTime.parse(json['expiresAt'] as String),
      createdBy: json['createdBy'] as String,
      encryptedKeys: Map<String, String>.from(json['encryptedKeys'] as Map),
      messageCount: (json['messageCount'] as num?)?.toInt() ?? 0,
      historicalKeys: (json['historicalKeys'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(int.parse(k),
                HistoricalSessionKey.fromJson(e as Map<String, dynamic>)),
          ) ??
          const {},
    );

Map<String, dynamic> _$$SessionKeyMetadataImplToJson(
        _$SessionKeyMetadataImpl instance) =>
    <String, dynamic>{
      'sessionKey': instance.sessionKey,
      'sessionId': instance.sessionId,
      'version': instance.version,
      'createdAt': instance.createdAt.toIso8601String(),
      'expiresAt': instance.expiresAt.toIso8601String(),
      'createdBy': instance.createdBy,
      'encryptedKeys': instance.encryptedKeys,
      'messageCount': instance.messageCount,
      'historicalKeys':
          instance.historicalKeys.map((k, e) => MapEntry(k.toString(), e)),
    };

_$SessionKeyRotationRequestImpl _$$SessionKeyRotationRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$SessionKeyRotationRequestImpl(
      sessionId: json['sessionId'] as String,
      currentVersion: (json['currentVersion'] as num).toInt(),
      requestedBy: json['requestedBy'] as String,
    );

Map<String, dynamic> _$$SessionKeyRotationRequestImplToJson(
        _$SessionKeyRotationRequestImpl instance) =>
    <String, dynamic>{
      'sessionId': instance.sessionId,
      'currentVersion': instance.currentVersion,
      'requestedBy': instance.requestedBy,
    };

_$SessionKeyRotationResponseImpl _$$SessionKeyRotationResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$SessionKeyRotationResponseImpl(
      sessionId: json['sessionId'] as String,
      newVersion: (json['newVersion'] as num).toInt(),
      encryptedKeys: Map<String, String>.from(json['encryptedKeys'] as Map),
      createdAt: DateTime.parse(json['createdAt'] as String),
      expiresAt: DateTime.parse(json['expiresAt'] as String),
      createdBy: json['createdBy'] as String,
    );

Map<String, dynamic> _$$SessionKeyRotationResponseImplToJson(
        _$SessionKeyRotationResponseImpl instance) =>
    <String, dynamic>{
      'sessionId': instance.sessionId,
      'newVersion': instance.newVersion,
      'encryptedKeys': instance.encryptedKeys,
      'createdAt': instance.createdAt.toIso8601String(),
      'expiresAt': instance.expiresAt.toIso8601String(),
      'createdBy': instance.createdBy,
    };
