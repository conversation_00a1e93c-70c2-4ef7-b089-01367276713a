// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'gift_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$GiftState {
  AppException? get exception => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  PaginatedResponse<BagGiftModel> get bagGifts =>
      throw _privateConstructorUsedError;
  PaginatedResponse<BagGiftModel> get bagFrames =>
      throw _privateConstructorUsedError;
  PaginatedResponse<GiftModel> get gifts => throw _privateConstructorUsedError;
  PaginatedResponse<GiftModel> get frames => throw _privateConstructorUsedError;
  PaginatedResponse<RoomGiftRecord> get sentGifts =>
      throw _privateConstructorUsedError;
  PaginatedResponse<RoomGiftRecord> get receivedGifts =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $GiftStateCopyWith<GiftState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GiftStateCopyWith<$Res> {
  factory $GiftStateCopyWith(GiftState value, $Res Function(GiftState) then) =
      _$GiftStateCopyWithImpl<$Res, GiftState>;
  @useResult
  $Res call(
      {AppException? exception,
      bool isLoading,
      PaginatedResponse<BagGiftModel> bagGifts,
      PaginatedResponse<BagGiftModel> bagFrames,
      PaginatedResponse<GiftModel> gifts,
      PaginatedResponse<GiftModel> frames,
      PaginatedResponse<RoomGiftRecord> sentGifts,
      PaginatedResponse<RoomGiftRecord> receivedGifts});

  $PaginatedResponseCopyWith<BagGiftModel, $Res> get bagGifts;
  $PaginatedResponseCopyWith<BagGiftModel, $Res> get bagFrames;
  $PaginatedResponseCopyWith<GiftModel, $Res> get gifts;
  $PaginatedResponseCopyWith<GiftModel, $Res> get frames;
  $PaginatedResponseCopyWith<RoomGiftRecord, $Res> get sentGifts;
  $PaginatedResponseCopyWith<RoomGiftRecord, $Res> get receivedGifts;
}

/// @nodoc
class _$GiftStateCopyWithImpl<$Res, $Val extends GiftState>
    implements $GiftStateCopyWith<$Res> {
  _$GiftStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? exception = freezed,
    Object? isLoading = null,
    Object? bagGifts = null,
    Object? bagFrames = null,
    Object? gifts = null,
    Object? frames = null,
    Object? sentGifts = null,
    Object? receivedGifts = null,
  }) {
    return _then(_value.copyWith(
      exception: freezed == exception
          ? _value.exception
          : exception // ignore: cast_nullable_to_non_nullable
              as AppException?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      bagGifts: null == bagGifts
          ? _value.bagGifts
          : bagGifts // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<BagGiftModel>,
      bagFrames: null == bagFrames
          ? _value.bagFrames
          : bagFrames // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<BagGiftModel>,
      gifts: null == gifts
          ? _value.gifts
          : gifts // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<GiftModel>,
      frames: null == frames
          ? _value.frames
          : frames // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<GiftModel>,
      sentGifts: null == sentGifts
          ? _value.sentGifts
          : sentGifts // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<RoomGiftRecord>,
      receivedGifts: null == receivedGifts
          ? _value.receivedGifts
          : receivedGifts // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<RoomGiftRecord>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginatedResponseCopyWith<BagGiftModel, $Res> get bagGifts {
    return $PaginatedResponseCopyWith<BagGiftModel, $Res>(_value.bagGifts,
        (value) {
      return _then(_value.copyWith(bagGifts: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginatedResponseCopyWith<BagGiftModel, $Res> get bagFrames {
    return $PaginatedResponseCopyWith<BagGiftModel, $Res>(_value.bagFrames,
        (value) {
      return _then(_value.copyWith(bagFrames: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginatedResponseCopyWith<GiftModel, $Res> get gifts {
    return $PaginatedResponseCopyWith<GiftModel, $Res>(_value.gifts, (value) {
      return _then(_value.copyWith(gifts: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginatedResponseCopyWith<GiftModel, $Res> get frames {
    return $PaginatedResponseCopyWith<GiftModel, $Res>(_value.frames, (value) {
      return _then(_value.copyWith(frames: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginatedResponseCopyWith<RoomGiftRecord, $Res> get sentGifts {
    return $PaginatedResponseCopyWith<RoomGiftRecord, $Res>(_value.sentGifts,
        (value) {
      return _then(_value.copyWith(sentGifts: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $PaginatedResponseCopyWith<RoomGiftRecord, $Res> get receivedGifts {
    return $PaginatedResponseCopyWith<RoomGiftRecord, $Res>(
        _value.receivedGifts, (value) {
      return _then(_value.copyWith(receivedGifts: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$GiftStateImplCopyWith<$Res>
    implements $GiftStateCopyWith<$Res> {
  factory _$$GiftStateImplCopyWith(
          _$GiftStateImpl value, $Res Function(_$GiftStateImpl) then) =
      __$$GiftStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {AppException? exception,
      bool isLoading,
      PaginatedResponse<BagGiftModel> bagGifts,
      PaginatedResponse<BagGiftModel> bagFrames,
      PaginatedResponse<GiftModel> gifts,
      PaginatedResponse<GiftModel> frames,
      PaginatedResponse<RoomGiftRecord> sentGifts,
      PaginatedResponse<RoomGiftRecord> receivedGifts});

  @override
  $PaginatedResponseCopyWith<BagGiftModel, $Res> get bagGifts;
  @override
  $PaginatedResponseCopyWith<BagGiftModel, $Res> get bagFrames;
  @override
  $PaginatedResponseCopyWith<GiftModel, $Res> get gifts;
  @override
  $PaginatedResponseCopyWith<GiftModel, $Res> get frames;
  @override
  $PaginatedResponseCopyWith<RoomGiftRecord, $Res> get sentGifts;
  @override
  $PaginatedResponseCopyWith<RoomGiftRecord, $Res> get receivedGifts;
}

/// @nodoc
class __$$GiftStateImplCopyWithImpl<$Res>
    extends _$GiftStateCopyWithImpl<$Res, _$GiftStateImpl>
    implements _$$GiftStateImplCopyWith<$Res> {
  __$$GiftStateImplCopyWithImpl(
      _$GiftStateImpl _value, $Res Function(_$GiftStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? exception = freezed,
    Object? isLoading = null,
    Object? bagGifts = null,
    Object? bagFrames = null,
    Object? gifts = null,
    Object? frames = null,
    Object? sentGifts = null,
    Object? receivedGifts = null,
  }) {
    return _then(_$GiftStateImpl(
      exception: freezed == exception
          ? _value.exception
          : exception // ignore: cast_nullable_to_non_nullable
              as AppException?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      bagGifts: null == bagGifts
          ? _value.bagGifts
          : bagGifts // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<BagGiftModel>,
      bagFrames: null == bagFrames
          ? _value.bagFrames
          : bagFrames // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<BagGiftModel>,
      gifts: null == gifts
          ? _value.gifts
          : gifts // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<GiftModel>,
      frames: null == frames
          ? _value.frames
          : frames // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<GiftModel>,
      sentGifts: null == sentGifts
          ? _value.sentGifts
          : sentGifts // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<RoomGiftRecord>,
      receivedGifts: null == receivedGifts
          ? _value.receivedGifts
          : receivedGifts // ignore: cast_nullable_to_non_nullable
              as PaginatedResponse<RoomGiftRecord>,
    ));
  }
}

/// @nodoc

class _$GiftStateImpl extends _GiftState {
  const _$GiftStateImpl(
      {this.exception,
      this.isLoading = false,
      this.bagGifts = const PaginatedResponse<BagGiftModel>(),
      this.bagFrames = const PaginatedResponse<BagGiftModel>(),
      this.gifts = const PaginatedResponse<GiftModel>(),
      this.frames = const PaginatedResponse<GiftModel>(),
      this.sentGifts = const PaginatedResponse<RoomGiftRecord>(),
      this.receivedGifts = const PaginatedResponse<RoomGiftRecord>()})
      : super._();

  @override
  final AppException? exception;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final PaginatedResponse<BagGiftModel> bagGifts;
  @override
  @JsonKey()
  final PaginatedResponse<BagGiftModel> bagFrames;
  @override
  @JsonKey()
  final PaginatedResponse<GiftModel> gifts;
  @override
  @JsonKey()
  final PaginatedResponse<GiftModel> frames;
  @override
  @JsonKey()
  final PaginatedResponse<RoomGiftRecord> sentGifts;
  @override
  @JsonKey()
  final PaginatedResponse<RoomGiftRecord> receivedGifts;

  @override
  String toString() {
    return 'GiftState(exception: $exception, isLoading: $isLoading, bagGifts: $bagGifts, bagFrames: $bagFrames, gifts: $gifts, frames: $frames, sentGifts: $sentGifts, receivedGifts: $receivedGifts)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GiftStateImpl &&
            (identical(other.exception, exception) ||
                other.exception == exception) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.bagGifts, bagGifts) ||
                other.bagGifts == bagGifts) &&
            (identical(other.bagFrames, bagFrames) ||
                other.bagFrames == bagFrames) &&
            (identical(other.gifts, gifts) || other.gifts == gifts) &&
            (identical(other.frames, frames) || other.frames == frames) &&
            (identical(other.sentGifts, sentGifts) ||
                other.sentGifts == sentGifts) &&
            (identical(other.receivedGifts, receivedGifts) ||
                other.receivedGifts == receivedGifts));
  }

  @override
  int get hashCode => Object.hash(runtimeType, exception, isLoading, bagGifts,
      bagFrames, gifts, frames, sentGifts, receivedGifts);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GiftStateImplCopyWith<_$GiftStateImpl> get copyWith =>
      __$$GiftStateImplCopyWithImpl<_$GiftStateImpl>(this, _$identity);
}

abstract class _GiftState extends GiftState {
  const factory _GiftState(
      {final AppException? exception,
      final bool isLoading,
      final PaginatedResponse<BagGiftModel> bagGifts,
      final PaginatedResponse<BagGiftModel> bagFrames,
      final PaginatedResponse<GiftModel> gifts,
      final PaginatedResponse<GiftModel> frames,
      final PaginatedResponse<RoomGiftRecord> sentGifts,
      final PaginatedResponse<RoomGiftRecord> receivedGifts}) = _$GiftStateImpl;
  const _GiftState._() : super._();

  @override
  AppException? get exception;
  @override
  bool get isLoading;
  @override
  PaginatedResponse<BagGiftModel> get bagGifts;
  @override
  PaginatedResponse<BagGiftModel> get bagFrames;
  @override
  PaginatedResponse<GiftModel> get gifts;
  @override
  PaginatedResponse<GiftModel> get frames;
  @override
  PaginatedResponse<RoomGiftRecord> get sentGifts;
  @override
  PaginatedResponse<RoomGiftRecord> get receivedGifts;
  @override
  @JsonKey(ignore: true)
  _$$GiftStateImplCopyWith<_$GiftStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
