// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'gift_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

GiftModel _$GiftModelFromJson(Map<String, dynamic> json) {
  return _GiftModel.fromJson(json);
}

/// @nodoc
mixin _$GiftModel {
  int? get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  String? get svgaUrl => throw _privateConstructorUsedError;
  int? get price => throw _privateConstructorUsedError;
  int? get showOrder => throw _privateConstructorUsedError;
  GiftType? get giftType => throw _privateConstructorUsedError;
  bool? get isFree => throw _privateConstructorUsedError;
  int? get expiryDay => throw _privateConstructorUsedError;
  bool? get isMystery => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GiftModelCopyWith<GiftModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GiftModelCopyWith<$Res> {
  factory $GiftModelCopyWith(GiftModel value, $Res Function(GiftModel) then) =
      _$GiftModelCopyWithImpl<$Res, GiftModel>;
  @useResult
  $Res call(
      {int? id,
      String? name,
      String? imageUrl,
      String? svgaUrl,
      int? price,
      int? showOrder,
      GiftType? giftType,
      bool? isFree,
      int? expiryDay,
      bool? isMystery});
}

/// @nodoc
class _$GiftModelCopyWithImpl<$Res, $Val extends GiftModel>
    implements $GiftModelCopyWith<$Res> {
  _$GiftModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? imageUrl = freezed,
    Object? svgaUrl = freezed,
    Object? price = freezed,
    Object? showOrder = freezed,
    Object? giftType = freezed,
    Object? isFree = freezed,
    Object? expiryDay = freezed,
    Object? isMystery = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      svgaUrl: freezed == svgaUrl
          ? _value.svgaUrl
          : svgaUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int?,
      showOrder: freezed == showOrder
          ? _value.showOrder
          : showOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      giftType: freezed == giftType
          ? _value.giftType
          : giftType // ignore: cast_nullable_to_non_nullable
              as GiftType?,
      isFree: freezed == isFree
          ? _value.isFree
          : isFree // ignore: cast_nullable_to_non_nullable
              as bool?,
      expiryDay: freezed == expiryDay
          ? _value.expiryDay
          : expiryDay // ignore: cast_nullable_to_non_nullable
              as int?,
      isMystery: freezed == isMystery
          ? _value.isMystery
          : isMystery // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GiftModelImplCopyWith<$Res>
    implements $GiftModelCopyWith<$Res> {
  factory _$$GiftModelImplCopyWith(
          _$GiftModelImpl value, $Res Function(_$GiftModelImpl) then) =
      __$$GiftModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      String? name,
      String? imageUrl,
      String? svgaUrl,
      int? price,
      int? showOrder,
      GiftType? giftType,
      bool? isFree,
      int? expiryDay,
      bool? isMystery});
}

/// @nodoc
class __$$GiftModelImplCopyWithImpl<$Res>
    extends _$GiftModelCopyWithImpl<$Res, _$GiftModelImpl>
    implements _$$GiftModelImplCopyWith<$Res> {
  __$$GiftModelImplCopyWithImpl(
      _$GiftModelImpl _value, $Res Function(_$GiftModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? imageUrl = freezed,
    Object? svgaUrl = freezed,
    Object? price = freezed,
    Object? showOrder = freezed,
    Object? giftType = freezed,
    Object? isFree = freezed,
    Object? expiryDay = freezed,
    Object? isMystery = freezed,
  }) {
    return _then(_$GiftModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      svgaUrl: freezed == svgaUrl
          ? _value.svgaUrl
          : svgaUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int?,
      showOrder: freezed == showOrder
          ? _value.showOrder
          : showOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      giftType: freezed == giftType
          ? _value.giftType
          : giftType // ignore: cast_nullable_to_non_nullable
              as GiftType?,
      isFree: freezed == isFree
          ? _value.isFree
          : isFree // ignore: cast_nullable_to_non_nullable
              as bool?,
      expiryDay: freezed == expiryDay
          ? _value.expiryDay
          : expiryDay // ignore: cast_nullable_to_non_nullable
              as int?,
      isMystery: freezed == isMystery
          ? _value.isMystery
          : isMystery // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$GiftModelImpl extends _GiftModel {
  const _$GiftModelImpl(
      {this.id,
      this.name,
      this.imageUrl,
      this.svgaUrl,
      this.price,
      this.showOrder,
      this.giftType,
      this.isFree,
      this.expiryDay,
      this.isMystery})
      : super._();

  factory _$GiftModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$GiftModelImplFromJson(json);

  @override
  final int? id;
  @override
  final String? name;
  @override
  final String? imageUrl;
  @override
  final String? svgaUrl;
  @override
  final int? price;
  @override
  final int? showOrder;
  @override
  final GiftType? giftType;
  @override
  final bool? isFree;
  @override
  final int? expiryDay;
  @override
  final bool? isMystery;

  @override
  String toString() {
    return 'GiftModel(id: $id, name: $name, imageUrl: $imageUrl, svgaUrl: $svgaUrl, price: $price, showOrder: $showOrder, giftType: $giftType, isFree: $isFree, expiryDay: $expiryDay, isMystery: $isMystery)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GiftModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.svgaUrl, svgaUrl) || other.svgaUrl == svgaUrl) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.showOrder, showOrder) ||
                other.showOrder == showOrder) &&
            (identical(other.giftType, giftType) ||
                other.giftType == giftType) &&
            (identical(other.isFree, isFree) || other.isFree == isFree) &&
            (identical(other.expiryDay, expiryDay) ||
                other.expiryDay == expiryDay) &&
            (identical(other.isMystery, isMystery) ||
                other.isMystery == isMystery));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, imageUrl, svgaUrl,
      price, showOrder, giftType, isFree, expiryDay, isMystery);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GiftModelImplCopyWith<_$GiftModelImpl> get copyWith =>
      __$$GiftModelImplCopyWithImpl<_$GiftModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GiftModelImplToJson(
      this,
    );
  }
}

abstract class _GiftModel extends GiftModel {
  const factory _GiftModel(
      {final int? id,
      final String? name,
      final String? imageUrl,
      final String? svgaUrl,
      final int? price,
      final int? showOrder,
      final GiftType? giftType,
      final bool? isFree,
      final int? expiryDay,
      final bool? isMystery}) = _$GiftModelImpl;
  const _GiftModel._() : super._();

  factory _GiftModel.fromJson(Map<String, dynamic> json) =
      _$GiftModelImpl.fromJson;

  @override
  int? get id;
  @override
  String? get name;
  @override
  String? get imageUrl;
  @override
  String? get svgaUrl;
  @override
  int? get price;
  @override
  int? get showOrder;
  @override
  GiftType? get giftType;
  @override
  bool? get isFree;
  @override
  int? get expiryDay;
  @override
  bool? get isMystery;
  @override
  @JsonKey(ignore: true)
  _$$GiftModelImplCopyWith<_$GiftModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
