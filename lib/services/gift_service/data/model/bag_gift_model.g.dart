// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bag_gift_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BagGiftModelImpl _$$BagGiftModelImplFromJson(Map<String, dynamic> json) =>
    _$BagGiftModelImpl(
      id: (json['id'] as num?)?.toInt(),
      userId: json['userId'] as String?,
      giftId: (json['giftId'] as num?)?.toInt(),
      giftType: $enumDecodeNullable(_$GiftTypeEnumMap, json['giftType']),
      source: json['source'] as String?,
      giftStatus: json['giftStatus'] as String?,
      createTime: json['createTime'] == null
          ? null
          : DateTime.parse(json['createTime'] as String),
      expiryTime: json['expiryTime'] == null
          ? null
          : DateTime.parse(json['expiryTime'] as String),
      activateExpireTime: json['activateExpireTime'] == null
          ? null
          : DateTime.parse(json['activateExpireTime'] as String),
      expiryDay: (json['expiryDay'] as num?)?.toInt(),
      name: json['name'] as String?,
      imageUrl: json['imageUrl'] as String?,
      svgaUrl: json['svgaUrl'] as String?,
      price: (json['price'] as num?)?.toInt(),
      giftFormat: json['giftFormat'] as String?,
    );

Map<String, dynamic> _$$BagGiftModelImplToJson(_$BagGiftModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'giftId': instance.giftId,
      'giftType': _$GiftTypeEnumMap[instance.giftType],
      'source': instance.source,
      'giftStatus': instance.giftStatus,
      'createTime': instance.createTime?.toIso8601String(),
      'expiryTime': instance.expiryTime?.toIso8601String(),
      'activateExpireTime': instance.activateExpireTime?.toIso8601String(),
      'expiryDay': instance.expiryDay,
      'name': instance.name,
      'imageUrl': instance.imageUrl,
      'svgaUrl': instance.svgaUrl,
      'price': instance.price,
      'giftFormat': instance.giftFormat,
    };

const _$GiftTypeEnumMap = {
  GiftType.gift: 'gift',
  GiftType.frame: 'avatar_frame',
};
