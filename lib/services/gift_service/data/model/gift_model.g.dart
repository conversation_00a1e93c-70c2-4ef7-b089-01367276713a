// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gift_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$GiftModelImpl _$$GiftModelImplFromJson(Map<String, dynamic> json) =>
    _$GiftModelImpl(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      imageUrl: json['imageUrl'] as String?,
      svgaUrl: json['svgaUrl'] as String?,
      price: (json['price'] as num?)?.toInt(),
      showOrder: (json['showOrder'] as num?)?.toInt(),
      giftType: $enumDecodeNullable(_$GiftTypeEnumMap, json['giftType']),
      isFree: json['isFree'] as bool?,
      expiryDay: (json['expiryDay'] as num?)?.toInt(),
      isMystery: json['isMystery'] as bool?,
    );

Map<String, dynamic> _$$GiftModelImplToJson(_$GiftModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'imageUrl': instance.imageUrl,
      'svgaUrl': instance.svgaUrl,
      'price': instance.price,
      'showOrder': instance.showOrder,
      'giftType': _$GiftTypeEnumMap[instance.giftType],
      'isFree': instance.isFree,
      'expiryDay': instance.expiryDay,
      'isMystery': instance.isMystery,
    };

const _$GiftTypeEnumMap = {
  GiftType.gift: 'gift',
  GiftType.frame: 'avatar_frame',
};
