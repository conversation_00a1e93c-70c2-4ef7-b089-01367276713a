// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bag_gift_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BagGiftModel _$BagGiftModelFromJson(Map<String, dynamic> json) {
  return _BagGiftModel.fromJson(json);
}

/// @nodoc
mixin _$BagGiftModel {
  int? get id => throw _privateConstructorUsedError;
  String? get userId => throw _privateConstructorUsedError;
  int? get giftId => throw _privateConstructorUsedError;
  GiftType? get giftType => throw _privateConstructorUsedError;
  String? get source => throw _privateConstructorUsedError;
  String? get giftStatus => throw _privateConstructorUsedError;
  DateTime? get createTime => throw _privateConstructorUsedError;
  DateTime? get expiryTime => throw _privateConstructorUsedError;
  DateTime? get activateExpireTime => throw _privateConstructorUsedError;
  int? get expiryDay => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  String? get svgaUrl => throw _privateConstructorUsedError;
  int? get price => throw _privateConstructorUsedError;
  String? get giftFormat => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BagGiftModelCopyWith<BagGiftModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BagGiftModelCopyWith<$Res> {
  factory $BagGiftModelCopyWith(
          BagGiftModel value, $Res Function(BagGiftModel) then) =
      _$BagGiftModelCopyWithImpl<$Res, BagGiftModel>;
  @useResult
  $Res call(
      {int? id,
      String? userId,
      int? giftId,
      GiftType? giftType,
      String? source,
      String? giftStatus,
      DateTime? createTime,
      DateTime? expiryTime,
      DateTime? activateExpireTime,
      int? expiryDay,
      String? name,
      String? imageUrl,
      String? svgaUrl,
      int? price,
      String? giftFormat});
}

/// @nodoc
class _$BagGiftModelCopyWithImpl<$Res, $Val extends BagGiftModel>
    implements $BagGiftModelCopyWith<$Res> {
  _$BagGiftModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? userId = freezed,
    Object? giftId = freezed,
    Object? giftType = freezed,
    Object? source = freezed,
    Object? giftStatus = freezed,
    Object? createTime = freezed,
    Object? expiryTime = freezed,
    Object? activateExpireTime = freezed,
    Object? expiryDay = freezed,
    Object? name = freezed,
    Object? imageUrl = freezed,
    Object? svgaUrl = freezed,
    Object? price = freezed,
    Object? giftFormat = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      giftId: freezed == giftId
          ? _value.giftId
          : giftId // ignore: cast_nullable_to_non_nullable
              as int?,
      giftType: freezed == giftType
          ? _value.giftType
          : giftType // ignore: cast_nullable_to_non_nullable
              as GiftType?,
      source: freezed == source
          ? _value.source
          : source // ignore: cast_nullable_to_non_nullable
              as String?,
      giftStatus: freezed == giftStatus
          ? _value.giftStatus
          : giftStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      expiryTime: freezed == expiryTime
          ? _value.expiryTime
          : expiryTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      activateExpireTime: freezed == activateExpireTime
          ? _value.activateExpireTime
          : activateExpireTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      expiryDay: freezed == expiryDay
          ? _value.expiryDay
          : expiryDay // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      svgaUrl: freezed == svgaUrl
          ? _value.svgaUrl
          : svgaUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int?,
      giftFormat: freezed == giftFormat
          ? _value.giftFormat
          : giftFormat // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BagGiftModelImplCopyWith<$Res>
    implements $BagGiftModelCopyWith<$Res> {
  factory _$$BagGiftModelImplCopyWith(
          _$BagGiftModelImpl value, $Res Function(_$BagGiftModelImpl) then) =
      __$$BagGiftModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      String? userId,
      int? giftId,
      GiftType? giftType,
      String? source,
      String? giftStatus,
      DateTime? createTime,
      DateTime? expiryTime,
      DateTime? activateExpireTime,
      int? expiryDay,
      String? name,
      String? imageUrl,
      String? svgaUrl,
      int? price,
      String? giftFormat});
}

/// @nodoc
class __$$BagGiftModelImplCopyWithImpl<$Res>
    extends _$BagGiftModelCopyWithImpl<$Res, _$BagGiftModelImpl>
    implements _$$BagGiftModelImplCopyWith<$Res> {
  __$$BagGiftModelImplCopyWithImpl(
      _$BagGiftModelImpl _value, $Res Function(_$BagGiftModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? userId = freezed,
    Object? giftId = freezed,
    Object? giftType = freezed,
    Object? source = freezed,
    Object? giftStatus = freezed,
    Object? createTime = freezed,
    Object? expiryTime = freezed,
    Object? activateExpireTime = freezed,
    Object? expiryDay = freezed,
    Object? name = freezed,
    Object? imageUrl = freezed,
    Object? svgaUrl = freezed,
    Object? price = freezed,
    Object? giftFormat = freezed,
  }) {
    return _then(_$BagGiftModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      giftId: freezed == giftId
          ? _value.giftId
          : giftId // ignore: cast_nullable_to_non_nullable
              as int?,
      giftType: freezed == giftType
          ? _value.giftType
          : giftType // ignore: cast_nullable_to_non_nullable
              as GiftType?,
      source: freezed == source
          ? _value.source
          : source // ignore: cast_nullable_to_non_nullable
              as String?,
      giftStatus: freezed == giftStatus
          ? _value.giftStatus
          : giftStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      expiryTime: freezed == expiryTime
          ? _value.expiryTime
          : expiryTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      activateExpireTime: freezed == activateExpireTime
          ? _value.activateExpireTime
          : activateExpireTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      expiryDay: freezed == expiryDay
          ? _value.expiryDay
          : expiryDay // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      svgaUrl: freezed == svgaUrl
          ? _value.svgaUrl
          : svgaUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int?,
      giftFormat: freezed == giftFormat
          ? _value.giftFormat
          : giftFormat // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$BagGiftModelImpl extends _BagGiftModel {
  const _$BagGiftModelImpl(
      {this.id,
      this.userId,
      this.giftId,
      this.giftType,
      this.source,
      this.giftStatus,
      this.createTime,
      this.expiryTime,
      this.activateExpireTime,
      this.expiryDay,
      this.name,
      this.imageUrl,
      this.svgaUrl,
      this.price,
      this.giftFormat})
      : super._();

  factory _$BagGiftModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$BagGiftModelImplFromJson(json);

  @override
  final int? id;
  @override
  final String? userId;
  @override
  final int? giftId;
  @override
  final GiftType? giftType;
  @override
  final String? source;
  @override
  final String? giftStatus;
  @override
  final DateTime? createTime;
  @override
  final DateTime? expiryTime;
  @override
  final DateTime? activateExpireTime;
  @override
  final int? expiryDay;
  @override
  final String? name;
  @override
  final String? imageUrl;
  @override
  final String? svgaUrl;
  @override
  final int? price;
  @override
  final String? giftFormat;

  @override
  String toString() {
    return 'BagGiftModel(id: $id, userId: $userId, giftId: $giftId, giftType: $giftType, source: $source, giftStatus: $giftStatus, createTime: $createTime, expiryTime: $expiryTime, activateExpireTime: $activateExpireTime, expiryDay: $expiryDay, name: $name, imageUrl: $imageUrl, svgaUrl: $svgaUrl, price: $price, giftFormat: $giftFormat)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BagGiftModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.giftId, giftId) || other.giftId == giftId) &&
            (identical(other.giftType, giftType) ||
                other.giftType == giftType) &&
            (identical(other.source, source) || other.source == source) &&
            (identical(other.giftStatus, giftStatus) ||
                other.giftStatus == giftStatus) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.expiryTime, expiryTime) ||
                other.expiryTime == expiryTime) &&
            (identical(other.activateExpireTime, activateExpireTime) ||
                other.activateExpireTime == activateExpireTime) &&
            (identical(other.expiryDay, expiryDay) ||
                other.expiryDay == expiryDay) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.svgaUrl, svgaUrl) || other.svgaUrl == svgaUrl) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.giftFormat, giftFormat) ||
                other.giftFormat == giftFormat));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      giftId,
      giftType,
      source,
      giftStatus,
      createTime,
      expiryTime,
      activateExpireTime,
      expiryDay,
      name,
      imageUrl,
      svgaUrl,
      price,
      giftFormat);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$BagGiftModelImplCopyWith<_$BagGiftModelImpl> get copyWith =>
      __$$BagGiftModelImplCopyWithImpl<_$BagGiftModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BagGiftModelImplToJson(
      this,
    );
  }
}

abstract class _BagGiftModel extends BagGiftModel {
  const factory _BagGiftModel(
      {final int? id,
      final String? userId,
      final int? giftId,
      final GiftType? giftType,
      final String? source,
      final String? giftStatus,
      final DateTime? createTime,
      final DateTime? expiryTime,
      final DateTime? activateExpireTime,
      final int? expiryDay,
      final String? name,
      final String? imageUrl,
      final String? svgaUrl,
      final int? price,
      final String? giftFormat}) = _$BagGiftModelImpl;
  const _BagGiftModel._() : super._();

  factory _BagGiftModel.fromJson(Map<String, dynamic> json) =
      _$BagGiftModelImpl.fromJson;

  @override
  int? get id;
  @override
  String? get userId;
  @override
  int? get giftId;
  @override
  GiftType? get giftType;
  @override
  String? get source;
  @override
  String? get giftStatus;
  @override
  DateTime? get createTime;
  @override
  DateTime? get expiryTime;
  @override
  DateTime? get activateExpireTime;
  @override
  int? get expiryDay;
  @override
  String? get name;
  @override
  String? get imageUrl;
  @override
  String? get svgaUrl;
  @override
  int? get price;
  @override
  String? get giftFormat;
  @override
  @JsonKey(ignore: true)
  _$$BagGiftModelImplCopyWith<_$BagGiftModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
