// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'receive_gift_list_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ReceiveGiftListItem _$ReceiveGiftListItemFromJson(Map<String, dynamic> json) {
  return _ReceiveGiftListItem.fromJson(json);
}

/// @nodoc
mixin _$ReceiveGiftListItem {
  List<RoomGiftRecord>? get userReceivedGifts =>
      throw _privateConstructorUsedError;
  int? get popularity => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ReceiveGiftListItemCopyWith<ReceiveGiftListItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReceiveGiftListItemCopyWith<$Res> {
  factory $ReceiveGiftListItemCopyWith(
          ReceiveGiftListItem value, $Res Function(ReceiveGiftListItem) then) =
      _$ReceiveGiftListItemCopyWithImpl<$Res, ReceiveGiftListItem>;
  @useResult
  $Res call({List<RoomGiftRecord>? userReceivedGifts, int? popularity});
}

/// @nodoc
class _$ReceiveGiftListItemCopyWithImpl<$Res, $Val extends ReceiveGiftListItem>
    implements $ReceiveGiftListItemCopyWith<$Res> {
  _$ReceiveGiftListItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userReceivedGifts = freezed,
    Object? popularity = freezed,
  }) {
    return _then(_value.copyWith(
      userReceivedGifts: freezed == userReceivedGifts
          ? _value.userReceivedGifts
          : userReceivedGifts // ignore: cast_nullable_to_non_nullable
              as List<RoomGiftRecord>?,
      popularity: freezed == popularity
          ? _value.popularity
          : popularity // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReceiveGiftListItemImplCopyWith<$Res>
    implements $ReceiveGiftListItemCopyWith<$Res> {
  factory _$$ReceiveGiftListItemImplCopyWith(_$ReceiveGiftListItemImpl value,
          $Res Function(_$ReceiveGiftListItemImpl) then) =
      __$$ReceiveGiftListItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<RoomGiftRecord>? userReceivedGifts, int? popularity});
}

/// @nodoc
class __$$ReceiveGiftListItemImplCopyWithImpl<$Res>
    extends _$ReceiveGiftListItemCopyWithImpl<$Res, _$ReceiveGiftListItemImpl>
    implements _$$ReceiveGiftListItemImplCopyWith<$Res> {
  __$$ReceiveGiftListItemImplCopyWithImpl(_$ReceiveGiftListItemImpl _value,
      $Res Function(_$ReceiveGiftListItemImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userReceivedGifts = freezed,
    Object? popularity = freezed,
  }) {
    return _then(_$ReceiveGiftListItemImpl(
      userReceivedGifts: freezed == userReceivedGifts
          ? _value._userReceivedGifts
          : userReceivedGifts // ignore: cast_nullable_to_non_nullable
              as List<RoomGiftRecord>?,
      popularity: freezed == popularity
          ? _value.popularity
          : popularity // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$ReceiveGiftListItemImpl implements _ReceiveGiftListItem {
  const _$ReceiveGiftListItemImpl(
      {final List<RoomGiftRecord>? userReceivedGifts, this.popularity})
      : _userReceivedGifts = userReceivedGifts;

  factory _$ReceiveGiftListItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReceiveGiftListItemImplFromJson(json);

  final List<RoomGiftRecord>? _userReceivedGifts;
  @override
  List<RoomGiftRecord>? get userReceivedGifts {
    final value = _userReceivedGifts;
    if (value == null) return null;
    if (_userReceivedGifts is EqualUnmodifiableListView)
      return _userReceivedGifts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? popularity;

  @override
  String toString() {
    return 'ReceiveGiftListItem(userReceivedGifts: $userReceivedGifts, popularity: $popularity)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReceiveGiftListItemImpl &&
            const DeepCollectionEquality()
                .equals(other._userReceivedGifts, _userReceivedGifts) &&
            (identical(other.popularity, popularity) ||
                other.popularity == popularity));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_userReceivedGifts), popularity);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ReceiveGiftListItemImplCopyWith<_$ReceiveGiftListItemImpl> get copyWith =>
      __$$ReceiveGiftListItemImplCopyWithImpl<_$ReceiveGiftListItemImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReceiveGiftListItemImplToJson(
      this,
    );
  }
}

abstract class _ReceiveGiftListItem implements ReceiveGiftListItem {
  const factory _ReceiveGiftListItem(
      {final List<RoomGiftRecord>? userReceivedGifts,
      final int? popularity}) = _$ReceiveGiftListItemImpl;

  factory _ReceiveGiftListItem.fromJson(Map<String, dynamic> json) =
      _$ReceiveGiftListItemImpl.fromJson;

  @override
  List<RoomGiftRecord>? get userReceivedGifts;
  @override
  int? get popularity;
  @override
  @JsonKey(ignore: true)
  _$$ReceiveGiftListItemImplCopyWith<_$ReceiveGiftListItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
