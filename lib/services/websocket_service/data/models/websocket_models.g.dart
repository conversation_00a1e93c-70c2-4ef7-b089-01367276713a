// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'websocket_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$WebSocketMessageModelImpl _$$WebSocketMessageModelImplFromJson(
        Map<String, dynamic> json) =>
    _$WebSocketMessageModelImpl(
      data: json['data'],
      type: $enumDecode(_$WebSocketMessageTypeEnumMap, json['type']),
    );

Map<String, dynamic> _$$WebSocketMessageModelImplToJson(
        _$WebSocketMessageModelImpl instance) =>
    <String, dynamic>{
      'data': instance.data,
      'type': _$WebSocketMessageTypeEnumMap[instance.type]!,
    };

const _$WebSocketMessageTypeEnumMap = {
  WebSocketMessageType.text: 'text',
  WebSocketMessageType.json: 'json',
  WebSocketMessageType.binary: 'binary',
};

_$WebSocketErrorModelImpl _$$WebSocketErrorModelImplFromJson(
        Map<String, dynamic> json) =>
    _$WebSocketErrorModelImpl(
      message: json['message'] as String,
      error: json['error'],
      stackTrace:
          const StackTraceConverter().fromJson(json['stackTrace'] as String?),
      timestamp: json['timestamp'] == null
          ? null
          : DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$$WebSocketErrorModelImplToJson(
        _$WebSocketErrorModelImpl instance) =>
    <String, dynamic>{
      'message': instance.message,
      'error': instance.error,
      'stackTrace': const StackTraceConverter().toJson(instance.stackTrace),
      'timestamp': instance.timestamp?.toIso8601String(),
    };

_$WebSocketConnectionModelImpl _$$WebSocketConnectionModelImplFromJson(
        Map<String, dynamic> json) =>
    _$WebSocketConnectionModelImpl(
      url: json['url'] as String,
      state: $enumDecode(_$WebSocketStateEnumMap, json['state']),
      timestamp: json['timestamp'] == null
          ? null
          : DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$$WebSocketConnectionModelImplToJson(
        _$WebSocketConnectionModelImpl instance) =>
    <String, dynamic>{
      'url': instance.url,
      'state': _$WebSocketStateEnumMap[instance.state]!,
      'timestamp': instance.timestamp?.toIso8601String(),
    };

const _$WebSocketStateEnumMap = {
  WebSocketState.disconnected: 'disconnected',
  WebSocketState.connecting: 'connecting',
  WebSocketState.connected: 'connected',
  WebSocketState.reconnecting: 'reconnecting',
  WebSocketState.error: 'error',
};
