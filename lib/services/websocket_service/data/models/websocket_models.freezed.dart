// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'websocket_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

WebSocketMessageModel _$WebSocketMessageModelFromJson(
    Map<String, dynamic> json) {
  return _WebSocketMessageModel.fromJson(json);
}

/// @nodoc
mixin _$WebSocketMessageModel {
  dynamic get data => throw _privateConstructorUsedError;
  WebSocketMessageType get type => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WebSocketMessageModelCopyWith<WebSocketMessageModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WebSocketMessageModelCopyWith<$Res> {
  factory $WebSocketMessageModelCopyWith(WebSocketMessageModel value,
          $Res Function(WebSocketMessageModel) then) =
      _$WebSocketMessageModelCopyWithImpl<$Res, WebSocketMessageModel>;
  @useResult
  $Res call({dynamic data, WebSocketMessageType type});
}

/// @nodoc
class _$WebSocketMessageModelCopyWithImpl<$Res,
        $Val extends WebSocketMessageModel>
    implements $WebSocketMessageModelCopyWith<$Res> {
  _$WebSocketMessageModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = freezed,
    Object? type = null,
  }) {
    return _then(_value.copyWith(
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as dynamic,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as WebSocketMessageType,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WebSocketMessageModelImplCopyWith<$Res>
    implements $WebSocketMessageModelCopyWith<$Res> {
  factory _$$WebSocketMessageModelImplCopyWith(
          _$WebSocketMessageModelImpl value,
          $Res Function(_$WebSocketMessageModelImpl) then) =
      __$$WebSocketMessageModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({dynamic data, WebSocketMessageType type});
}

/// @nodoc
class __$$WebSocketMessageModelImplCopyWithImpl<$Res>
    extends _$WebSocketMessageModelCopyWithImpl<$Res,
        _$WebSocketMessageModelImpl>
    implements _$$WebSocketMessageModelImplCopyWith<$Res> {
  __$$WebSocketMessageModelImplCopyWithImpl(_$WebSocketMessageModelImpl _value,
      $Res Function(_$WebSocketMessageModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = freezed,
    Object? type = null,
  }) {
    return _then(_$WebSocketMessageModelImpl(
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as dynamic,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as WebSocketMessageType,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WebSocketMessageModelImpl implements _WebSocketMessageModel {
  const _$WebSocketMessageModelImpl({required this.data, required this.type});

  factory _$WebSocketMessageModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$WebSocketMessageModelImplFromJson(json);

  @override
  final dynamic data;
  @override
  final WebSocketMessageType type;

  @override
  String toString() {
    return 'WebSocketMessageModel(data: $data, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WebSocketMessageModelImpl &&
            const DeepCollectionEquality().equals(other.data, data) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(data), type);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WebSocketMessageModelImplCopyWith<_$WebSocketMessageModelImpl>
      get copyWith => __$$WebSocketMessageModelImplCopyWithImpl<
          _$WebSocketMessageModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WebSocketMessageModelImplToJson(
      this,
    );
  }
}

abstract class _WebSocketMessageModel implements WebSocketMessageModel {
  const factory _WebSocketMessageModel(
      {required final dynamic data,
      required final WebSocketMessageType type}) = _$WebSocketMessageModelImpl;

  factory _WebSocketMessageModel.fromJson(Map<String, dynamic> json) =
      _$WebSocketMessageModelImpl.fromJson;

  @override
  dynamic get data;
  @override
  WebSocketMessageType get type;
  @override
  @JsonKey(ignore: true)
  _$$WebSocketMessageModelImplCopyWith<_$WebSocketMessageModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

WebSocketErrorModel _$WebSocketErrorModelFromJson(Map<String, dynamic> json) {
  return _WebSocketErrorModel.fromJson(json);
}

/// @nodoc
mixin _$WebSocketErrorModel {
  String get message => throw _privateConstructorUsedError;
  dynamic get error => throw _privateConstructorUsedError;
  @StackTraceConverter()
  StackTrace? get stackTrace => throw _privateConstructorUsedError;
  DateTime? get timestamp => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WebSocketErrorModelCopyWith<WebSocketErrorModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WebSocketErrorModelCopyWith<$Res> {
  factory $WebSocketErrorModelCopyWith(
          WebSocketErrorModel value, $Res Function(WebSocketErrorModel) then) =
      _$WebSocketErrorModelCopyWithImpl<$Res, WebSocketErrorModel>;
  @useResult
  $Res call(
      {String message,
      dynamic error,
      @StackTraceConverter() StackTrace? stackTrace,
      DateTime? timestamp});
}

/// @nodoc
class _$WebSocketErrorModelCopyWithImpl<$Res, $Val extends WebSocketErrorModel>
    implements $WebSocketErrorModelCopyWith<$Res> {
  _$WebSocketErrorModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? error = freezed,
    Object? stackTrace = freezed,
    Object? timestamp = freezed,
  }) {
    return _then(_value.copyWith(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as dynamic,
      stackTrace: freezed == stackTrace
          ? _value.stackTrace
          : stackTrace // ignore: cast_nullable_to_non_nullable
              as StackTrace?,
      timestamp: freezed == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WebSocketErrorModelImplCopyWith<$Res>
    implements $WebSocketErrorModelCopyWith<$Res> {
  factory _$$WebSocketErrorModelImplCopyWith(_$WebSocketErrorModelImpl value,
          $Res Function(_$WebSocketErrorModelImpl) then) =
      __$$WebSocketErrorModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      dynamic error,
      @StackTraceConverter() StackTrace? stackTrace,
      DateTime? timestamp});
}

/// @nodoc
class __$$WebSocketErrorModelImplCopyWithImpl<$Res>
    extends _$WebSocketErrorModelCopyWithImpl<$Res, _$WebSocketErrorModelImpl>
    implements _$$WebSocketErrorModelImplCopyWith<$Res> {
  __$$WebSocketErrorModelImplCopyWithImpl(_$WebSocketErrorModelImpl _value,
      $Res Function(_$WebSocketErrorModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? error = freezed,
    Object? stackTrace = freezed,
    Object? timestamp = freezed,
  }) {
    return _then(_$WebSocketErrorModelImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as dynamic,
      stackTrace: freezed == stackTrace
          ? _value.stackTrace
          : stackTrace // ignore: cast_nullable_to_non_nullable
              as StackTrace?,
      timestamp: freezed == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WebSocketErrorModelImpl implements _WebSocketErrorModel {
  const _$WebSocketErrorModelImpl(
      {required this.message,
      this.error,
      @StackTraceConverter() this.stackTrace,
      this.timestamp});

  factory _$WebSocketErrorModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$WebSocketErrorModelImplFromJson(json);

  @override
  final String message;
  @override
  final dynamic error;
  @override
  @StackTraceConverter()
  final StackTrace? stackTrace;
  @override
  final DateTime? timestamp;

  @override
  String toString() {
    return 'WebSocketErrorModel(message: $message, error: $error, stackTrace: $stackTrace, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WebSocketErrorModelImpl &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality().equals(other.error, error) &&
            (identical(other.stackTrace, stackTrace) ||
                other.stackTrace == stackTrace) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, message,
      const DeepCollectionEquality().hash(error), stackTrace, timestamp);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WebSocketErrorModelImplCopyWith<_$WebSocketErrorModelImpl> get copyWith =>
      __$$WebSocketErrorModelImplCopyWithImpl<_$WebSocketErrorModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WebSocketErrorModelImplToJson(
      this,
    );
  }
}

abstract class _WebSocketErrorModel implements WebSocketErrorModel {
  const factory _WebSocketErrorModel(
      {required final String message,
      final dynamic error,
      @StackTraceConverter() final StackTrace? stackTrace,
      final DateTime? timestamp}) = _$WebSocketErrorModelImpl;

  factory _WebSocketErrorModel.fromJson(Map<String, dynamic> json) =
      _$WebSocketErrorModelImpl.fromJson;

  @override
  String get message;
  @override
  dynamic get error;
  @override
  @StackTraceConverter()
  StackTrace? get stackTrace;
  @override
  DateTime? get timestamp;
  @override
  @JsonKey(ignore: true)
  _$$WebSocketErrorModelImplCopyWith<_$WebSocketErrorModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WebSocketConnectionModel _$WebSocketConnectionModelFromJson(
    Map<String, dynamic> json) {
  return _WebSocketConnectionModel.fromJson(json);
}

/// @nodoc
mixin _$WebSocketConnectionModel {
  String get url => throw _privateConstructorUsedError;
  WebSocketState get state => throw _privateConstructorUsedError;
  DateTime? get timestamp => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WebSocketConnectionModelCopyWith<WebSocketConnectionModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WebSocketConnectionModelCopyWith<$Res> {
  factory $WebSocketConnectionModelCopyWith(WebSocketConnectionModel value,
          $Res Function(WebSocketConnectionModel) then) =
      _$WebSocketConnectionModelCopyWithImpl<$Res, WebSocketConnectionModel>;
  @useResult
  $Res call({String url, WebSocketState state, DateTime? timestamp});
}

/// @nodoc
class _$WebSocketConnectionModelCopyWithImpl<$Res,
        $Val extends WebSocketConnectionModel>
    implements $WebSocketConnectionModelCopyWith<$Res> {
  _$WebSocketConnectionModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = null,
    Object? state = null,
    Object? timestamp = freezed,
  }) {
    return _then(_value.copyWith(
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as WebSocketState,
      timestamp: freezed == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WebSocketConnectionModelImplCopyWith<$Res>
    implements $WebSocketConnectionModelCopyWith<$Res> {
  factory _$$WebSocketConnectionModelImplCopyWith(
          _$WebSocketConnectionModelImpl value,
          $Res Function(_$WebSocketConnectionModelImpl) then) =
      __$$WebSocketConnectionModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String url, WebSocketState state, DateTime? timestamp});
}

/// @nodoc
class __$$WebSocketConnectionModelImplCopyWithImpl<$Res>
    extends _$WebSocketConnectionModelCopyWithImpl<$Res,
        _$WebSocketConnectionModelImpl>
    implements _$$WebSocketConnectionModelImplCopyWith<$Res> {
  __$$WebSocketConnectionModelImplCopyWithImpl(
      _$WebSocketConnectionModelImpl _value,
      $Res Function(_$WebSocketConnectionModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = null,
    Object? state = null,
    Object? timestamp = freezed,
  }) {
    return _then(_$WebSocketConnectionModelImpl(
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as WebSocketState,
      timestamp: freezed == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WebSocketConnectionModelImpl implements _WebSocketConnectionModel {
  const _$WebSocketConnectionModelImpl(
      {required this.url, required this.state, this.timestamp});

  factory _$WebSocketConnectionModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$WebSocketConnectionModelImplFromJson(json);

  @override
  final String url;
  @override
  final WebSocketState state;
  @override
  final DateTime? timestamp;

  @override
  String toString() {
    return 'WebSocketConnectionModel(url: $url, state: $state, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WebSocketConnectionModelImpl &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, url, state, timestamp);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WebSocketConnectionModelImplCopyWith<_$WebSocketConnectionModelImpl>
      get copyWith => __$$WebSocketConnectionModelImplCopyWithImpl<
          _$WebSocketConnectionModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WebSocketConnectionModelImplToJson(
      this,
    );
  }
}

abstract class _WebSocketConnectionModel implements WebSocketConnectionModel {
  const factory _WebSocketConnectionModel(
      {required final String url,
      required final WebSocketState state,
      final DateTime? timestamp}) = _$WebSocketConnectionModelImpl;

  factory _WebSocketConnectionModel.fromJson(Map<String, dynamic> json) =
      _$WebSocketConnectionModelImpl.fromJson;

  @override
  String get url;
  @override
  WebSocketState get state;
  @override
  DateTime? get timestamp;
  @override
  @JsonKey(ignore: true)
  _$$WebSocketConnectionModelImplCopyWith<_$WebSocketConnectionModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
