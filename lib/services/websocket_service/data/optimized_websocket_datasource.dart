import 'dart:async';

import 'package:flutter_audio_room/services/websocket_service/core/implementations/websocket_implementations.dart';
import 'package:flutter_audio_room/services/websocket_service/core/interfaces/websocket_interfaces.dart';
import 'package:flutter_audio_room/services/websocket_service/core/modules/connection_manager.dart';
import 'package:flutter_audio_room/services/websocket_service/core/modules/heartbeat_manager.dart';
import 'package:flutter_audio_room/services/websocket_service/core/modules/message_processor.dart';
import 'package:flutter_audio_room/services/websocket_service/core/modules/reconnect_manager.dart';
import 'package:flutter_audio_room/services/websocket_service/data/websocket_datasource.dart';
import 'package:flutter_audio_room/services/websocket_service/domain/entities/websocket_entity.dart';
import 'package:flutter_audio_room/shared/data/local/storage_service.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/models/response.dart'
    as response;
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';

/// Optimized WebSocket data source implementation
///
/// This implementation follows clean architecture principles with:
/// - Low coupling: Dependencies are injected through interfaces
/// - High cohesion: Related functionality is grouped in dedicated modules
/// - High availability: Robust error handling and reconnection mechanisms
/// - Modularity: Functionality is split into independent, testable modules
class OptimizedWebSocketDataSource implements WebSocketDataSource {
  // Core modules
  late final WebSocketConnectionManager _connectionManager;
  late final WebSocketHeartbeatManager _heartbeatManager;
  late final WebSocketMessageProcessor _messageProcessor;
  late final WebSocketReconnectManager _reconnectManager;

  // Configuration and providers
  late final IWebSocketConfig _config;
  late final IWebSocketLogger _logger;

  // State management
  bool _isDisposed = false;
  StreamSubscription<ConnectionState>? _connectionStateSubscription;
  StreamSubscription<String>? _messageSubscription;

  /// Factory constructor for creating with default implementations
  factory OptimizedWebSocketDataSource.create({
    required StorageService storageService,
    required String url,
  }) {
    // Create providers
    final authProvider = StorageWebSocketAuthProvider(
      storageService: storageService,
    );
    final deviceInfoProvider = GetItWebSocketDeviceInfoProvider();
    final logger = LogUtilsWebSocketLogger();
    final errorHandler = DefaultWebSocketErrorHandler(logger: logger);

    // Create configuration
    final config = EnhancedWebSocketConfig(
      url: url,
      authProvider: authProvider,
      deviceInfoProvider: deviceInfoProvider,
    );

    return OptimizedWebSocketDataSource._(
      config: config,
      connectionProvider: DefaultWebSocketConnectionProvider(),
      messageSerializer: DefaultWebSocketMessageSerializer(),
      authProvider: authProvider,
      logger: logger,
      errorHandler: errorHandler,
    );
  }

  /// Private constructor for dependency injection
  OptimizedWebSocketDataSource._({
    required IWebSocketConfig config,
    required IWebSocketConnectionProvider connectionProvider,
    required IWebSocketMessageSerializer messageSerializer,
    required IWebSocketAuthProvider authProvider,
    required IWebSocketLogger logger,
    required IWebSocketErrorHandler errorHandler,
  })  : _config = config,
        _logger = logger {
    // Initialize connection manager
    _connectionManager = WebSocketConnectionManager(
      config: config,
      connectionProvider: connectionProvider,
      logger: logger,
      errorHandler: errorHandler,
    );

    // Initialize message processor
    _messageProcessor = WebSocketMessageProcessor(
      serializer: messageSerializer,
      logger: logger,
      onHeartbeatResponse: () => _heartbeatManager.onHeartbeatResponse(),
    );

    // Initialize heartbeat manager
    _heartbeatManager = WebSocketHeartbeatManager(
      config: config,
      logger: logger,
      errorHandler: errorHandler,
      sendMessage: _sendMessageInternal,
    );

    // Initialize reconnect manager
    _reconnectManager = WebSocketReconnectManager(
      config: config,
      logger: logger,
      errorHandler: errorHandler,
      authProvider: authProvider,
      connectFunction: _connectInternal,
    );

    _setupEventListeners();
  }

  @override
  String get url => _config.url;

  @override
  bool get isConnected => _connectionManager.isConnected;

  @override
  Stream<response.WebsocketResponse> get messageStream =>
      _messageProcessor.messageStream;

  @override
  Future<ResultWithData<void>> connect() async {
    if (_isDisposed) {
      return Either.left(const AppException(
        statusCode: 1006,
        message: 'WebSocket data source is disposed',
        identifier: 'WS_DISPOSED',
      ));
    }

    _logger.info('Connecting to WebSocket: $url',
        tag: 'OptimizedWebSocketDataSource');
    return await _connectInternal();
  }

  @override
  Future<ResultWithData<void>> disconnect(
      {required CloseCode closeCode}) async {
    _logger.info('Disconnecting WebSocket: $url',
        tag: 'OptimizedWebSocketDataSource');

    // Stop heartbeat and reconnection
    _heartbeatManager.stop();
    _reconnectManager.stopReconnection();

    // Disconnect connection
    return await _connectionManager.disconnect(closeCode: closeCode.value);
  }

  @override
  Future<ResultWithData<void>> sendMessage(
      WebSocketMessageEntity message) async {
    if (_isDisposed) {
      return Either.left(const AppException(
        statusCode: 1006,
        message: 'WebSocket data source is disposed',
        identifier: 'WS_DISPOSED',
      ));
    }

    if (!isConnected) {
      return Either.left(const AppException(
        statusCode: 1006,
        message: 'WebSocket is not connected',
        identifier: 'WS_NOT_CONNECTED',
      ));
    }

    try {
      final serializedMessage =
          _messageProcessor.serializeOutgoingMessage(message);
      return await _connectionManager.send(serializedMessage);
    } catch (e) {
      _logger.error('Failed to send message',
          error: e, tag: 'OptimizedWebSocketDataSource');
      return Either.left(AppException(
        statusCode: 500,
        message: 'Failed to send message: $e',
        identifier: 'WS_SEND_ERROR',
      ));
    }
  }

  @override
  void startHeartbeat() {
    if (!_isDisposed && isConnected) {
      _heartbeatManager.start();
    }
  }

  @override
  void stopHeartbeat() {
    _heartbeatManager.stop();
  }

  @override
  Future<void> tryRefreshTokenAndReconnect() async {
    if (_isDisposed) {
      _logger.debug(
          'Data source is disposed, skipping token refresh and reconnect',
          tag: 'OptimizedWebSocketDataSource');
      return;
    }

    await _reconnectManager.startReconnection();
  }

  @override
  void resetReconnectionState() {
    _reconnectManager.reset();
  }

  @override
  Future<void> dispose() async {
    if (_isDisposed) return;

    _isDisposed = true;
    _logger.info('Disposing WebSocket data source: $url',
        tag: 'OptimizedWebSocketDataSource');

    // Cancel subscriptions
    await _connectionStateSubscription?.cancel();
    await _messageSubscription?.cancel();

    // Dispose modules in reverse order of initialization
    _reconnectManager.dispose();
    _heartbeatManager.dispose();
    await _messageProcessor.dispose();
    await _connectionManager.dispose();

    _logger.info('WebSocket data source disposed: $url',
        tag: 'OptimizedWebSocketDataSource');
  }

  /// Internal connection method
  Future<ResultWithData<void>> _connectInternal() async {
    final result = await _connectionManager.connect();

    result.fold(
      (error) {
        _logger.error('Connection failed: ${error.message}',
            tag: 'OptimizedWebSocketDataSource');
        // Start reconnection on failure
        if (!_isDisposed) {
          unawaited(_reconnectManager.startReconnection());
        }
      },
      (_) {
        _logger.info('Connection successful',
            tag: 'OptimizedWebSocketDataSource');
        // Reset reconnection state on success
        _reconnectManager.reset();
        // Start heartbeat
        startHeartbeat();
      },
    );

    return result;
  }

  /// Internal message sending method for heartbeat
  Future<void> _sendMessageInternal(WebSocketMessageEntity message) async {
    final result = await sendMessage(message);
    result.fold(
      (error) => _logger.error(
          'Failed to send internal message: ${error.message}',
          tag: 'OptimizedWebSocketDataSource'),
      (_) => {}, // Success, no action needed
    );
  }

  /// Setup event listeners for connection state and messages
  void _setupEventListeners() {
    // Listen to connection state changes
    _connectionStateSubscription = _connectionManager.stateStream.listen(
      (state) {
        _logger.debug('Connection state changed: $state',
            tag: 'OptimizedWebSocketDataSource');

        switch (state) {
          case ConnectionState.connected:
            startHeartbeat();
            break;
          case ConnectionState.disconnecting:
            stopHeartbeat();
            break;
          case ConnectionState.disconnected:
            stopHeartbeat();
            if (!_isDisposed) {
              unawaited(_reconnectManager.startReconnection());
            }
            break;
          case ConnectionState.disposed:
            stopHeartbeat();
            _reconnectManager.stopReconnection();
            break;
          default:
            break;
        }
      },
      onError: (error) {
        _logger.error('Connection state stream error',
            error: error, tag: 'OptimizedWebSocketDataSource');
      },
    );

    // Listen to incoming messages
    _messageSubscription = _connectionManager.messageStream.listen(
      (message) {
        _messageProcessor.processIncomingMessage(message);
      },
      onError: (error) {
        _logger.error('Message stream error',
            error: error, tag: 'OptimizedWebSocketDataSource');
      },
    );
  }
}
