import 'dart:async';
import 'dart:io';

import 'package:flutter_audio_room/services/websocket_service/core/interfaces/websocket_interfaces.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';

/// WebSocket connection states
enum ConnectionState {
  disconnected,
  connecting,
  connected,
  disconnecting,
  reconnecting,
  disposed,
}

/// WebSocket connection manager
/// Handles the low-level WebSocket connection lifecycle
class WebSocketConnectionManager {
  final IWebSocketConfig _config;
  final IWebSocketConnectionProvider _connectionProvider;
  final IWebSocketLogger _logger;
  final IWebSocketErrorHandler _errorHandler;

  WebSocket? _socket;
  ConnectionState _state = ConnectionState.disconnected;
  final StreamController<ConnectionState> _stateController =
      StreamController<ConnectionState>.broadcast();
  final StreamController<String> _messageController =
      StreamController<String>.broadcast();

  bool _isDisposed = false;

  WebSocketConnectionManager({
    required IWebSocketConfig config,
    required IWebSocketConnectionProvider connectionProvider,
    required IWebSocketLogger logger,
    required IWebSocketErrorHandler errorHandler,
  })  : _config = config,
        _connectionProvider = connectionProvider,
        _logger = logger,
        _errorHandler = errorHandler;

  /// Current connection state
  ConnectionState get state => _state;

  /// Connection state stream
  Stream<ConnectionState> get stateStream => _stateController.stream;

  /// Check if connected
  bool get isConnected =>
      _state == ConnectionState.connected && _socket != null;

  /// Check if disposed
  bool get isDisposed => _isDisposed;

  /// Connect to WebSocket server
  Future<ResultWithData<void>> connect() async {
    if (_isDisposed) {
      return Either.left(const AppException(
        statusCode: 1006,
        message: 'Connection manager is disposed',
        identifier: 'WS_DISPOSED',
      ));
    }

    if (_state == ConnectionState.connecting) {
      _logger.debug('Connection already in progress', tag: 'ConnectionManager');
      return Either.right(null);
    }

    if (isConnected) {
      _logger.debug('Already connected', tag: 'ConnectionManager');
      return Either.right(null);
    }

    return await _performConnect();
  }

  /// Disconnect from WebSocket server
  Future<ResultWithData<void>> disconnect({int? closeCode}) async {
    _logger.debug('Disconnecting WebSocket', tag: 'ConnectionManager');

    final currentSocket = _socket;
    if (currentSocket != null) {
      _updateState(ConnectionState.disconnecting);

      try {
        await currentSocket.close(closeCode ?? 1000);
        _logger.info('WebSocket disconnected gracefully',
            tag: 'ConnectionManager');
      } catch (e) {
        _logger.warning('Error during disconnect: $e',
            tag: 'ConnectionManager');
      } finally {
        _socket = null;
        _updateState(ConnectionState.disconnected);
      }
    } else {
      _updateState(ConnectionState.disconnected);
    }

    return Either.right(null);
  }

  /// Send data through WebSocket
  Future<ResultWithData<void>> send(String data) async {
    if (!isConnected) {
      return Either.left(const AppException(
        statusCode: 1006,
        message: 'WebSocket is not connected',
        identifier: 'WS_NOT_CONNECTED',
      ));
    }

    try {
      _socket!.add(data);
      return Either.right(null);
    } catch (e) {
      _logger.error('Failed to send message',
          error: e, tag: 'ConnectionManager');
      _errorHandler.handleMessageError(e, StackTrace.current);
      return Either.left(AppException(
        statusCode: 500,
        message: 'Failed to send message: $e',
        identifier: 'WS_SEND_ERROR',
      ));
    }
  }

  /// Listen to incoming messages
  Stream<String> get messageStream => _messageController.stream;

  /// Dispose resources
  Future<void> dispose() async {
    if (_isDisposed) return;

    _isDisposed = true;
    _updateState(ConnectionState.disposed);

    await disconnect(closeCode: 1001);
    await _stateController.close();
    await _messageController.close();

    _logger.debug('Connection manager disposed', tag: 'ConnectionManager');
  }

  /// Perform the actual connection
  Future<ResultWithData<void>> _performConnect() async {
    _updateState(ConnectionState.connecting);

    try {
      _logger.info('Connecting to ${_config.url}', tag: 'ConnectionManager');

      final socket = await _connectionProvider
          .connect(_config.url, _config.headers)
          .timeout(Duration(seconds: _config.connectionTimeout));

      if (_isDisposed) {
        await socket.close(1001);
        return Either.left(const AppException(
          statusCode: 1006,
          message: 'Connection cancelled due to disposal',
          identifier: 'WS_DISPOSED',
        ));
      }

      _socket = socket as WebSocket;
      _setupSocketListeners();
      _updateState(ConnectionState.connected);

      _logger.info('WebSocket connected successfully',
          tag: 'ConnectionManager');
      return Either.right(null);
    } on WebSocketException catch (e) {
      _updateState(ConnectionState.disconnected);
      final error = AppException(
        statusCode: 1006,
        message: 'WebSocket connection failed: ${e.message}',
        identifier: 'WS_CONNECTION_ERROR',
      );
      _logger.error('WebSocket connection failed',
          error: error, tag: 'ConnectionManager');
      _errorHandler.handleConnectionError(e, StackTrace.current);
      return Either.left(error);
    } catch (e) {
      _updateState(ConnectionState.disconnected);
      final error = AppException(
        statusCode: 500,
        message: 'Unexpected connection error: $e',
        identifier: 'WS_UNEXPECTED_ERROR',
      );
      _logger.error('Unexpected connection error',
          error: error, tag: 'ConnectionManager');
      _errorHandler.handleConnectionError(e, StackTrace.current);
      return Either.left(error);
    }
  }

  /// Setup socket event listeners
  void _setupSocketListeners() {
    _socket?.listen(
      (message) {
        // Handle incoming messages
        if (message is String) {
          _messageController.add(message);
        } else {
          _messageController.add(message.toString());
        }
      },
      onError: (error) {
        _logger.error('WebSocket error',
            error: error, tag: 'ConnectionManager');
        _errorHandler.handleConnectionError(error, StackTrace.current);
        _updateState(ConnectionState.disconnected);
      },
      onDone: () {
        _logger.info('WebSocket connection closed', tag: 'ConnectionManager');
        _updateState(ConnectionState.disconnected);
      },
      cancelOnError: false,
    );
  }

  /// Update connection state
  void _updateState(ConnectionState newState) {
    if (_state != newState) {
      _state = newState;
      if (!_stateController.isClosed) {
        _stateController.add(newState);
      }
    }
  }
}
