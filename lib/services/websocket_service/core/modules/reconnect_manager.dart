import 'dart:async';
import 'dart:math';

import 'package:flutter_audio_room/services/websocket_service/core/interfaces/websocket_interfaces.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';

/// Reconnection manager
/// Handles automatic reconnection with exponential backoff
class WebSocketReconnectManager {
  final IWebSocketConfig _config;
  final IWebSocketLogger _logger;
  final IWebSocketErrorHandler _errorHandler;
  final IWebSocketAuthProvider _authProvider;
  final Future<ResultWithData<void>> Function() _connectFunction;

  Timer? _reconnectTimer;
  int _reconnectAttempts = 0;
  bool _isReconnecting = false;
  bool _isDisposed = false;
  Future<void>? _activeReconnectTask;

  WebSocketReconnectManager({
    required IWebSocketConfig config,
    required IWebSocketLogger logger,
    required IWebSocketErrorHandler errorHandler,
    required IWebSocketAuthProvider authProvider,
    required Future<ResultWithData<void>> Function() connectFunction,
  })  : _config = config,
        _logger = logger,
        _errorHandler = errorHandler,
        _authProvider = authProvider,
        _connectFunction = connectFunction;

  /// Check if currently reconnecting
  bool get isReconnecting => _isReconnecting;

  /// Get current reconnect attempts
  int get reconnectAttempts => _reconnectAttempts;

  /// Check if disposed
  bool get isDisposed => _isDisposed;

  /// Start reconnection process
  Future<void> startReconnection() async {
    if (_isDisposed) {
      _logger.debug('Reconnect manager is disposed, skipping reconnection',
          tag: 'ReconnectManager');
      return;
    }

    if (_activeReconnectTask != null) {
      _logger.debug('Reconnection already in progress, waiting for completion',
          tag: 'ReconnectManager');
      await _activeReconnectTask;
      return;
    }

    _activeReconnectTask = _performReconnection();

    try {
      await _activeReconnectTask;
    } finally {
      _activeReconnectTask = null;
    }
  }

  /// Stop reconnection process
  void stopReconnection() {
    _logger.info('Stopping reconnection process', tag: 'ReconnectManager');

    _reconnectTimer?.cancel();
    _reconnectTimer = null;
    _isReconnecting = false;
    _reconnectAttempts = 0;
  }

  /// Reset reconnection state
  void reset() {
    _logger.debug('Resetting reconnection state', tag: 'ReconnectManager');

    stopReconnection();
    _reconnectAttempts = 0;
  }

  /// Dispose resources
  void dispose() {
    if (_isDisposed) return;

    _isDisposed = true;
    stopReconnection();

    _logger.debug('Reconnect manager disposed', tag: 'ReconnectManager');
  }

  /// Perform the reconnection process
  Future<void> _performReconnection() async {
    if (_isDisposed) return;

    // Try to refresh token first
    final tokenResult = await _authProvider.refreshToken();
    tokenResult.fold(
      (error) {
        _logger.warning('Token refresh failed: ${error.message}',
            tag: 'ReconnectManager');
        // Continue with reconnection even if token refresh fails
      },
      (newToken) {
        _logger.info('Token refreshed successfully', tag: 'ReconnectManager');
        // Reset reconnect attempts after successful token refresh
        _reconnectAttempts = 0;
      },
    );

    _scheduleReconnection();
  }

  /// Schedule the next reconnection attempt
  void _scheduleReconnection() {
    if (_isDisposed) return;

    // Check if we've exceeded max attempts
    if (_config.maxReconnectAttempts > 0 &&
        _reconnectAttempts >= _config.maxReconnectAttempts) {
      _logger.warning(
        'Max reconnection attempts (${_config.maxReconnectAttempts}) reached',
        tag: 'ReconnectManager',
      );
      stopReconnection();
      return;
    }

    _reconnectTimer?.cancel();

    // Calculate delay using exponential backoff
    final delay = min(
      _config.initialReconnectDelay * pow(2, _reconnectAttempts),
      _config.maxReconnectDelay,
    ).toInt();

    _logger.info(
      'Scheduling reconnection attempt ${_reconnectAttempts + 1} in ${delay}s',
      tag: 'ReconnectManager',
    );

    _isReconnecting = true;
    _reconnectTimer = Timer(Duration(seconds: delay), () async {
      if (_isDisposed) return;

      _reconnectAttempts++;

      try {
        final result = await _connectFunction();

        result.fold(
          (error) {
            _logger.error(
              'Reconnection attempt $_reconnectAttempts failed: ${error.message}',
              tag: 'ReconnectManager',
            );

            // Check if we should retry for this error
            if (_errorHandler.shouldRetryConnection(error)) {
              _scheduleReconnection();
            } else {
              _logger.warning(
                'Error is not retryable, stopping reconnection',
                tag: 'ReconnectManager',
              );
              stopReconnection();
            }
          },
          (_) {
            _logger.info(
              'Reconnection successful after $_reconnectAttempts attempts',
              tag: 'ReconnectManager',
            );
            stopReconnection();
          },
        );
      } catch (e) {
        _logger.error(
          'Unexpected error during reconnection: $e',
          error: e,
          tag: 'ReconnectManager',
        );
        _scheduleReconnection();
      }
    });
  }
}
