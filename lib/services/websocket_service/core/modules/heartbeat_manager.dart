import 'dart:async';

import 'package:flutter_audio_room/services/websocket_service/core/interfaces/websocket_interfaces.dart';
import 'package:flutter_audio_room/services/websocket_service/domain/entities/websocket_entity.dart';

/// Heartbeat manager
/// Handles WebSocket heartbeat mechanism to keep connection alive
class WebSocketHeartbeatManager {
  final IWebSocketConfig _config;
  final IWebSocketLogger _logger;
  final IWebSocketErrorHandler _errorHandler;
  final Future<void> Function(WebSocketMessageEntity) _sendMessage;

  Timer? _heartbeatTimer;
  DateTime? _lastHeartbeatResponse;
  bool _isActive = false;

  WebSocketHeartbeatManager({
    required IWebSocketConfig config,
    required IWebSocketLogger logger,
    required IWebSocketErrorHandler errorHandler,
    required Future<void> Function(WebSocketMessageEntity) sendMessage,
  })  : _config = config,
        _logger = logger,
        _errorHandler = errorHandler,
        _sendMessage = sendMessage;

  /// Check if heartbeat is active
  bool get isActive => _isActive;

  /// Get last heartbeat response time
  DateTime? get lastHeartbeatResponse => _lastHeartbeatResponse;

  /// Start heartbeat mechanism
  void start() {
    if (_isActive) {
      _logger.debug('Heartbeat already active', tag: 'HeartbeatManager');
      return;
    }

    _isActive = true;
    _lastHeartbeatResponse = DateTime.now();

    _heartbeatTimer = Timer.periodic(
      Duration(seconds: _config.heartbeatInterval),
      (_) => _sendHeartbeat(),
    );

    _logger.info(
        'Heartbeat started with ${_config.heartbeatInterval}s interval',
        tag: 'HeartbeatManager');

    // Send immediate heartbeat to verify connection
    _sendHeartbeat();
  }

  /// Stop heartbeat mechanism
  void stop() {
    if (!_isActive) return;

    _isActive = false;
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
    _lastHeartbeatResponse = null;

    _logger.info('Heartbeat stopped', tag: 'HeartbeatManager');
  }

  /// Handle heartbeat response
  void onHeartbeatResponse() {
    _lastHeartbeatResponse = DateTime.now();
  }

  /// Check if heartbeat has timed out
  bool isHeartbeatTimeout() {
    if (!_isActive || _lastHeartbeatResponse == null) {
      return false;
    }

    final elapsed = DateTime.now().difference(_lastHeartbeatResponse!);
    final timeoutThreshold = Duration(seconds: _config.heartbeatInterval * 2);

    return elapsed > timeoutThreshold;
  }

  /// Send heartbeat message
  void _sendHeartbeat() async {
    if (!_isActive) return;

    try {
      // Check for timeout before sending
      if (isHeartbeatTimeout()) {
        _logger.warning('Heartbeat timeout detected', tag: 'HeartbeatManager');
        _errorHandler.handleHeartbeatTimeout();
        return;
      }

      final heartbeatMessage = WebSocketMessageEntity(
        data: _config.heartbeatMessage,
        type: WebSocketMessageType.json,
      );

      await _sendMessage(heartbeatMessage);
    } catch (e) {
      _logger.error('Failed to send heartbeat',
          error: e, tag: 'HeartbeatManager');
      _errorHandler.handleMessageError(e, StackTrace.current);
    }
  }

  /// Dispose resources
  void dispose() {
    stop();
    _logger.debug('Heartbeat manager disposed', tag: 'HeartbeatManager');
  }
}
