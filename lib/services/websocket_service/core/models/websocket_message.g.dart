// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'websocket_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$WebSocketMessageImpl _$$WebSocketMessageImplFromJson(
        Map<String, dynamic> json) =>
    _$WebSocketMessageImpl(
      data: json['data'],
      type: $enumDecode(_$WebSocketMessageTypeEnumMap, json['type']),
      timestamp: json['timestamp'] == null
          ? null
          : DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$$WebSocketMessageImplToJson(
        _$WebSocketMessageImpl instance) =>
    <String, dynamic>{
      'data': instance.data,
      'type': _$WebSocketMessageTypeEnumMap[instance.type]!,
      'timestamp': instance.timestamp?.toIso8601String(),
    };

const _$WebSocketMessageTypeEnumMap = {
  WebSocketMessageType.text: 'text',
  WebSocketMessageType.json: 'json',
  WebSocketMessageType.binary: 'binary',
};
