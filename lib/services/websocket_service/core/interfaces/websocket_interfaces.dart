import 'dart:async';

import 'package:flutter_audio_room/services/websocket_service/domain/entities/websocket_entity.dart';
import 'package:flutter_audio_room/shared/domain/models/response.dart'
    as response;
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';

/// WebSocket connection configuration interface
abstract class IWebSocketConfig {
  /// WebSocket URL
  String get url;

  /// Connection headers
  Map<String, String> get headers;

  /// Heartbeat interval in seconds
  int get heartbeatInterval;

  /// Initial reconnect delay in seconds
  int get initialReconnectDelay;

  /// Maximum reconnect delay in seconds
  int get maxReconnectDelay;

  /// Maximum reconnect attempts (0 for unlimited)
  int get maxReconnectAttempts;

  /// Connection timeout in seconds
  int get connectionTimeout;

  /// Heartbeat message
  String get heartbeatMessage;
}

/// WebSocket connection provider interface
abstract class IWebSocketConnectionProvider {
  /// Create a WebSocket connection
  Future<dynamic> connect(String url, Map<String, String> headers);
}

/// WebSocket message serializer interface
abstract class IWebSocketMessageSerializer {
  /// Serialize message to string
  String serialize(WebSocketMessageEntity message);

  /// Deserialize message from string
  response.WebsocketResponse deserialize(String data);

  /// Check if message is heartbeat request
  bool isHeartbeatRequest(String data);

  /// Check if message is heartbeat response
  bool isHeartbeatResponse(String data);
}

/// WebSocket authentication provider interface
abstract class IWebSocketAuthProvider {
  /// Get current access token
  String get accessToken;

  /// Get timezone
  String get timezone;

  /// Refresh token and return new access token
  Future<ResultWithData<String>> refreshToken();
}

/// WebSocket device info provider interface
abstract class IWebSocketDeviceInfoProvider {
  /// Get platform information
  String get platform;

  /// Get app version
  String get version;
}

/// WebSocket error handler interface
abstract class IWebSocketErrorHandler {
  /// Handle connection error
  void handleConnectionError(dynamic error, StackTrace? stackTrace);

  /// Handle message error
  void handleMessageError(dynamic error, StackTrace? stackTrace);

  /// Handle heartbeat timeout
  void handleHeartbeatTimeout();

  /// Should retry connection for this error
  bool shouldRetryConnection(dynamic error);
}

/// WebSocket logger interface
abstract class IWebSocketLogger {
  /// Log debug message
  void debug(String message, {String? tag});

  /// Log info message
  void info(String message, {String? tag});

  /// Log warning message
  void warning(String message, {String? tag});

  /// Log error message
  void error(String message,
      {dynamic error, StackTrace? stackTrace, String? tag});
}
