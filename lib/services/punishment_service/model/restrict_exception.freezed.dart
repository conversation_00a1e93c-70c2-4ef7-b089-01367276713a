// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'restrict_exception.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RestrictException _$RestrictExceptionFromJson(Map<String, dynamic> json) {
  return _RestrictException.fromJson(json);
}

/// @nodoc
mixin _$RestrictException {
  RestrictModel get restrict => throw _privateConstructorUsedError;
  String get identifier => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RestrictExceptionCopyWith<RestrictException> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RestrictExceptionCopyWith<$Res> {
  factory $RestrictExceptionCopyWith(
          RestrictException value, $Res Function(RestrictException) then) =
      _$RestrictExceptionCopyWithImpl<$Res, RestrictException>;
  @useResult
  $Res call({RestrictModel restrict, String identifier, String message});

  $RestrictModelCopyWith<$Res> get restrict;
}

/// @nodoc
class _$RestrictExceptionCopyWithImpl<$Res, $Val extends RestrictException>
    implements $RestrictExceptionCopyWith<$Res> {
  _$RestrictExceptionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? restrict = null,
    Object? identifier = null,
    Object? message = null,
  }) {
    return _then(_value.copyWith(
      restrict: null == restrict
          ? _value.restrict
          : restrict // ignore: cast_nullable_to_non_nullable
              as RestrictModel,
      identifier: null == identifier
          ? _value.identifier
          : identifier // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $RestrictModelCopyWith<$Res> get restrict {
    return $RestrictModelCopyWith<$Res>(_value.restrict, (value) {
      return _then(_value.copyWith(restrict: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RestrictExceptionImplCopyWith<$Res>
    implements $RestrictExceptionCopyWith<$Res> {
  factory _$$RestrictExceptionImplCopyWith(_$RestrictExceptionImpl value,
          $Res Function(_$RestrictExceptionImpl) then) =
      __$$RestrictExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({RestrictModel restrict, String identifier, String message});

  @override
  $RestrictModelCopyWith<$Res> get restrict;
}

/// @nodoc
class __$$RestrictExceptionImplCopyWithImpl<$Res>
    extends _$RestrictExceptionCopyWithImpl<$Res, _$RestrictExceptionImpl>
    implements _$$RestrictExceptionImplCopyWith<$Res> {
  __$$RestrictExceptionImplCopyWithImpl(_$RestrictExceptionImpl _value,
      $Res Function(_$RestrictExceptionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? restrict = null,
    Object? identifier = null,
    Object? message = null,
  }) {
    return _then(_$RestrictExceptionImpl(
      restrict: null == restrict
          ? _value.restrict
          : restrict // ignore: cast_nullable_to_non_nullable
              as RestrictModel,
      identifier: null == identifier
          ? _value.identifier
          : identifier // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$RestrictExceptionImpl extends _RestrictException {
  const _$RestrictExceptionImpl(
      {required this.restrict, required this.identifier, required this.message})
      : super._();

  factory _$RestrictExceptionImpl.fromJson(Map<String, dynamic> json) =>
      _$$RestrictExceptionImplFromJson(json);

  @override
  final RestrictModel restrict;
  @override
  final String identifier;
  @override
  final String message;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RestrictExceptionImpl &&
            (identical(other.restrict, restrict) ||
                other.restrict == restrict) &&
            (identical(other.identifier, identifier) ||
                other.identifier == identifier) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, restrict, identifier, message);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RestrictExceptionImplCopyWith<_$RestrictExceptionImpl> get copyWith =>
      __$$RestrictExceptionImplCopyWithImpl<_$RestrictExceptionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RestrictExceptionImplToJson(
      this,
    );
  }
}

abstract class _RestrictException extends RestrictException {
  const factory _RestrictException(
      {required final RestrictModel restrict,
      required final String identifier,
      required final String message}) = _$RestrictExceptionImpl;
  const _RestrictException._() : super._();

  factory _RestrictException.fromJson(Map<String, dynamic> json) =
      _$RestrictExceptionImpl.fromJson;

  @override
  RestrictModel get restrict;
  @override
  String get identifier;
  @override
  String get message;
  @override
  @JsonKey(ignore: true)
  _$$RestrictExceptionImplCopyWith<_$RestrictExceptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
