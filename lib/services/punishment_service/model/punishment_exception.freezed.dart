// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'punishment_exception.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PunishmentException _$PunishmentExceptionFromJson(Map<String, dynamic> json) {
  return _PunishmentException.fromJson(json);
}

/// @nodoc
mixin _$PunishmentException {
  String get identifier => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  PunishmentModel get punishment => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PunishmentExceptionCopyWith<PunishmentException> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PunishmentExceptionCopyWith<$Res> {
  factory $PunishmentExceptionCopyWith(
          PunishmentException value, $Res Function(PunishmentException) then) =
      _$PunishmentExceptionCopyWithImpl<$Res, PunishmentException>;
  @useResult
  $Res call({String identifier, String message, PunishmentModel punishment});

  $PunishmentModelCopyWith<$Res> get punishment;
}

/// @nodoc
class _$PunishmentExceptionCopyWithImpl<$Res, $Val extends PunishmentException>
    implements $PunishmentExceptionCopyWith<$Res> {
  _$PunishmentExceptionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? identifier = null,
    Object? message = null,
    Object? punishment = null,
  }) {
    return _then(_value.copyWith(
      identifier: null == identifier
          ? _value.identifier
          : identifier // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      punishment: null == punishment
          ? _value.punishment
          : punishment // ignore: cast_nullable_to_non_nullable
              as PunishmentModel,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PunishmentModelCopyWith<$Res> get punishment {
    return $PunishmentModelCopyWith<$Res>(_value.punishment, (value) {
      return _then(_value.copyWith(punishment: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PunishmentExceptionImplCopyWith<$Res>
    implements $PunishmentExceptionCopyWith<$Res> {
  factory _$$PunishmentExceptionImplCopyWith(_$PunishmentExceptionImpl value,
          $Res Function(_$PunishmentExceptionImpl) then) =
      __$$PunishmentExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String identifier, String message, PunishmentModel punishment});

  @override
  $PunishmentModelCopyWith<$Res> get punishment;
}

/// @nodoc
class __$$PunishmentExceptionImplCopyWithImpl<$Res>
    extends _$PunishmentExceptionCopyWithImpl<$Res, _$PunishmentExceptionImpl>
    implements _$$PunishmentExceptionImplCopyWith<$Res> {
  __$$PunishmentExceptionImplCopyWithImpl(_$PunishmentExceptionImpl _value,
      $Res Function(_$PunishmentExceptionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? identifier = null,
    Object? message = null,
    Object? punishment = null,
  }) {
    return _then(_$PunishmentExceptionImpl(
      identifier: null == identifier
          ? _value.identifier
          : identifier // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      punishment: null == punishment
          ? _value.punishment
          : punishment // ignore: cast_nullable_to_non_nullable
              as PunishmentModel,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$PunishmentExceptionImpl extends _PunishmentException {
  const _$PunishmentExceptionImpl(
      {required this.identifier,
      required this.message,
      required this.punishment})
      : super._();

  factory _$PunishmentExceptionImpl.fromJson(Map<String, dynamic> json) =>
      _$$PunishmentExceptionImplFromJson(json);

  @override
  final String identifier;
  @override
  final String message;
  @override
  final PunishmentModel punishment;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PunishmentExceptionImpl &&
            (identical(other.identifier, identifier) ||
                other.identifier == identifier) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.punishment, punishment) ||
                other.punishment == punishment));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, identifier, message, punishment);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PunishmentExceptionImplCopyWith<_$PunishmentExceptionImpl> get copyWith =>
      __$$PunishmentExceptionImplCopyWithImpl<_$PunishmentExceptionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PunishmentExceptionImplToJson(
      this,
    );
  }
}

abstract class _PunishmentException extends PunishmentException {
  const factory _PunishmentException(
      {required final String identifier,
      required final String message,
      required final PunishmentModel punishment}) = _$PunishmentExceptionImpl;
  const _PunishmentException._() : super._();

  factory _PunishmentException.fromJson(Map<String, dynamic> json) =
      _$PunishmentExceptionImpl.fromJson;

  @override
  String get identifier;
  @override
  String get message;
  @override
  PunishmentModel get punishment;
  @override
  @JsonKey(ignore: true)
  _$$PunishmentExceptionImplCopyWith<_$PunishmentExceptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
