// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'restrict_exception.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RestrictExceptionImpl _$$RestrictExceptionImplFromJson(
        Map<String, dynamic> json) =>
    _$RestrictExceptionImpl(
      restrict:
          RestrictModel.fromJson(json['restrict'] as Map<String, dynamic>),
      identifier: json['identifier'] as String,
      message: json['message'] as String,
    );

Map<String, dynamic> _$$RestrictExceptionImplToJson(
        _$RestrictExceptionImpl instance) =>
    <String, dynamic>{
      'restrict': instance.restrict.toJson(),
      'identifier': instance.identifier,
      'message': instance.message,
    };
