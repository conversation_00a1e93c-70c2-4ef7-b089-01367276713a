// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'restrict_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RestrictModelImpl _$$RestrictModelImplFromJson(Map<String, dynamic> json) =>
    _$RestrictModelImpl(
      punishment: json['loginPunishment'] == null
          ? null
          : PunishmentModel.fromJson(
              json['loginPunishment'] as Map<String, dynamic>),
      banned: json['banned'] as bool? ?? false,
    );

Map<String, dynamic> _$$RestrictModelImplToJson(_$RestrictModelImpl instance) =>
    <String, dynamic>{
      'loginPunishment': instance.punishment?.toJson(),
      'banned': instance.banned,
    };
