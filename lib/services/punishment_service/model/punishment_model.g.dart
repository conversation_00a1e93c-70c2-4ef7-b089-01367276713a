// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'punishment_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PunishmentModelImpl _$$PunishmentModelImplFromJson(
        Map<String, dynamic> json) =>
    _$PunishmentModelImpl(
      duration: (json['duration'] as num?)?.toInt(),
      createTime: json['createTime'] == null
          ? null
          : DateTime.parse(json['createTime'] as String),
      expireTime: json['expireTime'] == null
          ? null
          : DateTime.parse(json['expireTime'] as String),
    );

Map<String, dynamic> _$$PunishmentModelImplToJson(
        _$PunishmentModelImpl instance) =>
    <String, dynamic>{
      'duration': instance.duration,
      'createTime': instance.createTime?.toIso8601String(),
      'expireTime': instance.expireTime?.toIso8601String(),
    };
