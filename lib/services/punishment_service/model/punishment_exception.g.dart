// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'punishment_exception.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PunishmentExceptionImpl _$$PunishmentExceptionImplFromJson(
        Map<String, dynamic> json) =>
    _$PunishmentExceptionImpl(
      identifier: json['identifier'] as String,
      message: json['message'] as String,
      punishment:
          PunishmentModel.fromJson(json['punishment'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$PunishmentExceptionImplToJson(
        _$PunishmentExceptionImpl instance) =>
    <String, dynamic>{
      'identifier': instance.identifier,
      'message': instance.message,
      'punishment': instance.punishment.toJson(),
    };
