// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'punishment_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PunishmentModel _$PunishmentModelFromJson(Map<String, dynamic> json) {
  return _PunishmentModel.fromJson(json);
}

/// @nodoc
mixin _$PunishmentModel {
  int? get duration => throw _privateConstructorUsedError; // in hours
  DateTime? get createTime => throw _privateConstructorUsedError;
  DateTime? get expireTime => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PunishmentModelCopyWith<PunishmentModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PunishmentModelCopyWith<$Res> {
  factory $PunishmentModelCopyWith(
          PunishmentModel value, $Res Function(PunishmentModel) then) =
      _$PunishmentModelCopyWithImpl<$Res, PunishmentModel>;
  @useResult
  $Res call({int? duration, DateTime? createTime, DateTime? expireTime});
}

/// @nodoc
class _$PunishmentModelCopyWithImpl<$Res, $Val extends PunishmentModel>
    implements $PunishmentModelCopyWith<$Res> {
  _$PunishmentModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? duration = freezed,
    Object? createTime = freezed,
    Object? expireTime = freezed,
  }) {
    return _then(_value.copyWith(
      duration: freezed == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      expireTime: freezed == expireTime
          ? _value.expireTime
          : expireTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PunishmentModelImplCopyWith<$Res>
    implements $PunishmentModelCopyWith<$Res> {
  factory _$$PunishmentModelImplCopyWith(_$PunishmentModelImpl value,
          $Res Function(_$PunishmentModelImpl) then) =
      __$$PunishmentModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? duration, DateTime? createTime, DateTime? expireTime});
}

/// @nodoc
class __$$PunishmentModelImplCopyWithImpl<$Res>
    extends _$PunishmentModelCopyWithImpl<$Res, _$PunishmentModelImpl>
    implements _$$PunishmentModelImplCopyWith<$Res> {
  __$$PunishmentModelImplCopyWithImpl(
      _$PunishmentModelImpl _value, $Res Function(_$PunishmentModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? duration = freezed,
    Object? createTime = freezed,
    Object? expireTime = freezed,
  }) {
    return _then(_$PunishmentModelImpl(
      duration: freezed == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      expireTime: freezed == expireTime
          ? _value.expireTime
          : expireTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PunishmentModelImpl implements _PunishmentModel {
  const _$PunishmentModelImpl(
      {this.duration, this.createTime, this.expireTime});

  factory _$PunishmentModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$PunishmentModelImplFromJson(json);

  @override
  final int? duration;
// in hours
  @override
  final DateTime? createTime;
  @override
  final DateTime? expireTime;

  @override
  String toString() {
    return 'PunishmentModel(duration: $duration, createTime: $createTime, expireTime: $expireTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PunishmentModelImpl &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.expireTime, expireTime) ||
                other.expireTime == expireTime));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, duration, createTime, expireTime);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PunishmentModelImplCopyWith<_$PunishmentModelImpl> get copyWith =>
      __$$PunishmentModelImplCopyWithImpl<_$PunishmentModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PunishmentModelImplToJson(
      this,
    );
  }
}

abstract class _PunishmentModel implements PunishmentModel {
  const factory _PunishmentModel(
      {final int? duration,
      final DateTime? createTime,
      final DateTime? expireTime}) = _$PunishmentModelImpl;

  factory _PunishmentModel.fromJson(Map<String, dynamic> json) =
      _$PunishmentModelImpl.fromJson;

  @override
  int? get duration;
  @override // in hours
  DateTime? get createTime;
  @override
  DateTime? get expireTime;
  @override
  @JsonKey(ignore: true)
  _$$PunishmentModelImplCopyWith<_$PunishmentModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
