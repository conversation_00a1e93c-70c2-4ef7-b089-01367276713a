// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'restrict_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RestrictModel _$RestrictModelFromJson(Map<String, dynamic> json) {
  return _RestrictModel.fromJson(json);
}

/// @nodoc
mixin _$RestrictModel {
  @JsonKey(name: 'loginPunishment')
  PunishmentModel? get punishment => throw _privateConstructorUsedError;
  bool get banned => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RestrictModelCopyWith<RestrictModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RestrictModelCopyWith<$Res> {
  factory $RestrictModelCopyWith(
          RestrictModel value, $Res Function(RestrictModel) then) =
      _$RestrictModelCopyWithImpl<$Res, RestrictModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'loginPunishment') PunishmentModel? punishment,
      bool banned});

  $PunishmentModelCopyWith<$Res>? get punishment;
}

/// @nodoc
class _$RestrictModelCopyWithImpl<$Res, $Val extends RestrictModel>
    implements $RestrictModelCopyWith<$Res> {
  _$RestrictModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? punishment = freezed,
    Object? banned = null,
  }) {
    return _then(_value.copyWith(
      punishment: freezed == punishment
          ? _value.punishment
          : punishment // ignore: cast_nullable_to_non_nullable
              as PunishmentModel?,
      banned: null == banned
          ? _value.banned
          : banned // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PunishmentModelCopyWith<$Res>? get punishment {
    if (_value.punishment == null) {
      return null;
    }

    return $PunishmentModelCopyWith<$Res>(_value.punishment!, (value) {
      return _then(_value.copyWith(punishment: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RestrictModelImplCopyWith<$Res>
    implements $RestrictModelCopyWith<$Res> {
  factory _$$RestrictModelImplCopyWith(
          _$RestrictModelImpl value, $Res Function(_$RestrictModelImpl) then) =
      __$$RestrictModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'loginPunishment') PunishmentModel? punishment,
      bool banned});

  @override
  $PunishmentModelCopyWith<$Res>? get punishment;
}

/// @nodoc
class __$$RestrictModelImplCopyWithImpl<$Res>
    extends _$RestrictModelCopyWithImpl<$Res, _$RestrictModelImpl>
    implements _$$RestrictModelImplCopyWith<$Res> {
  __$$RestrictModelImplCopyWithImpl(
      _$RestrictModelImpl _value, $Res Function(_$RestrictModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? punishment = freezed,
    Object? banned = null,
  }) {
    return _then(_$RestrictModelImpl(
      punishment: freezed == punishment
          ? _value.punishment
          : punishment // ignore: cast_nullable_to_non_nullable
              as PunishmentModel?,
      banned: null == banned
          ? _value.banned
          : banned // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$RestrictModelImpl implements _RestrictModel {
  const _$RestrictModelImpl(
      {@JsonKey(name: 'loginPunishment') this.punishment, this.banned = false});

  factory _$RestrictModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$RestrictModelImplFromJson(json);

  @override
  @JsonKey(name: 'loginPunishment')
  final PunishmentModel? punishment;
  @override
  @JsonKey()
  final bool banned;

  @override
  String toString() {
    return 'RestrictModel(punishment: $punishment, banned: $banned)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RestrictModelImpl &&
            (identical(other.punishment, punishment) ||
                other.punishment == punishment) &&
            (identical(other.banned, banned) || other.banned == banned));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, punishment, banned);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RestrictModelImplCopyWith<_$RestrictModelImpl> get copyWith =>
      __$$RestrictModelImplCopyWithImpl<_$RestrictModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RestrictModelImplToJson(
      this,
    );
  }
}

abstract class _RestrictModel implements RestrictModel {
  const factory _RestrictModel(
      {@JsonKey(name: 'loginPunishment') final PunishmentModel? punishment,
      final bool banned}) = _$RestrictModelImpl;

  factory _RestrictModel.fromJson(Map<String, dynamic> json) =
      _$RestrictModelImpl.fromJson;

  @override
  @JsonKey(name: 'loginPunishment')
  PunishmentModel? get punishment;
  @override
  bool get banned;
  @override
  @JsonKey(ignore: true)
  _$$RestrictModelImplCopyWith<_$RestrictModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
