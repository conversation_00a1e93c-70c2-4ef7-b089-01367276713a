import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/services/hive_service/i_hive_service.dart';
import 'package:flutter_audio_room/services/signal_protocol_service/hive_signal_protocol_store.dart';
import 'package:flutter_audio_room/services/signal_protocol_service/model/signal_key_key_bundle.dart';
import 'package:flutter_audio_room/services/signal_protocol_service/signal_protocol_service.dart';
import 'package:libsignal_protocol_dart/libsignal_protocol_dart.dart';

/// 会话未初始化错误
class SessionNotInitializedException implements Exception {
  final String message;
  SessionNotInitializedException([this.message = '会话未初始化']);
  @override
  String toString() => message;
}

/// 会话已损坏错误
class SessionCorruptedException implements Exception {
  final String message;
  SessionCorruptedException([this.message = '会话已损坏']);
  @override
  String toString() => message;
}

/// 会话需要重新初始化错误
class SessionNeedsRefreshException implements Exception {
  final String message;
  SessionNeedsRefreshException([this.message = '会话需要重新初始化']);
  @override
  String toString() => message;
}
const int maxValue = 0xFFFFFF;
const int preKeyBatchSize = 100;
int minPreKeyCount = (preKeyBatchSize * 0.2).toInt();

class SignalProtocolServiceImpl implements SignalProtocolService {
  final Map<String, SessionCipher> _sessionCiphers = {};
  final Map<String, HiveSignalProtocolStore> _stores = {};

  Future<HiveSignalProtocolStore> _getStore(String userId) async {
    final store = _stores[userId];
    if (store != null) {
      return store;
    }

    final box = await getIt<IHiveService>().openBox('signal_store_$userId');
    final newStore = HiveSignalProtocolStore(box, userId);
    _stores[userId] = newStore;
    return newStore;
  }

  Future<void> _loadPersistedStore(String userId) async {
    try {
      final store = await _getStore(userId);

      // 首先清理可能存在的损坏数据
      await store.cleanupCorruptedData();

      try {
        // 尝试加载现有的身份密钥对和注册ID
        await store.getIdentityKeyPair();
        final registrationId = await store.getLocalRegistrationId();

        LogUtils.d('registrationId: $registrationId',
            tag: '_loadPersistedStore');
      } catch (e) {
        // 如果加载失败，说明需要初始化新的存储
        LogUtils.d('需要初始化新的存储', tag: 'SignalProtocolService');

        // 生成新的身份密钥对和注册ID
        final identityKeyPair = generateIdentityKeyPair();
        final registrationId = generateRegistrationId(false);

        // 初始化存储
        await store.initialize(identityKeyPair, registrationId);
        LogUtils.d('存储初始化成功', tag: 'SignalProtocolService');
      }
    } catch (e) {
      LogUtils.e('加载持久化存储失败: $e', tag: 'SignalProtocolService');
      rethrow;
    }
  }

  @override
  Future<void> initializeUser(String userId) async {
    await _loadPersistedStore(userId);
  }

  @override
  Future<Map<String, dynamic>> getCurrentKeyBundle(String userId) async {
    final store = await _getStore(userId);
    final bundle = await store.getPreKeyBundle();
    return bundle;
  }

  @override
  Future<int> processPreKeyBundle(
    String localUserId,
    String remoteUserId,
    SignalKeyKeyBundle bundleData,
  ) async {
    final store = await _getStore(localUserId);

    try {
      // 创建远程地址
      final remoteAddress = SignalProtocolAddress(remoteUserId, 1);
      // 从预密钥列表中选择一个可用的预密钥

      final selectedPreKey = bundleData.preKey;
      final preKeyRecord = selectedPreKey == null
          ? null
          : PreKeyRecord.fromBuffer(base64Decode(selectedPreKey));
      final preKeyId = preKeyRecord?.id ?? -1;
      final preKeyPublic = preKeyRecord?.getKeyPair().publicKey;

      // 从数据中重建其他必要的密钥
      final signedPrekey = SignedPreKeyRecord.fromSerialized(
        base64Decode(bundleData.signedPreKey),
      );
      final signedPreKeyId = signedPrekey.id;
      final signedPreKeyPublic = signedPrekey.getKeyPair().publicKey;
      final signedPrekeySignature = signedPrekey.signature;
      final identityKeyPublic =
          IdentityKey.fromBytes(base64Decode(bundleData.identityKey), 0);

      final bundle = PreKeyBundle(
        bundleData.registrationId,
        1,
        preKeyId,
        preKeyPublic,
        signedPreKeyId,
        signedPreKeyPublic,
        signedPrekeySignature,
        identityKeyPublic,
      );

      // 创建会话构建器
      final sessionBuilder = SessionBuilder.fromSignalStore(
        store,
        remoteAddress,
      );

      // 处理预密钥包
      await sessionBuilder.processPreKeyBundle(bundle);
      LogUtils.d('预密钥包处理成功', tag: 'SignalProtocolService');

      // 创建会话加密器
      final sessionCipher = SessionCipher.fromStore(
        store,
        remoteAddress,
      );
      _sessionCiphers['$localUserId:$remoteUserId'] = sessionCipher;

      LogUtils.d('会话初始化成功', tag: 'SignalProtocolService');
      return preKeyId;
    } catch (e) {
      LogUtils.e('处理预密钥包失败: $e', tag: 'SignalProtocolService');
      // 清理可能的部分会话状态
      _sessionCiphers.remove('$localUserId:$remoteUserId');
      final remoteAddress = SignalProtocolAddress(remoteUserId, 1);
      await store.deleteSession(remoteAddress);
      rethrow;
    }
  }

  @override
  Future<(int, String)> encryptMessage(
    String localUserId,
    String remoteUserId,
    String message,
  ) async {
    final store = await _getStore(localUserId);

    try {
      final remoteAddress = SignalProtocolAddress(remoteUserId, 1);
      final sessionCipher = SessionCipher.fromStore(
        store,
        remoteAddress,
      );

      try {
        final ciphertext = await sessionCipher.encrypt(
          Uint8List.fromList(utf8.encode(message)),
        );
        final type = ciphertext.getType();
        LogUtils.d('加密消息成功: $type', tag: 'SignalProtocolService');
        return (type, base64Encode(ciphertext.serialize()));
      } catch (e) {
        LogUtils.e('加密过程失败: $e', tag: 'SignalProtocolService');
        if (e.toString().contains('InvalidKeyException')) {
          throw SessionCorruptedException('密钥无效，会话可能已损坏');
        }
        throw SessionNeedsRefreshException('加密失败，需要重新初始化会话');
      }
    } catch (e) {
      LogUtils.e('加密消息失败: $e', tag: 'SignalProtocolService');
      rethrow;
    }
  }

  @override
  Future<String> decryptMessage(
    String localUserId,
    String remoteUserId,
    String encryptedMessage,
    bool isPreKeyMessage,
  ) async {
    try {
      final store = await _getStore(localUserId);
      final remoteAddress = SignalProtocolAddress(remoteUserId, 1);
      final sessionCipher = SessionCipher.fromStore(
        store,
        remoteAddress,
      );

      final messageBytes = base64Decode(encryptedMessage);

      if (isPreKeyMessage) {
        final preKeyMessage = PreKeySignalMessage(messageBytes);
        LogUtils.d('收到预密钥消息，建立新会话', tag: 'SignalProtocolService');
        final plaintext = await sessionCipher.decrypt(preKeyMessage);
        final decryptedMessage = utf8.decode(plaintext);
        return decryptedMessage;
      }

      final message = SignalMessage.fromSerialized(messageBytes);
      LogUtils.d('收到普通消息，使用现有会话解密', tag: 'SignalProtocolService');
      final plaintext = await sessionCipher.decryptFromSignal(message);
      final decryptedMessage = utf8.decode(plaintext);
      return decryptedMessage;
    } catch (e) {
      LogUtils.e('初始化解密过程失败: $e', tag: 'SignalProtocolService');
      rethrow;
    }
  }


  @override
  Future<(int, Uint8List)> encryptData(
    String localUserId,
    String remoteUserId,
    Uint8List data,
  ) async {
    try {
      final store = await _getStore(localUserId);
      final remoteAddress = SignalProtocolAddress(remoteUserId, 1);
      
      // 检查会话状态
      try {
        final session = await store.loadSession(remoteAddress);
        if (!session.sessionState.hasSenderChain()) {
          throw SessionNotInitializedException('发送方链不存在，需要重新初始化会话');
        }
      } catch (e) {
        if (e is SessionNeedsRefreshException) {
          rethrow;
        }
        throw SessionNotInitializedException('会话不存在，需要初始化');
      }

      final sessionCipher = SessionCipher.fromStore(
        store,
        remoteAddress,
      );

      try {
        final ciphertext = await sessionCipher.encrypt(data);
        final type = ciphertext.getType();
        LogUtils.d('加密数据成功: $type', tag: 'SignalProtocolService');
        return (type, ciphertext.serialize());
      } catch (e) {
        LogUtils.e('加密过程失败: $e', tag: 'SignalProtocolService');
        if (e.toString().contains('InvalidKeyException')) {
          throw SessionCorruptedException('密钥无效，会话可能已损坏');
        }
        throw SessionNeedsRefreshException('加密失败，需要重新初始化会话');
      }
    } catch (e) {
      LogUtils.e('加密数据失败: $e', tag: 'SignalProtocolService');
      rethrow;
    }
  }

  @override
  Future<Uint8List> decryptData(
    String localUserId,
    String remoteUserId,
    Uint8List encryptedData,
    bool isPreKeyMessage,
  ) async {
    try {
      final store = await _getStore(localUserId);
      final remoteAddress = SignalProtocolAddress(remoteUserId, 1);
      final sessionCipher = SessionCipher.fromStore(
        store,
        remoteAddress,
      );

      try {
        if (isPreKeyMessage) {
          final preKeyMessage = PreKeySignalMessage(encryptedData);
          LogUtils.d('尝试作为预密钥消息解密数据', tag: 'SignalProtocolService');
          final plaintext = await sessionCipher.decrypt(preKeyMessage);
          LogUtils.d('预密钥消息数据解密成功', tag: 'SignalProtocolService');
          return plaintext;
        }

        final message = SignalMessage.fromSerialized(encryptedData);
        LogUtils.d('尝试作为普通消息解密数据', tag: 'SignalProtocolService');
        final plaintext = await sessionCipher.decryptFromSignal(message);
        LogUtils.d('普通消息数据解密成功', tag: 'SignalProtocolService');
        return plaintext;
      } catch (e) {
        LogUtils.e('解密数据失败: $e', tag: 'SignalProtocolService');
        rethrow;
      }
    } catch (e) {
      LogUtils.e('初始化解密过程失败: $e', tag: 'SignalProtocolService');
      rethrow;
    }
  }

  @override
  Future<bool> hasValidSession(String localUserId, String remoteUserId) async {
    try {
      final store = await _getStore(localUserId);
      final remoteAddress = SignalProtocolAddress(remoteUserId, 1);
      final session = await store.loadSession(remoteAddress);
      LogUtils.d('检查会话状态: ${session.sessionState.hasSenderChain()}',
          tag: 'SignalProtocolService');
      return session.sessionState.hasSenderChain();
    } catch (e) {
      LogUtils.d('检查会话状态失败: $e', tag: 'SignalProtocolService');
      return false;
    }
  }

  @override
  Future<void> deleteConversationSession(
    String localUserId,
    String remoteUserId,
  ) async {
    final store = await _getStore(localUserId);
    final remoteAddress = SignalProtocolAddress(remoteUserId, 1);
    await store.deleteSession(remoteAddress);
    _sessionCiphers.remove('$localUserId:$remoteUserId');
    LogUtils.d('会话删除成功', tag: 'SignalProtocolService');
  }

  @override
  Future<void> clearUserSession(String userId) async {
    try {
      final store = await _getStore(userId);
      final box = store.box;
      await box.clear();
      _stores.remove(userId);
      _sessionCiphers.removeWhere(
        (key, value) => key.startsWith('$userId:') || key.endsWith(':$userId'),
      );
    } catch (e) {
      LogUtils.e('清除用户会话失败: $e', tag: 'SignalProtocolService');
      rethrow;
    }
  }

  /// 完全清除用户数据（仅在必要时使用，如用户注销账号）
  @override
  Future<void> purgeUserData(String userId) async {
    try {
      LogUtils.d('开始完全清除用户数据: $userId', tag: 'SignalProtocolService');

      // 使用安全清理方法
      final store = _stores[userId];
      if (store != null) {
        await store.secureDeleteUserData();
      }

      // 清理内存缓存
      _stores.remove(userId);

      // 清理持久化数据
      final box = await getIt<IHiveService>().openBox('signal_store_$userId');
      if (box.isOpen) {
        await box.close();
        await getIt<IHiveService>().deleteBox('signal_store_$userId');
      }

      LogUtils.d('用户数据完全清除完成: $userId', tag: 'SignalProtocolService');
    } catch (e) {
      LogUtils.e('完全清除用户数据失败: $e', tag: 'SignalProtocolService');
      rethrow;
    }
  }


  @override
  Future<PreKeyStatus> checkPreKeyStatus(String userId) async {
    final store = await _getStore(userId);

    try {
      final preKeys = await store.loadPreKeys();
      final validPreKeyIds = preKeys.map((e) => e.id).toList();
      // 获取当前最大的prekey id
      final maxPreKeyId = await store.getMaxPreKeyId();

      SignedPreKeyRecord? signedPreKey;
      DateTime? signedPreKeyTimestamp;
      try {
        signedPreKey = await store.loadSignedPreKey(maxPreKeyId);
        signedPreKeyTimestamp = DateTime.fromMillisecondsSinceEpoch(
          signedPreKey.timestamp.toInt(),
        );
      } catch (e) {
        // 如果签名预密钥不存在，将其时间戳设置为很久以前，以确保更新
        signedPreKeyTimestamp = DateTime.fromMillisecondsSinceEpoch(0);
      }

      final now = DateTime.now();
      final signedPreKeyAge = now.difference(signedPreKeyTimestamp);

      LogUtils.d(
        '预密钥状态检查: 剩余预密钥数量: ${validPreKeyIds.length}, 签名预密钥年龄: ${signedPreKeyAge.inDays} 天, 预密钥id: $validPreKeyIds',
        tag: 'SignalProtocolService',
      );

      return PreKeyStatus(
        remainingPreKeys: validPreKeyIds.length,
        signedPreKeyAge: signedPreKeyAge,
        needsRotation: signedPreKeyAge.inDays >= 30,
        needsMorePreKeys:
            validPreKeyIds.length < minPreKeyCount, // 当预密钥少于10%时生成新的
      );
    } catch (e) {
      LogUtils.e('检查预密钥状态失败: $e', tag: 'SignalProtocolService');
      // 如果检查失败，返回需要更新的状态
      return PreKeyStatus(
        remainingPreKeys: 0,
        signedPreKeyAge: Duration.zero,
        needsRotation: true,
        needsMorePreKeys: true,
      );
    }
  }

  @override
  Future<Map<String, dynamic>> updateKeys(
    String userId, {
    required PreKeyStatus status,
  }) async {
    try {
      final store = await _getStore(userId);

      // 获取身份密钥对和注册ID（bundle生成需要）
      final identityKeyPair = await store.getIdentityKeyPair();
      final registrationId = await store.getLocalRegistrationId();

      List<PreKeyRecord> generatedPreKeys = [];
      
      if (status.needsMorePreKeys) {
        // 获取当前最大的prekey id
        final maxPreKeyId = await store.getMaxPreKeyId();
        final startId = (maxPreKeyId + 1) % maxValue;

        // 生成预密钥但不立即存储
        generatedPreKeys = generatePreKeys(startId, preKeyBatchSize);
        for (final preKey in generatedPreKeys) {
          await store.storePreKey(preKey.id, preKey);
        }
      } else {
        generatedPreKeys = await store.loadPreKeys();
      }
      
      SignedPreKeyRecord? newSignedPreKey;
      if (status.needsRotation) {
        // 生成新的签名预密钥但不立即存储
        final maxPreKeyId = await store.getMaxPreKeyId();
        final startId = (maxPreKeyId + 1) % maxValue;
        newSignedPreKey = generateSignedPreKey(identityKeyPair, startId);
        await store.storeSignedPreKey(newSignedPreKey.id, newSignedPreKey);
      } else {
        final maxPreKeyId = await store.getMaxPreKeyId();
        final startId = (maxPreKeyId + 1) % maxValue;
        newSignedPreKey = await store.loadSignedPreKey(startId);
      }

      if (status.needsRotation || status.needsMorePreKeys) {
        return {
          'registrationId': registrationId,
          'deviceId': 1,
          'preKeys': generatedPreKeys
              .map((preKey) => base64Encode(preKey.serialize()))
              .toList(),
          'signedPreKey': base64Encode(newSignedPreKey.serialize()),
          'identityKey':
              base64Encode(identityKeyPair.getPublicKey().serialize()),
        };
      }

      final currentBundle = await getCurrentKeyBundle(userId);

      return currentBundle;
    } catch (e) {
      LogUtils.e('更新密钥失败: $e', tag: 'SignalProtocolService');
      rethrow;
    }
  }

  @override
  Future<int> recreateSession(
    String localUserId,
    String remoteUserId,
    SignalKeyKeyBundle bundleData,
  ) async {
    // 清除现有会话
    final sessionId = '$localUserId:$remoteUserId';
    _sessionCiphers.remove(sessionId);

    final store = await _getStore(localUserId);

    try {
      // 清除现有会话状态
      final remoteAddress = SignalProtocolAddress(remoteUserId, 1);
      await store.deleteSession(remoteAddress);
    } catch (e) {
      // 忽略删除不存在的会话时的错误
    }

    // 使用新的预密钥包建立会话
    return await processPreKeyBundle(localUserId, remoteUserId, bundleData);
  }

  @override
  void dispose() {
    for (var store in _stores.values) {
      store.box.close();
    }
    _stores.clear();
    _sessionCiphers.clear();
  }
}
