// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'signal_message_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$signalMessageHash() => r'1fb31b24b4d11cb0b01fd57040e0b3571433615c';

/// See also [SignalMessage].
@ProviderFor(SignalMessage)
final signalMessageProvider = NotifierProvider<SignalMessage, void>.internal(
  SignalMessage.new,
  name: r'signalMessageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$signalMessageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SignalMessage = Notifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
