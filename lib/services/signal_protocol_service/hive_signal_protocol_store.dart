import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/services/crypto_service/i_crypto_service.dart';
import 'package:flutter_audio_room/services/secure_storage_service/i_secure_storage_service.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:libsignal_protocol_dart/libsignal_protocol_dart.dart';

class SecurityException implements Exception {
  final String message;
  SecurityException(this.message);

  @override
  String toString() => 'SecurityException: $message';
}

class HiveSignalProtocolStore implements SignalProtocolStore, SenderKeyStore {
  final Box box;
  final String userId;
  final ISecureStorageService _secureStorage;
  final ICryptoService _cryptoService;

  // 关键密钥存储在 SecureStorage 中
  static const String _identityKeyPairSecureKey = 'signal_identity_key_pair_';
  static const String _registrationIdSecureKey = 'signal_registration_id_';
  static const String _masterKeySecureKey = 'signal_master_key_';

  // 会话数据存储在加密的 Hive 中
  static const String _sessionPrefix = 'session_';
  static const String _preKeyPrefix = 'prekey_';
  static const String _signedPreKeyPrefix = 'signed_prekey_';
  static const String _identityKeyPrefix = 'identity_key_';
  static const String _trustedKeysPrefix = 'trusted_';
  static const String _senderKeyPrefix = 'sender_key_';

  HiveSignalProtocolStore(
    this.box,
    this.userId,
  )   : _secureStorage = getIt<ISecureStorageService>(),
        _cryptoService = getIt<ICryptoService>();

  // 获取或生成主密钥
  Future<String> _getMasterKey() async {
    final key = '$_masterKeySecureKey$userId';
    String? masterKey = await _secureStorage.read(key: key);

    if (masterKey == null) {
      // 生成新的主密钥
      final keyBytes = _cryptoService.generateSecureRandomBytes(32).getRight()!;
      masterKey = base64Encode(keyBytes);
      await _secureStorage.write(key: key, value: masterKey);
      LogUtils.d('生成新的主密钥', tag: 'HiveSignalProtocolStore');
    }

    return masterKey;
  }

  // 加密数据
  Future<String> _encryptData(String data, String masterKey) async {
    try {
      final keyBytes = base64Decode(masterKey);
      final key = Key(keyBytes);
      final iv = IV.fromSecureRandom(16);
      final encrypter = Encrypter(AES(key));

      final encrypted = encrypter.encrypt(data, iv: iv);

      // 返回 IV + 加密数据
      return base64Encode(iv.bytes + encrypted.bytes);
    } catch (e) {
      LogUtils.e('数据加密失败: $e', tag: 'HiveSignalProtocolStore');
      throw SecurityException('数据加密失败');
    }
  }

  // 解密数据
  Future<String> _decryptData(String encryptedData, String masterKey) async {
    try {
      final keyBytes = base64Decode(masterKey);
      final key = Key(keyBytes);

      final allBytes = base64Decode(encryptedData);
      if (allBytes.length < 16) {
        throw SecurityException('加密数据格式无效');
      }

      final iv = IV(allBytes.sublist(0, 16));
      final encryptedBytes = allBytes.sublist(16);

      final encrypter = Encrypter(AES(key));
      final encrypted = Encrypted(encryptedBytes);

      return encrypter.decrypt(encrypted, iv: iv);
    } catch (e) {
      LogUtils.e('数据解密失败: $e', tag: 'HiveSignalProtocolStore');
      throw SecurityException('数据解密失败');
    }
  }

  // 带完整性校验的存储
  Future<void> _storeWithIntegrity(String key, String data) async {
    final hash = sha256.convert(utf8.encode(data)).toString();
    // 先存储hash，再存储数据，确保读取时不会出现数据存在但hash不存在的情况
    await box.put('${key}_hash', hash);
    await box.put(key, data);
  }

  // 带完整性校验的读取
  Future<String?> _loadWithIntegrity(String key) async {
    final data = await box.get(key);
    final storedHash = await box.get('${key}_hash');

    if (data == null) return null;

    // 如果hash不存在，可能是旧数据或者存储过程中断，删除损坏的数据
    if (storedHash == null) {
      LogUtils.w('数据缺少完整性校验hash，删除损坏数据: $key', tag: 'HiveSignalProtocolStore');
      await box.delete(key);
      return null;
    }

    final computedHash = sha256.convert(utf8.encode(data)).toString();
    if (storedHash != computedHash) {
      LogUtils.e('数据完整性校验失败，删除损坏数据: $key', tag: 'HiveSignalProtocolStore');
      // 删除损坏的数据和hash
      await box.delete(key);
      await box.delete('${key}_hash');
      throw SecurityException('数据完整性校验失败');
    }

    return data;
  }

  // Session 存储
  @override
  Future<SessionRecord> loadSession(SignalProtocolAddress address) async {
    try {
      final sessionKey = _getSessionKey(address);
      final encryptedData = await _loadWithIntegrity(sessionKey);

      if (encryptedData == null) {
        return SessionRecord();
      }

      final masterKey = await _getMasterKey();
      final decryptedData = await _decryptData(encryptedData, masterKey);

      return SessionRecord.fromSerialized(base64Decode(decryptedData));
    } catch (e) {
      LogUtils.e('加载会话失败: $e', tag: 'HiveSignalProtocolStore');
      return SessionRecord();
    }
  }

  @override
  Future<List<int>> getSubDeviceSessions(String name) async {
    final sessions = <int>[];
    final prefix = '$_sessionPrefix$userId:$name:';

    for (final key in box.keys) {
      if (key.toString().startsWith(prefix)) {
        final deviceId = int.tryParse(key.toString().substring(prefix.length));
        if (deviceId != null) {
          sessions.add(deviceId);
        }
      }
    }

    return sessions;
  }

  @override
  Future<void> storeSession(
      SignalProtocolAddress address, SessionRecord record) async {
    try {
      final sessionKey = _getSessionKey(address);
      final masterKey = await _getMasterKey();

      // 使用主密钥加密会话数据
      final serializedData = base64Encode(record.serialize());
      final encryptedData = await _encryptData(serializedData, masterKey);

      await _storeWithIntegrity(sessionKey, encryptedData);
    } catch (e) {
      LogUtils.e('保存会话失败: $e', tag: 'HiveSignalProtocolStore');
      rethrow;
    }
  }

  @override
  Future<bool> containsSession(SignalProtocolAddress address) async {
    final key = _getSessionKey(address);
    if (!box.containsKey(key)) return false;

    final sessionRecord = await loadSession(address);
    return sessionRecord.sessionState.hasSenderChain() &&
        sessionRecord.sessionState.getSessionVersion() ==
            CiphertextMessage.currentVersion;
  }

  @override
  Future<void> deleteSession(SignalProtocolAddress address) async {
    final key = _getSessionKey(address);
    await box.delete(key);
    await box.delete('${key}_hash'); // 同时删除完整性校验数据
  }

  @override
  Future<void> deleteAllSessions(String name) async {
    final prefix = '$_sessionPrefix$userId:$name:';
    final keys = box.keys.where((key) => key.toString().startsWith(prefix));
    for (final key in keys) {
      await box.delete(key);
      await box.delete('${key}_hash'); // 同时删除完整性校验数据
    }
  }

  Future<List<PreKeyRecord>> loadPreKeys() async {
    final prefix = '$_preKeyPrefix$userId:';
    final keys = box.keys.where((key) =>
        key.toString().startsWith(prefix) && !key.toString().endsWith('_hash'));
    final records = <PreKeyRecord>[];
    final masterKey = await _getMasterKey();

    for (final key in keys) {
      try {
        final encryptedData = await _loadWithIntegrity(key.toString());
        if (encryptedData != null) {
          final decryptedData = await _decryptData(encryptedData, masterKey);
          records.add(PreKeyRecord.fromBuffer(base64Decode(decryptedData)));
        }
      } catch (e) {
        LogUtils.e('加载预密钥失败: $e', tag: 'HiveSignalProtocolStore');
      }
    }
    return records;
  }

  // PreKey 存储 - 使用加密
  @override
  Future<PreKeyRecord> loadPreKey(int preKeyId) async {
    try {
      final key = _getPreKeyKey(preKeyId);
      final encryptedData = await _loadWithIntegrity(key);

      if (encryptedData == null) {
        throw Exception('预密钥 $preKeyId 不存在');
      }

      final masterKey = await _getMasterKey();
      final decryptedData = await _decryptData(encryptedData, masterKey);

      return PreKeyRecord.fromBuffer(base64Decode(decryptedData));
    } catch (e) {
      LogUtils.e('加载预密钥失败: $e', tag: 'HiveSignalProtocolStore');
      rethrow;
    }
  }

  @override
  Future<void> storePreKey(int preKeyId, PreKeyRecord record) async {
    try {
      final key = _getPreKeyKey(preKeyId);
      final masterKey = await _getMasterKey();

      final serializedData = base64Encode(record.serialize());
      final encryptedData = await _encryptData(serializedData, masterKey);

      await _storeWithIntegrity(key, encryptedData);
    } catch (e) {
      LogUtils.e('保存预密钥失败: $e', tag: 'HiveSignalProtocolStore');
      rethrow;
    }
  }

  @override
  Future<bool> containsPreKey(int preKeyId) async {
    final key = _getPreKeyKey(preKeyId);
    return box.containsKey(key);
  }

  @override
  Future<void> removePreKey(int preKeyId) async {
    final key = _getPreKeyKey(preKeyId);
    await box.delete(key);
    await box.delete('${key}_hash'); // 同时删除完整性校验数据
  }

  // SignedPreKey 存储 - 使用加密
  @override
  Future<SignedPreKeyRecord> loadSignedPreKey(int signedPreKeyId) async {
    try {
      final key = _getSignedPreKeyKey(signedPreKeyId);
      final encryptedData = await _loadWithIntegrity(key);

      if (encryptedData == null) {
        throw Exception('签名预密钥不存在');
      }

      final masterKey = await _getMasterKey();
      final decryptedData = await _decryptData(encryptedData, masterKey);

      return SignedPreKeyRecord.fromSerialized(base64Decode(decryptedData));
    } catch (e) {
      LogUtils.e('加载签名预密钥失败: $e', tag: 'HiveSignalProtocolStore');
      rethrow;
    }
  }

  @override
  Future<void> storeSignedPreKey(
    int signedPreKeyId,
    SignedPreKeyRecord record,
  ) async {
    try {
      final key = _getSignedPreKeyKey(signedPreKeyId);
      final masterKey = await _getMasterKey();

      final serializedData = base64Encode(record.serialize());
      final encryptedData = await _encryptData(serializedData, masterKey);

      await _storeWithIntegrity(key, encryptedData);
    } catch (e) {
      LogUtils.e('保存签名预密钥失败: $e', tag: 'HiveSignalProtocolStore');
      rethrow;
    }
  }

  @override
  Future<bool> containsSignedPreKey(int signedPreKeyId) async {
    final key = _getSignedPreKeyKey(signedPreKeyId);
    return box.containsKey(key);
  }

  @override
  Future<void> removeSignedPreKey(int signedPreKeyId) async {
    final key = _getSignedPreKeyKey(signedPreKeyId);
    await box.delete(key);
    await box.delete('${key}_hash'); // 同时删除完整性校验数据
  }

  @override
  Future<List<SignedPreKeyRecord>> loadSignedPreKeys() async {
    final prefix = '$_signedPreKeyPrefix$userId:';
    final keys = box.keys.where((key) =>
        key.toString().startsWith(prefix) && !key.toString().endsWith('_hash'));
    final records = <SignedPreKeyRecord>[];
    final masterKey = await _getMasterKey();

    for (final key in keys) {
      try {
        final encryptedData = await _loadWithIntegrity(key.toString());
        if (encryptedData != null) {
          final decryptedData = await _decryptData(encryptedData, masterKey);
          records.add(
              SignedPreKeyRecord.fromSerialized(base64Decode(decryptedData)));
        }
      } catch (e) {
        LogUtils.e('加载签名预密钥失败: $e', tag: 'HiveSignalProtocolStore');
      }
    }

    return records;
  }

  // Identity Key 存储 - 使用 SecureStorage
  @override
  Future<IdentityKeyPair> getIdentityKeyPair() async {
    try {
      final key = '$_identityKeyPairSecureKey$userId';
      final data = await _secureStorage.read(key: key);

      if (data == null) {
        final identityKeyPair = generateIdentityKeyPair();
        await _secureStorage.write(
            key: key, value: base64Encode(identityKeyPair.serialize()));
        LogUtils.d('生成新的身份密钥对', tag: 'HiveSignalProtocolStore');
        return identityKeyPair;
      }

      return IdentityKeyPair.fromSerialized(base64Decode(data));
    } catch (e) {
      LogUtils.e('获取身份密钥对失败: $e', tag: 'HiveSignalProtocolStore');
      rethrow;
    }
  }

  @override
  Future<int> getLocalRegistrationId() async {
    try {
      final key = '$_registrationIdSecureKey$userId';
      final data = await _secureStorage.read(key: key);

      if (data == null) {
        final registrationId = generateRegistrationId(false);
        await _secureStorage.write(key: key, value: registrationId.toString());
        LogUtils.d('生成新的注册ID', tag: 'HiveSignalProtocolStore');
        return registrationId;
      }

      return int.parse(data);
    } catch (e) {
      LogUtils.e('获取注册ID失败: $e', tag: 'HiveSignalProtocolStore');
      rethrow;
    }
  }

  @override
  Future<bool> saveIdentity(
      SignalProtocolAddress address, IdentityKey? identityKey) async {
    final key = _getIdentityKey(address);
    final existing = await box.get(key);

    if (identityKey == null) {
      await box.delete(key);
      return false;
    }

    await box.put(key, base64Encode(identityKey.serialize()));
    return existing != null &&
        existing != base64Encode(identityKey.serialize());
  }

  @override
  Future<bool> isTrustedIdentity(
    SignalProtocolAddress address,
    IdentityKey? identityKey,
    Direction direction,
  ) async {
    if (identityKey == null) {
      return false;
    }

    final key = _getTrustedKey(address);
    final trusted = await box.get(key);

    if (trusted == null) {
      return true; // 首次信任
    }

    return trusted == base64Encode(identityKey.serialize());
  }

  @override
  Future<IdentityKey?> getIdentity(SignalProtocolAddress address) async {
    final key = _getIdentityKey(address);
    final data = await box.get(key);
    if (data == null) {
      return null;
    }
    return IdentityKey.fromBytes(base64Decode(data), 0);
  }

  // 初始化方法 - 使用 SecureStorage
  Future<void> initialize(
      IdentityKeyPair identityKeyPair, int registrationId) async {
    try {
      await _secureStorage.write(
          key: '$_identityKeyPairSecureKey$userId',
          value: base64Encode(identityKeyPair.serialize()));
      await _secureStorage.write(
          key: '$_registrationIdSecureKey$userId',
          value: registrationId.toString());
    } catch (e) {
      LogUtils.e('初始化失败: $e', tag: 'HiveSignalProtocolStore');
      rethrow;
    }
  }

  // 辅助方法
  String _getSessionKey(SignalProtocolAddress address) {
    return '$_sessionPrefix$userId:${address.getName()}:${address.getDeviceId()}';
  }

  String _getPreKeyKey(int preKeyId) {
    return '$_preKeyPrefix$userId:$preKeyId';
  }

  String _getSignedPreKeyKey(int signedPreKeyId) {
    return '$_signedPreKeyPrefix$userId:$signedPreKeyId';
  }

  String _getIdentityKey(SignalProtocolAddress address) {
    return '$_identityKeyPrefix$userId:${address.getName()}';
  }

  String _getTrustedKey(SignalProtocolAddress address) {
    return '$_trustedKeysPrefix$userId:${address.getName()}';
  }

  // 安全清理用户数据
  Future<void> secureDeleteUserData() async {
    try {
      // 清理 SecureStorage 中的关键密钥
      await _secureStorage.delete(key: '$_identityKeyPairSecureKey$userId');
      await _secureStorage.delete(key: '$_registrationIdSecureKey$userId');
      await _secureStorage.delete(key: '$_masterKeySecureKey$userId');

      // 清理 Hive 中的加密数据
      await box.clear();
    } catch (e) {
      LogUtils.e('安全清理用户数据失败: $e', tag: 'HiveSignalProtocolStore');
      rethrow;
    }
  }

  // 密钥轮换方法
  Future<void> rotateMasterKey() async {
    try {
      final oldMasterKey = await _getMasterKey();

      // 生成新的主密钥
      final newKeyBytes =
          _cryptoService.generateSecureRandomBytes(32).getRight()!;
      final newMasterKey = base64Encode(newKeyBytes);

      // 重新加密所有数据
      await _reencryptAllData(oldMasterKey, newMasterKey);

      // 更新主密钥
      await _secureStorage.write(
          key: '$_masterKeySecureKey$userId', value: newMasterKey);
    } catch (e) {
      LogUtils.e('主密钥轮换失败: $e', tag: 'HiveSignalProtocolStore');
      rethrow;
    }
  }

  // 重新加密所有数据
  Future<void> _reencryptAllData(
      String oldMasterKey, String newMasterKey) async {
    final allKeys = box.keys
        .where((key) =>
            key.toString().startsWith(_sessionPrefix) ||
            key.toString().startsWith(_preKeyPrefix) ||
            key.toString().startsWith(_signedPreKeyPrefix) ||
            key.toString().startsWith(_senderKeyPrefix))
        .toList();

    for (final key in allKeys) {
      try {
        final encryptedData = await box.get(key);
        if (encryptedData != null && !key.toString().endsWith('_hash')) {
          // 用旧密钥解密
          final decryptedData = await _decryptData(encryptedData, oldMasterKey);
          // 用新密钥加密
          final reencryptedData =
              await _encryptData(decryptedData, newMasterKey);
          // 重新存储
          await _storeWithIntegrity(key.toString(), reencryptedData);
        }
      } catch (e) {
        LogUtils.e('重新加密数据失败: $key, $e', tag: 'HiveSignalProtocolStore');
      }
    }
  }

  Future<int> getMaxPreKeyId() async {
    int maxId = 0;
    final prefix = '$_preKeyPrefix$userId:';

    for (final key in box.keys) {
      if (key.toString().startsWith(prefix)) {
        final preKeyId = int.tryParse(key.toString().substring(prefix.length));
        if (preKeyId != null && preKeyId > maxId) {
          maxId = preKeyId;
        }
      }
    }

    return maxId;
  }

  Future<Map<String, dynamic>> getPreKeyBundle() async {
    final maxPreKeyId = await getMaxPreKeyId();
    final signedPreKey = await loadSignedPreKey(maxPreKeyId);
    final identityKey = await getIdentityKeyPair();

    final preKeys = await loadPreKeys();
    final base64PreKeys = <String>[];

    for (var i = 0; i < preKeys.length; i++) {
      try {
        final preKey = preKeys[i];
        base64PreKeys.add(base64Encode(preKey.serialize()));
      } catch (e) {
        LogUtils.e('加载预密钥失败: $e', tag: 'HiveSignalProtocolStore');
      }
    }

    return {
      'registrationId': await getLocalRegistrationId(),
      'deviceId': 1,
      'identityKey': base64Encode(identityKey.getPublicKey().serialize()),
      'signedPreKey': base64Encode(signedPreKey.serialize()),
      'preKeys': base64PreKeys,
    };
  }

  // SenderKeyStore implementation - 使用加密
  @override
  Future<void> storeSenderKey(
    SenderKeyName senderKeyName,
    SenderKeyRecord record,
  ) async {
    try {
      final key = _getSenderKeyKey(senderKeyName);
      final masterKey = await _getMasterKey();

      final serializedData = base64Encode(record.serialize());
      final encryptedData = await _encryptData(serializedData, masterKey);

      await _storeWithIntegrity(key, encryptedData);
    } catch (e) {
      LogUtils.e('保存发送者密钥失败: $e', tag: 'HiveSignalProtocolStore');
      rethrow;
    }
  }

  @override
  Future<SenderKeyRecord> loadSenderKey(SenderKeyName senderKeyName) async {
    try {
      final key = _getSenderKeyKey(senderKeyName);
      final encryptedData = await _loadWithIntegrity(key);

      if (encryptedData == null) {
        return SenderKeyRecord();
      }

      final masterKey = await _getMasterKey();
      final decryptedData = await _decryptData(encryptedData, masterKey);

      return SenderKeyRecord.fromSerialized(base64Decode(decryptedData));
    } catch (e) {
      LogUtils.e('加载发送者密钥失败: $e', tag: 'HiveSignalProtocolStore');
      return SenderKeyRecord();
    }
  }

  String _getSenderKeyKey(SenderKeyName senderKeyName) {
    return '$_senderKeyPrefix$userId:${senderKeyName.groupId}:${senderKeyName.sender.getName()}:${senderKeyName.sender.getDeviceId()}';
  }

  // 清理损坏的数据
  Future<void> cleanupCorruptedData() async {
    try {
      final allKeys = box.keys.toList();
      final keysToDelete = <String>[];

      for (final key in allKeys) {
        final keyStr = key.toString();

        // 跳过hash键
        if (keyStr.endsWith('_hash')) continue;

        // 检查是否是加密数据键
        if (keyStr.startsWith(_sessionPrefix) ||
            keyStr.startsWith(_preKeyPrefix) ||
            keyStr.startsWith(_signedPreKeyPrefix) ||
            keyStr.startsWith(_senderKeyPrefix)) {
          final data = await box.get(key);
          final storedHash = await box.get('${keyStr}_hash');

          if (data != null && storedHash == null) {
            // 数据存在但hash不存在，标记删除
            keysToDelete.add(keyStr);
            LogUtils.w('发现缺少hash的数据，将删除: $keyStr',
                tag: 'HiveSignalProtocolStore');
          } else if (data != null && storedHash != null) {
            // 验证数据完整性
            final computedHash = sha256.convert(utf8.encode(data)).toString();
            if (storedHash != computedHash) {
              keysToDelete.add(keyStr);
              LogUtils.w('发现损坏的数据，将删除: $keyStr',
                  tag: 'HiveSignalProtocolStore');
            }
          }
        }
      }

      // 删除损坏的数据
      for (final key in keysToDelete) {
        await box.delete(key);
        await box.delete('${key}_hash');
      }

      if (keysToDelete.isNotEmpty) {
        LogUtils.i('清理了 ${keysToDelete.length} 个损坏的数据项',
            tag: 'HiveSignalProtocolStore');
      }
    } catch (e) {
      LogUtils.e('清理损坏数据失败: $e', tag: 'HiveSignalProtocolStore');
    }
  }
}
