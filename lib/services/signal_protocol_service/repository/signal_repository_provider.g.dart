// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'signal_repository_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$signalRepositoryHash() => r'0b7fc1423e8a43bcc52bad7169f43915e5eba8ea';

/// See also [SignalRepository].
@ProviderFor(SignalRepository)
final signalRepositoryProvider =
    AutoDisposeNotifierProvider<SignalRepository, void>.internal(
  SignalRepository.new,
  name: r'signalRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$signalRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SignalRepository = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
