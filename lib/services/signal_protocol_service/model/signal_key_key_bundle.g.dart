// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'signal_key_key_bundle.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SignalKeyKeyBundleImpl _$$SignalKeyKeyBundleImplFromJson(
        Map<String, dynamic> json) =>
    _$SignalKeyKeyBundleImpl(
      identityKey: json['identityKey'] as String,
      signedPreKey: json['signedPreKey'] as String,
      preKey: json['preKey'] as String?,
      registrationId: (json['registrationId'] as num).toInt(),
    );

Map<String, dynamic> _$$SignalKey<PERSON>ey<PERSON>undleImplToJson(
        _$Signal<PERSON>ey<PERSON>eyBundleImpl instance) =>
    <String, dynamic>{
      'identityKey': instance.identityKey,
      'signedPreKey': instance.signedPreKey,
      'preKey': instance.preKey,
      'registrationId': instance.registrationId,
    };
