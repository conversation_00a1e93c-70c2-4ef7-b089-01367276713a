// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'isar_conversation_service.dart';

// **************************************************************************
// _IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, invalid_use_of_protected_member, lines_longer_than_80_chars, constant_identifier_names, avoid_js_rounded_ints, no_leading_underscores_for_local_identifiers, require_trailing_commas, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_in_if_null_operators, library_private_types_in_public_api, prefer_const_constructors
// ignore_for_file: type=lint

extension GetIsarConversationCollection on Isar {
  IsarCollection<int, IsarConversation> get isarConversations =>
      this.collection();
}

const IsarConversationSchema = IsarGeneratedSchema(
  schema: IsarSchema(
    name: 'IsarConversation',
    idName: 'id',
    embedded: false,
    properties: [
      IsarPropertySchema(
        name: 'conversationId',
        type: IsarType.string,
      ),
      IsarPropertySchema(
        name: 'userId',
        type: IsarType.string,
      ),
      IsarPropertySchema(
        name: 'jsonData',
        type: IsarType.string,
      ),
      IsarPropertySchema(
        name: 'updatedAt',
        type: IsarType.long,
      ),
      IsarPropertySchema(
        name: 'pinned',
        type: IsarType.bool,
      ),
    ],
    indexes: [
      IsarIndexSchema(
        name: 'conversationId',
        properties: [
          "conversationId",
        ],
        unique: false,
        hash: false,
      ),
      IsarIndexSchema(
        name: 'userId',
        properties: [
          "userId",
        ],
        unique: false,
        hash: false,
      ),
      IsarIndexSchema(
        name: 'updatedAt',
        properties: [
          "updatedAt",
        ],
        unique: false,
        hash: false,
      ),
      IsarIndexSchema(
        name: 'pinned',
        properties: [
          "pinned",
        ],
        unique: false,
        hash: false,
      ),
    ],
  ),
  converter: IsarObjectConverter<int, IsarConversation>(
    serialize: serializeIsarConversation,
    deserialize: deserializeIsarConversation,
    deserializeProperty: deserializeIsarConversationProp,
  ),
  embeddedSchemas: [],
);

@isarProtected
int serializeIsarConversation(IsarWriter writer, IsarConversation object) {
  IsarCore.writeString(writer, 1, object.conversationId);
  IsarCore.writeString(writer, 2, object.userId);
  IsarCore.writeString(writer, 3, object.jsonData);
  IsarCore.writeLong(writer, 4, object.updatedAt);
  IsarCore.writeBool(writer, 5, object.pinned);
  return object.id;
}

@isarProtected
IsarConversation deserializeIsarConversation(IsarReader reader) {
  final object = IsarConversation();
  object.id = IsarCore.readId(reader);
  object.conversationId = IsarCore.readString(reader, 1) ?? '';
  object.userId = IsarCore.readString(reader, 2) ?? '';
  object.jsonData = IsarCore.readString(reader, 3) ?? '';
  object.updatedAt = IsarCore.readLong(reader, 4);
  object.pinned = IsarCore.readBool(reader, 5);
  return object;
}

@isarProtected
dynamic deserializeIsarConversationProp(IsarReader reader, int property) {
  switch (property) {
    case 0:
      return IsarCore.readId(reader);
    case 1:
      return IsarCore.readString(reader, 1) ?? '';
    case 2:
      return IsarCore.readString(reader, 2) ?? '';
    case 3:
      return IsarCore.readString(reader, 3) ?? '';
    case 4:
      return IsarCore.readLong(reader, 4);
    case 5:
      return IsarCore.readBool(reader, 5);
    default:
      throw ArgumentError('Unknown property: $property');
  }
}

sealed class _IsarConversationUpdate {
  bool call({
    required int id,
    String? conversationId,
    String? userId,
    String? jsonData,
    int? updatedAt,
    bool? pinned,
  });
}

class _IsarConversationUpdateImpl implements _IsarConversationUpdate {
  const _IsarConversationUpdateImpl(this.collection);

  final IsarCollection<int, IsarConversation> collection;

  @override
  bool call({
    required int id,
    Object? conversationId = ignore,
    Object? userId = ignore,
    Object? jsonData = ignore,
    Object? updatedAt = ignore,
    Object? pinned = ignore,
  }) {
    return collection.updateProperties([
          id
        ], {
          if (conversationId != ignore) 1: conversationId as String?,
          if (userId != ignore) 2: userId as String?,
          if (jsonData != ignore) 3: jsonData as String?,
          if (updatedAt != ignore) 4: updatedAt as int?,
          if (pinned != ignore) 5: pinned as bool?,
        }) >
        0;
  }
}

sealed class _IsarConversationUpdateAll {
  int call({
    required List<int> id,
    String? conversationId,
    String? userId,
    String? jsonData,
    int? updatedAt,
    bool? pinned,
  });
}

class _IsarConversationUpdateAllImpl implements _IsarConversationUpdateAll {
  const _IsarConversationUpdateAllImpl(this.collection);

  final IsarCollection<int, IsarConversation> collection;

  @override
  int call({
    required List<int> id,
    Object? conversationId = ignore,
    Object? userId = ignore,
    Object? jsonData = ignore,
    Object? updatedAt = ignore,
    Object? pinned = ignore,
  }) {
    return collection.updateProperties(id, {
      if (conversationId != ignore) 1: conversationId as String?,
      if (userId != ignore) 2: userId as String?,
      if (jsonData != ignore) 3: jsonData as String?,
      if (updatedAt != ignore) 4: updatedAt as int?,
      if (pinned != ignore) 5: pinned as bool?,
    });
  }
}

extension IsarConversationUpdate on IsarCollection<int, IsarConversation> {
  _IsarConversationUpdate get update => _IsarConversationUpdateImpl(this);

  _IsarConversationUpdateAll get updateAll =>
      _IsarConversationUpdateAllImpl(this);
}

sealed class _IsarConversationQueryUpdate {
  int call({
    String? conversationId,
    String? userId,
    String? jsonData,
    int? updatedAt,
    bool? pinned,
  });
}

class _IsarConversationQueryUpdateImpl implements _IsarConversationQueryUpdate {
  const _IsarConversationQueryUpdateImpl(this.query, {this.limit});

  final IsarQuery<IsarConversation> query;
  final int? limit;

  @override
  int call({
    Object? conversationId = ignore,
    Object? userId = ignore,
    Object? jsonData = ignore,
    Object? updatedAt = ignore,
    Object? pinned = ignore,
  }) {
    return query.updateProperties(limit: limit, {
      if (conversationId != ignore) 1: conversationId as String?,
      if (userId != ignore) 2: userId as String?,
      if (jsonData != ignore) 3: jsonData as String?,
      if (updatedAt != ignore) 4: updatedAt as int?,
      if (pinned != ignore) 5: pinned as bool?,
    });
  }
}

extension IsarConversationQueryUpdate on IsarQuery<IsarConversation> {
  _IsarConversationQueryUpdate get updateFirst =>
      _IsarConversationQueryUpdateImpl(this, limit: 1);

  _IsarConversationQueryUpdate get updateAll =>
      _IsarConversationQueryUpdateImpl(this);
}

class _IsarConversationQueryBuilderUpdateImpl
    implements _IsarConversationQueryUpdate {
  const _IsarConversationQueryBuilderUpdateImpl(this.query, {this.limit});

  final QueryBuilder<IsarConversation, IsarConversation, QOperations> query;
  final int? limit;

  @override
  int call({
    Object? conversationId = ignore,
    Object? userId = ignore,
    Object? jsonData = ignore,
    Object? updatedAt = ignore,
    Object? pinned = ignore,
  }) {
    final q = query.build();
    try {
      return q.updateProperties(limit: limit, {
        if (conversationId != ignore) 1: conversationId as String?,
        if (userId != ignore) 2: userId as String?,
        if (jsonData != ignore) 3: jsonData as String?,
        if (updatedAt != ignore) 4: updatedAt as int?,
        if (pinned != ignore) 5: pinned as bool?,
      });
    } finally {
      q.close();
    }
  }
}

extension IsarConversationQueryBuilderUpdate
    on QueryBuilder<IsarConversation, IsarConversation, QOperations> {
  _IsarConversationQueryUpdate get updateFirst =>
      _IsarConversationQueryBuilderUpdateImpl(this, limit: 1);

  _IsarConversationQueryUpdate get updateAll =>
      _IsarConversationQueryBuilderUpdateImpl(this);
}

extension IsarConversationQueryFilter
    on QueryBuilder<IsarConversation, IsarConversation, QFilterCondition> {
  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      idEqualTo(
    int value,
  ) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        EqualCondition(
          property: 0,
          value: value,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      idGreaterThan(
    int value,
  ) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        GreaterCondition(
          property: 0,
          value: value,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      idGreaterThanOrEqualTo(
    int value,
  ) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        GreaterOrEqualCondition(
          property: 0,
          value: value,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      idLessThan(
    int value,
  ) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        LessCondition(
          property: 0,
          value: value,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      idLessThanOrEqualTo(
    int value,
  ) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        LessOrEqualCondition(
          property: 0,
          value: value,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      idBetween(
    int lower,
    int upper,
  ) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        BetweenCondition(
          property: 0,
          lower: lower,
          upper: upper,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      conversationIdEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        EqualCondition(
          property: 1,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      conversationIdGreaterThan(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        GreaterCondition(
          property: 1,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      conversationIdGreaterThanOrEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        GreaterOrEqualCondition(
          property: 1,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      conversationIdLessThan(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        LessCondition(
          property: 1,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      conversationIdLessThanOrEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        LessOrEqualCondition(
          property: 1,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      conversationIdBetween(
    String lower,
    String upper, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        BetweenCondition(
          property: 1,
          lower: lower,
          upper: upper,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      conversationIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        StartsWithCondition(
          property: 1,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      conversationIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        EndsWithCondition(
          property: 1,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      conversationIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        ContainsCondition(
          property: 1,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      conversationIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        MatchesCondition(
          property: 1,
          wildcard: pattern,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      conversationIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        const EqualCondition(
          property: 1,
          value: '',
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      conversationIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        const GreaterCondition(
          property: 1,
          value: '',
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      userIdEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        EqualCondition(
          property: 2,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      userIdGreaterThan(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        GreaterCondition(
          property: 2,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      userIdGreaterThanOrEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        GreaterOrEqualCondition(
          property: 2,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      userIdLessThan(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        LessCondition(
          property: 2,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      userIdLessThanOrEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        LessOrEqualCondition(
          property: 2,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      userIdBetween(
    String lower,
    String upper, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        BetweenCondition(
          property: 2,
          lower: lower,
          upper: upper,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      userIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        StartsWithCondition(
          property: 2,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      userIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        EndsWithCondition(
          property: 2,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      userIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        ContainsCondition(
          property: 2,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      userIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        MatchesCondition(
          property: 2,
          wildcard: pattern,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      userIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        const EqualCondition(
          property: 2,
          value: '',
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      userIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        const GreaterCondition(
          property: 2,
          value: '',
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      jsonDataEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        EqualCondition(
          property: 3,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      jsonDataGreaterThan(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        GreaterCondition(
          property: 3,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      jsonDataGreaterThanOrEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        GreaterOrEqualCondition(
          property: 3,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      jsonDataLessThan(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        LessCondition(
          property: 3,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      jsonDataLessThanOrEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        LessOrEqualCondition(
          property: 3,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      jsonDataBetween(
    String lower,
    String upper, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        BetweenCondition(
          property: 3,
          lower: lower,
          upper: upper,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      jsonDataStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        StartsWithCondition(
          property: 3,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      jsonDataEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        EndsWithCondition(
          property: 3,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      jsonDataContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        ContainsCondition(
          property: 3,
          value: value,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      jsonDataMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        MatchesCondition(
          property: 3,
          wildcard: pattern,
          caseSensitive: caseSensitive,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      jsonDataIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        const EqualCondition(
          property: 3,
          value: '',
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      jsonDataIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        const GreaterCondition(
          property: 3,
          value: '',
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      updatedAtEqualTo(
    int value,
  ) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        EqualCondition(
          property: 4,
          value: value,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      updatedAtGreaterThan(
    int value,
  ) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        GreaterCondition(
          property: 4,
          value: value,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      updatedAtGreaterThanOrEqualTo(
    int value,
  ) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        GreaterOrEqualCondition(
          property: 4,
          value: value,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      updatedAtLessThan(
    int value,
  ) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        LessCondition(
          property: 4,
          value: value,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      updatedAtLessThanOrEqualTo(
    int value,
  ) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        LessOrEqualCondition(
          property: 4,
          value: value,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      updatedAtBetween(
    int lower,
    int upper,
  ) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        BetweenCondition(
          property: 4,
          lower: lower,
          upper: upper,
        ),
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterFilterCondition>
      pinnedEqualTo(
    bool value,
  ) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(
        EqualCondition(
          property: 5,
          value: value,
        ),
      );
    });
  }
}

extension IsarConversationQueryObject
    on QueryBuilder<IsarConversation, IsarConversation, QFilterCondition> {}

extension IsarConversationQuerySortBy
    on QueryBuilder<IsarConversation, IsarConversation, QSortBy> {
  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy> sortById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(0);
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy>
      sortByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(0, sort: Sort.desc);
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy>
      sortByConversationId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(
        1,
        caseSensitive: caseSensitive,
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy>
      sortByConversationIdDesc({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(
        1,
        sort: Sort.desc,
        caseSensitive: caseSensitive,
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy> sortByUserId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(
        2,
        caseSensitive: caseSensitive,
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy>
      sortByUserIdDesc({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(
        2,
        sort: Sort.desc,
        caseSensitive: caseSensitive,
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy> sortByJsonData(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(
        3,
        caseSensitive: caseSensitive,
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy>
      sortByJsonDataDesc({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(
        3,
        sort: Sort.desc,
        caseSensitive: caseSensitive,
      );
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(4);
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(4, sort: Sort.desc);
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy>
      sortByPinned() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(5);
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy>
      sortByPinnedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(5, sort: Sort.desc);
    });
  }
}

extension IsarConversationQuerySortThenBy
    on QueryBuilder<IsarConversation, IsarConversation, QSortThenBy> {
  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(0);
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(0, sort: Sort.desc);
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy>
      thenByConversationId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(1, caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy>
      thenByConversationIdDesc({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(1, sort: Sort.desc, caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy> thenByUserId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(2, caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy>
      thenByUserIdDesc({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(2, sort: Sort.desc, caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy> thenByJsonData(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(3, caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy>
      thenByJsonDataDesc({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(3, sort: Sort.desc, caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(4);
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(4, sort: Sort.desc);
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy>
      thenByPinned() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(5);
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterSortBy>
      thenByPinnedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(5, sort: Sort.desc);
    });
  }
}

extension IsarConversationQueryWhereDistinct
    on QueryBuilder<IsarConversation, IsarConversation, QDistinct> {
  QueryBuilder<IsarConversation, IsarConversation, QAfterDistinct>
      distinctByConversationId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(1, caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterDistinct>
      distinctByUserId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(2, caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterDistinct>
      distinctByJsonData({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(3, caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(4);
    });
  }

  QueryBuilder<IsarConversation, IsarConversation, QAfterDistinct>
      distinctByPinned() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(5);
    });
  }
}

extension IsarConversationQueryProperty1
    on QueryBuilder<IsarConversation, IsarConversation, QProperty> {
  QueryBuilder<IsarConversation, int, QAfterProperty> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addProperty(0);
    });
  }

  QueryBuilder<IsarConversation, String, QAfterProperty>
      conversationIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addProperty(1);
    });
  }

  QueryBuilder<IsarConversation, String, QAfterProperty> userIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addProperty(2);
    });
  }

  QueryBuilder<IsarConversation, String, QAfterProperty> jsonDataProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addProperty(3);
    });
  }

  QueryBuilder<IsarConversation, int, QAfterProperty> updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addProperty(4);
    });
  }

  QueryBuilder<IsarConversation, bool, QAfterProperty> pinnedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addProperty(5);
    });
  }
}

extension IsarConversationQueryProperty2<R>
    on QueryBuilder<IsarConversation, R, QAfterProperty> {
  QueryBuilder<IsarConversation, (R, int), QAfterProperty> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addProperty(0);
    });
  }

  QueryBuilder<IsarConversation, (R, String), QAfterProperty>
      conversationIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addProperty(1);
    });
  }

  QueryBuilder<IsarConversation, (R, String), QAfterProperty> userIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addProperty(2);
    });
  }

  QueryBuilder<IsarConversation, (R, String), QAfterProperty>
      jsonDataProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addProperty(3);
    });
  }

  QueryBuilder<IsarConversation, (R, int), QAfterProperty> updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addProperty(4);
    });
  }

  QueryBuilder<IsarConversation, (R, bool), QAfterProperty> pinnedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addProperty(5);
    });
  }
}

extension IsarConversationQueryProperty3<R1, R2>
    on QueryBuilder<IsarConversation, (R1, R2), QAfterProperty> {
  QueryBuilder<IsarConversation, (R1, R2, int), QOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addProperty(0);
    });
  }

  QueryBuilder<IsarConversation, (R1, R2, String), QOperations>
      conversationIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addProperty(1);
    });
  }

  QueryBuilder<IsarConversation, (R1, R2, String), QOperations>
      userIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addProperty(2);
    });
  }

  QueryBuilder<IsarConversation, (R1, R2, String), QOperations>
      jsonDataProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addProperty(3);
    });
  }

  QueryBuilder<IsarConversation, (R1, R2, int), QOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addProperty(4);
    });
  }

  QueryBuilder<IsarConversation, (R1, R2, bool), QOperations> pinnedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addProperty(5);
    });
  }
}
