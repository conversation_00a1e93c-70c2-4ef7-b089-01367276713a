import 'dart:convert';

import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/chat/domain/extension/ephemeral_message_extension.dart';
import 'package:flutter_audio_room/features/chat/domain/extension/message_metadata_extension.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart';
import 'package:isar/isar.dart';

part 'isar_message_service.g.dart';

@Collection()
class IsarMessage {
  late int id;

  @Index()
  late String messageId;

  @Index()
  late String conversationId;

  @Index()
  late String userId;

  late String jsonData;

  @Index()
  late int createdAt;
}

class IsarMessageService {
  final Isar _isar;

  IsarMessageService(this._isar);

  Future<List<Message>> queryMessages({
    required String conversationId,
    required String userId,
    int limit = 20,
    int offset = 0,
  }) async {
    final messages = _isar.isarMessages
        .where()
        .conversationIdEqualTo(conversationId)
        .userIdEqualTo(userId)
        .sortByCreatedAtDesc()
        .findAll(offset: offset, limit: limit);

    LogUtils.d('Query messages: ${messages.map((e) => e.messageId)}',
        tag: 'IsarServiceImpl');

    return messages
        .map((e) => Message.fromJson(json.decode(e.jsonData)))
        .toList();
  }

  Future<Message?> getMessage(String id) async {
    final message = _isar.isarMessages.where().messageIdEqualTo(id).findFirst();
    return message != null
        ? Message.fromJson(json.decode(message.jsonData))
        : null;
  }

  Future<void> createMessage(
    Message message, {
    required String conversationId,
    required String userId,
  }) async {
    await _isar.writeAsync((isar) {
      return isar.isarMessages.put(IsarMessage()
        ..id = isar.isarMessages.autoIncrement()
        ..messageId = message.id
        ..conversationId = conversationId
        ..userId = userId
        ..jsonData = json.encode(message.toJson())
        ..createdAt =
            message.createdAt ?? DateTime.now().millisecondsSinceEpoch);
    });
  }

  Future<void> updateMessage(Message message) async {
    final existing =
        _isar.isarMessages.where().messageIdEqualTo(message.id).findFirst();

    if (existing == null) {
      LogUtils.e('Message not found', tag: 'IsarServiceImpl');
      return;
    }

    await _isar.writeAsync((isar) {
      return isar.isarMessages.put(
        existing
          ..jsonData = json.encode(
            message.toJson(),
          ),
      );
    });
  }

  Future<void> deleteMessage(String id) async {
    await _isar.writeAsync((isar) {
      return isar.isarMessages.where().messageIdEqualTo(id).deleteAll();
    });
  }

  Future<void> clearConversationMessages(
    String conversationId,
    String userId,
  ) async {
    await _isar.writeAsync((isar) {
      return isar.isarMessages
          .where()
          .conversationIdEqualTo(conversationId)
          .userIdEqualTo(userId)
          .deleteAll();
    });
  }

  /// Query the next expiring message across all conversations
  Future<ExpiredMessageInfo?> queryNextExpiringMessage() async {
    try {
      // Get all messages and filter for ephemeral ones that are read
      final allMessages = _isar.isarMessages.where().findAll();

      ExpiredMessageInfo? nextExpiring;
      DateTime? earliestExpiryTime;

      for (final isarMessage in allMessages) {
        try {
          final message = Message.fromJson(json.decode(isarMessage.jsonData));

          // Check if message is ephemeral and has been read
          if (message.isEphemeral && message.readAt != null) {
            final timeout = message.ephemeralTimeout ?? 1;
            final expiryTime = message.readAt!.add(Duration(seconds: timeout));

            // Find the earliest expiry time
            if (earliestExpiryTime == null ||
                expiryTime.isBefore(earliestExpiryTime)) {
              earliestExpiryTime = expiryTime;
              nextExpiring = ExpiredMessageInfo(
                messageId: message.id,
                conversationId: isarMessage.conversationId,
                expiryTime: expiryTime,
              );
            }
          }
        } catch (e) {
          LogUtils.e('Error parsing message ${isarMessage.messageId}: $e',
              tag: 'IsarMessageService');
        }
      }

      return nextExpiring;
    } catch (e) {
      LogUtils.e('Error querying next expiring message: $e',
          tag: 'IsarMessageService');
      return null;
    }
  }

  /// Query all expired messages across all conversations
  Future<List<ExpiredMessageInfo>> queryExpiredMessages() async {
    try {
      final allMessages = _isar.isarMessages.where().findAll();
      final expiredMessages = <ExpiredMessageInfo>[];

      for (final isarMessage in allMessages) {
        try {
          final message = Message.fromJson(json.decode(isarMessage.jsonData));

          // Check if message is expired
          if (message.isEphemeral &&
              message.readAt != null &&
              message.isExpired) {
            expiredMessages.add(ExpiredMessageInfo(
              messageId: message.id,
              conversationId: isarMessage.conversationId,
              expiryTime: message.readAt!
                  .add(Duration(seconds: message.ephemeralTimeout ?? 1)),
            ));
          }
        } catch (e) {
          LogUtils.e('Error parsing message ${isarMessage.messageId}: $e',
              tag: 'IsarMessageService');
        }
      }

      // Sort expired messages by expiry time (earliest first) to ensure FIFO deletion order
      expiredMessages.sort((a, b) => a.expiryTime.compareTo(b.expiryTime));

      LogUtils.d(
          'Found ${expiredMessages.length} expired messages (sorted by expiry time)',
          tag: 'IsarMessageService');
      return expiredMessages;
    } catch (e) {
      LogUtils.e('Error querying expired messages: $e',
          tag: 'IsarMessageService');
      return [];
    }
  }

  /// Batch delete expired messages by their IDs
  Future<void> batchDeleteExpiredMessages(List<String> messageIds) async {
    if (messageIds.isEmpty) return;

    try {
      await _isar.writeAsync((isar) {
        for (final messageId in messageIds) {
          isar.isarMessages.where().messageIdEqualTo(messageId).deleteAll();
        }
      });

      LogUtils.d('Batch deleted ${messageIds.length} expired messages',
          tag: 'IsarMessageService');
    } catch (e) {
      LogUtils.e('Error batch deleting expired messages: $e',
          tag: 'IsarMessageService');
      rethrow;
    }
  }
}

/// Information about an expired message
class ExpiredMessageInfo {
  final String messageId;
  final String conversationId;
  final DateTime expiryTime;

  const ExpiredMessageInfo({
    required this.messageId,
    required this.conversationId,
    required this.expiryTime,
  });

  @override
  String toString() {
    return 'ExpiredMessageInfo(messageId: $messageId, conversationId: $conversationId, expiryTime: $expiryTime)';
  }
}
