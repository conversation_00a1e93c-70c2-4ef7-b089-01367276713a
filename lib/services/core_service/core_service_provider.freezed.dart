// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'core_service_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CoreServiceState {
  AppConfigModel get config => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $CoreServiceStateCopyWith<CoreServiceState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CoreServiceStateCopyWith<$Res> {
  factory $CoreServiceStateCopyWith(
          CoreServiceState value, $Res Function(CoreServiceState) then) =
      _$CoreServiceStateCopyWithImpl<$Res, CoreServiceState>;
  @useResult
  $Res call({AppConfigModel config, bool isLoading, String? error});

  $AppConfigModelCopyWith<$Res> get config;
}

/// @nodoc
class _$CoreServiceStateCopyWithImpl<$Res, $Val extends CoreServiceState>
    implements $CoreServiceStateCopyWith<$Res> {
  _$CoreServiceStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? config = null,
    Object? isLoading = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      config: null == config
          ? _value.config
          : config // ignore: cast_nullable_to_non_nullable
              as AppConfigModel,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AppConfigModelCopyWith<$Res> get config {
    return $AppConfigModelCopyWith<$Res>(_value.config, (value) {
      return _then(_value.copyWith(config: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CoreServiceStateImplCopyWith<$Res>
    implements $CoreServiceStateCopyWith<$Res> {
  factory _$$CoreServiceStateImplCopyWith(_$CoreServiceStateImpl value,
          $Res Function(_$CoreServiceStateImpl) then) =
      __$$CoreServiceStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({AppConfigModel config, bool isLoading, String? error});

  @override
  $AppConfigModelCopyWith<$Res> get config;
}

/// @nodoc
class __$$CoreServiceStateImplCopyWithImpl<$Res>
    extends _$CoreServiceStateCopyWithImpl<$Res, _$CoreServiceStateImpl>
    implements _$$CoreServiceStateImplCopyWith<$Res> {
  __$$CoreServiceStateImplCopyWithImpl(_$CoreServiceStateImpl _value,
      $Res Function(_$CoreServiceStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? config = null,
    Object? isLoading = null,
    Object? error = freezed,
  }) {
    return _then(_$CoreServiceStateImpl(
      config: null == config
          ? _value.config
          : config // ignore: cast_nullable_to_non_nullable
              as AppConfigModel,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$CoreServiceStateImpl implements _CoreServiceState {
  const _$CoreServiceStateImpl(
      {required this.config, required this.isLoading, required this.error});

  @override
  final AppConfigModel config;
  @override
  final bool isLoading;
  @override
  final String? error;

  @override
  String toString() {
    return 'CoreServiceState(config: $config, isLoading: $isLoading, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CoreServiceStateImpl &&
            (identical(other.config, config) || other.config == config) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, config, isLoading, error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CoreServiceStateImplCopyWith<_$CoreServiceStateImpl> get copyWith =>
      __$$CoreServiceStateImplCopyWithImpl<_$CoreServiceStateImpl>(
          this, _$identity);
}

abstract class _CoreServiceState implements CoreServiceState {
  const factory _CoreServiceState(
      {required final AppConfigModel config,
      required final bool isLoading,
      required final String? error}) = _$CoreServiceStateImpl;

  @override
  AppConfigModel get config;
  @override
  bool get isLoading;
  @override
  String? get error;
  @override
  @JsonKey(ignore: true)
  _$$CoreServiceStateImplCopyWith<_$CoreServiceStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
