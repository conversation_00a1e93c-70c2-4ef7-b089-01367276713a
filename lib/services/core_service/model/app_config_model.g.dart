// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AppConfigModelImpl _$$AppConfigModelImplFromJson(Map<String, dynamic> json) =>
    _$AppConfigModelImpl(
      profileConfigResp: json['profileConfigResp'] == null
          ? AppConfigDefaults.profileDefaults
          : ProfileConfigResp.fromJson(
              json['profileConfigResp'] as Map<String, dynamic>),
      communicateConfigResp: json['communicateConfigResp'] == null
          ? AppConfigDefaults.communicateDefaults
          : CommunicateConfigResp.fromJson(
              json['communicateConfigResp'] as Map<String, dynamic>),
      rtcConfigResp: json['rtcConfigResp'] == null
          ? AppConfigDefaults.rtcDefaults
          : RtcConfigResp.fromJson(
              json['rtcConfigResp'] as Map<String, dynamic>),
      chatConfigResp: json['chatConfigResp'] == null
          ? AppConfigDefaults.chatDefaults
          : ChatConfigResp.fromJson(
              json['chatConfigResp'] as Map<String, dynamic>),
      fileConfigResp: json['fileConfigResp'] == null
          ? AppConfigDefaults.fileDefaults
          : FileConfigResp.fromJson(
              json['fileConfigResp'] as Map<String, dynamic>),
      registerConfigResp: json['registerConfigResp'] == null
          ? AppConfigDefaults.registerDefaults
          : RegisterConfigResp.fromJson(
              json['registerConfigResp'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$AppConfigModelImplToJson(
        _$AppConfigModelImpl instance) =>
    <String, dynamic>{
      'profileConfigResp': instance.profileConfigResp.toJson(),
      'communicateConfigResp': instance.communicateConfigResp.toJson(),
      'rtcConfigResp': instance.rtcConfigResp.toJson(),
      'chatConfigResp': instance.chatConfigResp.toJson(),
      'fileConfigResp': instance.fileConfigResp.toJson(),
      'registerConfigResp': instance.registerConfigResp.toJson(),
    };

_$RegisterConfigRespImpl _$$RegisterConfigRespImplFromJson(
        Map<String, dynamic> json) =>
    _$RegisterConfigRespImpl(
      excludeCountries: (json['excludeCountries'] as List<dynamic>?)
              ?.map((e) => CountryModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          AppConfigDefaults.excludeCountries,
    );

Map<String, dynamic> _$$RegisterConfigRespImplToJson(
        _$RegisterConfigRespImpl instance) =>
    <String, dynamic>{
      'excludeCountries':
          instance.excludeCountries.map((e) => e.toJson()).toList(),
    };

_$ProfileConfigRespImpl _$$ProfileConfigRespImplFromJson(
        Map<String, dynamic> json) =>
    _$ProfileConfigRespImpl(
      nicknameUpdateLimitDay:
          (json['nicknameUpdateLimitDay'] as num?)?.toInt() ??
              AppConfigDefaults.nicknameUpdateLimitDay,
      blackListMaxCount: (json['blackListMaxCount'] as num?)?.toInt() ??
          AppConfigDefaults.blackListMaxCount,
      deleteAccountCooldownDay:
          (json['deleteAccountCooldownDay'] as num?)?.toInt() ??
              AppConfigDefaults.deleteAccountCooldownDay,
    );

Map<String, dynamic> _$$ProfileConfigRespImplToJson(
        _$ProfileConfigRespImpl instance) =>
    <String, dynamic>{
      'nicknameUpdateLimitDay': instance.nicknameUpdateLimitDay,
      'blackListMaxCount': instance.blackListMaxCount,
      'deleteAccountCooldownDay': instance.deleteAccountCooldownDay,
    };

_$CommunicateConfigRespImpl _$$CommunicateConfigRespImplFromJson(
        Map<String, dynamic> json) =>
    _$CommunicateConfigRespImpl(
      roomConfigResp: json['roomConfigResp'] == null
          ? AppConfigDefaults.roomDefaults
          : RoomConfigResp.fromJson(
              json['roomConfigResp'] as Map<String, dynamic>),
      giftConfigResp: json['giftConfigResp'] == null
          ? AppConfigDefaults.giftDefaults
          : GiftConfigResp.fromJson(
              json['giftConfigResp'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CommunicateConfigRespImplToJson(
        _$CommunicateConfigRespImpl instance) =>
    <String, dynamic>{
      'roomConfigResp': instance.roomConfigResp,
      'giftConfigResp': instance.giftConfigResp,
    };

_$RoomConfigRespImpl _$$RoomConfigRespImplFromJson(Map<String, dynamic> json) =>
    _$RoomConfigRespImpl(
      createRoomDayLimit: (json['createRoomDayLimit'] as num?)?.toInt() ??
          AppConfigDefaults.createRoomDayLimit,
      roomTTLInHour: (json['roomTTLInHour'] as num?)?.toInt() ??
          AppConfigDefaults.roomTTLInHour,
      maxUserInRoom: (json['maxUserInRoom'] as num?)?.toInt() ??
          AppConfigDefaults.maxUserInRoom,
      roomBoostActivities: (json['roomBoostActivities'] as num?)?.toInt() ??
          AppConfigDefaults.roomBoostActivities,
      roomTitleMaxLength: (json['roomTitleMaxLength'] as num?)?.toInt() ??
          AppConfigDefaults.roomTitleMaxLength,
      roomAnnouncementMaxLength:
          (json['roomAnnouncementMaxLength'] as num?)?.toInt() ??
              AppConfigDefaults.roomAnnouncementMaxLength,
    );

Map<String, dynamic> _$$RoomConfigRespImplToJson(
        _$RoomConfigRespImpl instance) =>
    <String, dynamic>{
      'createRoomDayLimit': instance.createRoomDayLimit,
      'roomTTLInHour': instance.roomTTLInHour,
      'maxUserInRoom': instance.maxUserInRoom,
      'roomBoostActivities': instance.roomBoostActivities,
      'roomTitleMaxLength': instance.roomTitleMaxLength,
      'roomAnnouncementMaxLength': instance.roomAnnouncementMaxLength,
    };

_$GiftConfigRespImpl _$$GiftConfigRespImplFromJson(Map<String, dynamic> json) =>
    _$GiftConfigRespImpl(
      unactivatedGiftExpireDays:
          (json['unactivatedGiftExpireDays'] as num?)?.toInt() ??
              AppConfigDefaults.unactivatedGiftExpireDays,
    );

Map<String, dynamic> _$$GiftConfigRespImplToJson(
        _$GiftConfigRespImpl instance) =>
    <String, dynamic>{
      'unactivatedGiftExpireDays': instance.unactivatedGiftExpireDays,
    };

_$RtcConfigRespImpl _$$RtcConfigRespImplFromJson(Map<String, dynamic> json) =>
    _$RtcConfigRespImpl(
      instantChatConfigResp: json['instantChatConfigResp'] == null
          ? AppConfigDefaults.instantChatDefaults
          : InstantChatConfigResp.fromJson(
              json['instantChatConfigResp'] as Map<String, dynamic>),
      instantVoiceCallConfigResp: json['instantVoiceCallConfigResp'] == null
          ? AppConfigDefaults.instantVoiceCallDefaults
          : InstantVoiceCallConfigResp.fromJson(
              json['instantVoiceCallConfigResp'] as Map<String, dynamic>),
      voiceCallConfigResp: json['voiceCallConfigResp'] == null
          ? AppConfigDefaults.voiceCallDefaults
          : VoiceCallConfigResp.fromJson(
              json['voiceCallConfigResp'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$RtcConfigRespImplToJson(_$RtcConfigRespImpl instance) =>
    <String, dynamic>{
      'instantChatConfigResp': instance.instantChatConfigResp,
      'instantVoiceCallConfigResp': instance.instantVoiceCallConfigResp,
      'voiceCallConfigResp': instance.voiceCallConfigResp,
    };

_$VoiceCallConfigRespImpl _$$VoiceCallConfigRespImplFromJson(
        Map<String, dynamic> json) =>
    _$VoiceCallConfigRespImpl(
      pendingTimeoutSeconds: (json['pendingTimeoutSeconds'] as num?)?.toInt() ??
          AppConfigDefaults.pendingTimeoutSeconds,
    );

Map<String, dynamic> _$$VoiceCallConfigRespImplToJson(
        _$VoiceCallConfigRespImpl instance) =>
    <String, dynamic>{
      'pendingTimeoutSeconds': instance.pendingTimeoutSeconds,
    };

_$InstantChatConfigRespImpl _$$InstantChatConfigRespImplFromJson(
        Map<String, dynamic> json) =>
    _$InstantChatConfigRespImpl(
      pendingTimeoutSeconds: (json['pendingTimeoutSeconds'] as num?)?.toInt() ??
          AppConfigDefaults.pendingTimeoutSeconds,
      maleDailyMatchCount: (json['maleDailyMatchCount'] as num?)?.toInt() ??
          AppConfigDefaults.maleDailyMatchCount,
      femaleDailyMatchCount: (json['femaleDailyMatchCount'] as num?)?.toInt() ??
          AppConfigDefaults.femaleDailyMatchCount,
      gemCost: (json['gemCost'] as num?)?.toInt() ?? AppConfigDefaults.gemCost,
    );

Map<String, dynamic> _$$InstantChatConfigRespImplToJson(
        _$InstantChatConfigRespImpl instance) =>
    <String, dynamic>{
      'pendingTimeoutSeconds': instance.pendingTimeoutSeconds,
      'maleDailyMatchCount': instance.maleDailyMatchCount,
      'femaleDailyMatchCount': instance.femaleDailyMatchCount,
      'gemCost': instance.gemCost,
    };

_$InstantVoiceCallConfigRespImpl _$$InstantVoiceCallConfigRespImplFromJson(
        Map<String, dynamic> json) =>
    _$InstantVoiceCallConfigRespImpl(
      pendingTimeoutSeconds: (json['pendingTimeoutSeconds'] as num?)?.toInt() ??
          AppConfigDefaults.pendingTimeoutSeconds,
      maleDailyMatchCount: (json['maleDailyMatchCount'] as num?)?.toInt() ??
          AppConfigDefaults.maleDailyMatchCount,
      femaleDailyMatchCount: (json['femaleDailyMatchCount'] as num?)?.toInt() ??
          AppConfigDefaults.femaleDailyMatchCount,
      gemCost: (json['gemCost'] as num?)?.toInt() ?? AppConfigDefaults.gemCost,
    );

Map<String, dynamic> _$$InstantVoiceCallConfigRespImplToJson(
        _$InstantVoiceCallConfigRespImpl instance) =>
    <String, dynamic>{
      'pendingTimeoutSeconds': instance.pendingTimeoutSeconds,
      'maleDailyMatchCount': instance.maleDailyMatchCount,
      'femaleDailyMatchCount': instance.femaleDailyMatchCount,
      'gemCost': instance.gemCost,
    };

_$ChatConfigRespImpl _$$ChatConfigRespImplFromJson(Map<String, dynamic> json) =>
    _$ChatConfigRespImpl(
      maxContentLength: (json['maxContentLength'] as num?)?.toInt() ??
          AppConfigDefaults.maxContentLength,
      msgTimeoutDay: (json['msgTimeoutDay'] as num?)?.toInt() ??
          AppConfigDefaults.msgTimeoutDay,
      dailyReceiverLimit: (json['dailyReceiverLimit'] as num?)?.toInt() ??
          AppConfigDefaults.dailyReceiverLimit,
      msgClientTimeoutSeconds:
          (json['msgClientTimeoutSeconds'] as num?)?.toInt() ??
              AppConfigDefaults.msgClientTimeoutSeconds,
    );

Map<String, dynamic> _$$ChatConfigRespImplToJson(
        _$ChatConfigRespImpl instance) =>
    <String, dynamic>{
      'maxContentLength': instance.maxContentLength,
      'msgTimeoutDay': instance.msgTimeoutDay,
      'dailyReceiverLimit': instance.dailyReceiverLimit,
      'msgClientTimeoutSeconds': instance.msgClientTimeoutSeconds,
    };

_$FileConfigRespImpl _$$FileConfigRespImplFromJson(Map<String, dynamic> json) =>
    _$FileConfigRespImpl(
      fileSizeLimit: (json['fileSizeLimit'] as num?)?.toInt() ??
          AppConfigDefaults.fileSizeLimit,
      imageSizeLimit: (json['imageSizeLimit'] as num?)?.toInt() ??
          AppConfigDefaults.imageSizeLimit,
      videoSizeLimit: (json['videoSizeLimit'] as num?)?.toInt() ??
          AppConfigDefaults.videoSizeLimit,
    );

Map<String, dynamic> _$$FileConfigRespImplToJson(
        _$FileConfigRespImpl instance) =>
    <String, dynamic>{
      'fileSizeLimit': instance.fileSizeLimit,
      'imageSizeLimit': instance.imageSizeLimit,
      'videoSizeLimit': instance.videoSizeLimit,
    };
