// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_config_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AppConfigModel _$AppConfigModelFromJson(Map<String, dynamic> json) {
  return _AppConfigModel.fromJson(json);
}

/// @nodoc
mixin _$AppConfigModel {
  ProfileConfigResp get profileConfigResp => throw _privateConstructorUsedError;
  CommunicateConfigResp get communicateConfigResp =>
      throw _privateConstructorUsedError;
  RtcConfigResp get rtcConfigResp => throw _privateConstructorUsedError;
  ChatConfigResp get chatConfigResp => throw _privateConstructorUsedError;
  FileConfigResp get fileConfigResp => throw _privateConstructorUsedError;
  RegisterConfigResp get registerConfigResp =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AppConfigModelCopyWith<AppConfigModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppConfigModelCopyWith<$Res> {
  factory $AppConfigModelCopyWith(
          AppConfigModel value, $Res Function(AppConfigModel) then) =
      _$AppConfigModelCopyWithImpl<$Res, AppConfigModel>;
  @useResult
  $Res call(
      {ProfileConfigResp profileConfigResp,
      CommunicateConfigResp communicateConfigResp,
      RtcConfigResp rtcConfigResp,
      ChatConfigResp chatConfigResp,
      FileConfigResp fileConfigResp,
      RegisterConfigResp registerConfigResp});

  $ProfileConfigRespCopyWith<$Res> get profileConfigResp;
  $CommunicateConfigRespCopyWith<$Res> get communicateConfigResp;
  $RtcConfigRespCopyWith<$Res> get rtcConfigResp;
  $ChatConfigRespCopyWith<$Res> get chatConfigResp;
  $FileConfigRespCopyWith<$Res> get fileConfigResp;
  $RegisterConfigRespCopyWith<$Res> get registerConfigResp;
}

/// @nodoc
class _$AppConfigModelCopyWithImpl<$Res, $Val extends AppConfigModel>
    implements $AppConfigModelCopyWith<$Res> {
  _$AppConfigModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profileConfigResp = null,
    Object? communicateConfigResp = null,
    Object? rtcConfigResp = null,
    Object? chatConfigResp = null,
    Object? fileConfigResp = null,
    Object? registerConfigResp = null,
  }) {
    return _then(_value.copyWith(
      profileConfigResp: null == profileConfigResp
          ? _value.profileConfigResp
          : profileConfigResp // ignore: cast_nullable_to_non_nullable
              as ProfileConfigResp,
      communicateConfigResp: null == communicateConfigResp
          ? _value.communicateConfigResp
          : communicateConfigResp // ignore: cast_nullable_to_non_nullable
              as CommunicateConfigResp,
      rtcConfigResp: null == rtcConfigResp
          ? _value.rtcConfigResp
          : rtcConfigResp // ignore: cast_nullable_to_non_nullable
              as RtcConfigResp,
      chatConfigResp: null == chatConfigResp
          ? _value.chatConfigResp
          : chatConfigResp // ignore: cast_nullable_to_non_nullable
              as ChatConfigResp,
      fileConfigResp: null == fileConfigResp
          ? _value.fileConfigResp
          : fileConfigResp // ignore: cast_nullable_to_non_nullable
              as FileConfigResp,
      registerConfigResp: null == registerConfigResp
          ? _value.registerConfigResp
          : registerConfigResp // ignore: cast_nullable_to_non_nullable
              as RegisterConfigResp,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ProfileConfigRespCopyWith<$Res> get profileConfigResp {
    return $ProfileConfigRespCopyWith<$Res>(_value.profileConfigResp, (value) {
      return _then(_value.copyWith(profileConfigResp: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CommunicateConfigRespCopyWith<$Res> get communicateConfigResp {
    return $CommunicateConfigRespCopyWith<$Res>(_value.communicateConfigResp,
        (value) {
      return _then(_value.copyWith(communicateConfigResp: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $RtcConfigRespCopyWith<$Res> get rtcConfigResp {
    return $RtcConfigRespCopyWith<$Res>(_value.rtcConfigResp, (value) {
      return _then(_value.copyWith(rtcConfigResp: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ChatConfigRespCopyWith<$Res> get chatConfigResp {
    return $ChatConfigRespCopyWith<$Res>(_value.chatConfigResp, (value) {
      return _then(_value.copyWith(chatConfigResp: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $FileConfigRespCopyWith<$Res> get fileConfigResp {
    return $FileConfigRespCopyWith<$Res>(_value.fileConfigResp, (value) {
      return _then(_value.copyWith(fileConfigResp: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $RegisterConfigRespCopyWith<$Res> get registerConfigResp {
    return $RegisterConfigRespCopyWith<$Res>(_value.registerConfigResp,
        (value) {
      return _then(_value.copyWith(registerConfigResp: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AppConfigModelImplCopyWith<$Res>
    implements $AppConfigModelCopyWith<$Res> {
  factory _$$AppConfigModelImplCopyWith(_$AppConfigModelImpl value,
          $Res Function(_$AppConfigModelImpl) then) =
      __$$AppConfigModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {ProfileConfigResp profileConfigResp,
      CommunicateConfigResp communicateConfigResp,
      RtcConfigResp rtcConfigResp,
      ChatConfigResp chatConfigResp,
      FileConfigResp fileConfigResp,
      RegisterConfigResp registerConfigResp});

  @override
  $ProfileConfigRespCopyWith<$Res> get profileConfigResp;
  @override
  $CommunicateConfigRespCopyWith<$Res> get communicateConfigResp;
  @override
  $RtcConfigRespCopyWith<$Res> get rtcConfigResp;
  @override
  $ChatConfigRespCopyWith<$Res> get chatConfigResp;
  @override
  $FileConfigRespCopyWith<$Res> get fileConfigResp;
  @override
  $RegisterConfigRespCopyWith<$Res> get registerConfigResp;
}

/// @nodoc
class __$$AppConfigModelImplCopyWithImpl<$Res>
    extends _$AppConfigModelCopyWithImpl<$Res, _$AppConfigModelImpl>
    implements _$$AppConfigModelImplCopyWith<$Res> {
  __$$AppConfigModelImplCopyWithImpl(
      _$AppConfigModelImpl _value, $Res Function(_$AppConfigModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profileConfigResp = null,
    Object? communicateConfigResp = null,
    Object? rtcConfigResp = null,
    Object? chatConfigResp = null,
    Object? fileConfigResp = null,
    Object? registerConfigResp = null,
  }) {
    return _then(_$AppConfigModelImpl(
      profileConfigResp: null == profileConfigResp
          ? _value.profileConfigResp
          : profileConfigResp // ignore: cast_nullable_to_non_nullable
              as ProfileConfigResp,
      communicateConfigResp: null == communicateConfigResp
          ? _value.communicateConfigResp
          : communicateConfigResp // ignore: cast_nullable_to_non_nullable
              as CommunicateConfigResp,
      rtcConfigResp: null == rtcConfigResp
          ? _value.rtcConfigResp
          : rtcConfigResp // ignore: cast_nullable_to_non_nullable
              as RtcConfigResp,
      chatConfigResp: null == chatConfigResp
          ? _value.chatConfigResp
          : chatConfigResp // ignore: cast_nullable_to_non_nullable
              as ChatConfigResp,
      fileConfigResp: null == fileConfigResp
          ? _value.fileConfigResp
          : fileConfigResp // ignore: cast_nullable_to_non_nullable
              as FileConfigResp,
      registerConfigResp: null == registerConfigResp
          ? _value.registerConfigResp
          : registerConfigResp // ignore: cast_nullable_to_non_nullable
              as RegisterConfigResp,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$AppConfigModelImpl extends _AppConfigModel {
  const _$AppConfigModelImpl(
      {this.profileConfigResp = AppConfigDefaults.profileDefaults,
      this.communicateConfigResp = AppConfigDefaults.communicateDefaults,
      this.rtcConfigResp = AppConfigDefaults.rtcDefaults,
      this.chatConfigResp = AppConfigDefaults.chatDefaults,
      this.fileConfigResp = AppConfigDefaults.fileDefaults,
      this.registerConfigResp = AppConfigDefaults.registerDefaults})
      : super._();

  factory _$AppConfigModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppConfigModelImplFromJson(json);

  @override
  @JsonKey()
  final ProfileConfigResp profileConfigResp;
  @override
  @JsonKey()
  final CommunicateConfigResp communicateConfigResp;
  @override
  @JsonKey()
  final RtcConfigResp rtcConfigResp;
  @override
  @JsonKey()
  final ChatConfigResp chatConfigResp;
  @override
  @JsonKey()
  final FileConfigResp fileConfigResp;
  @override
  @JsonKey()
  final RegisterConfigResp registerConfigResp;

  @override
  String toString() {
    return 'AppConfigModel(profileConfigResp: $profileConfigResp, communicateConfigResp: $communicateConfigResp, rtcConfigResp: $rtcConfigResp, chatConfigResp: $chatConfigResp, fileConfigResp: $fileConfigResp, registerConfigResp: $registerConfigResp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppConfigModelImpl &&
            (identical(other.profileConfigResp, profileConfigResp) ||
                other.profileConfigResp == profileConfigResp) &&
            (identical(other.communicateConfigResp, communicateConfigResp) ||
                other.communicateConfigResp == communicateConfigResp) &&
            (identical(other.rtcConfigResp, rtcConfigResp) ||
                other.rtcConfigResp == rtcConfigResp) &&
            (identical(other.chatConfigResp, chatConfigResp) ||
                other.chatConfigResp == chatConfigResp) &&
            (identical(other.fileConfigResp, fileConfigResp) ||
                other.fileConfigResp == fileConfigResp) &&
            (identical(other.registerConfigResp, registerConfigResp) ||
                other.registerConfigResp == registerConfigResp));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      profileConfigResp,
      communicateConfigResp,
      rtcConfigResp,
      chatConfigResp,
      fileConfigResp,
      registerConfigResp);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AppConfigModelImplCopyWith<_$AppConfigModelImpl> get copyWith =>
      __$$AppConfigModelImplCopyWithImpl<_$AppConfigModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppConfigModelImplToJson(
      this,
    );
  }
}

abstract class _AppConfigModel extends AppConfigModel {
  const factory _AppConfigModel(
      {final ProfileConfigResp profileConfigResp,
      final CommunicateConfigResp communicateConfigResp,
      final RtcConfigResp rtcConfigResp,
      final ChatConfigResp chatConfigResp,
      final FileConfigResp fileConfigResp,
      final RegisterConfigResp registerConfigResp}) = _$AppConfigModelImpl;
  const _AppConfigModel._() : super._();

  factory _AppConfigModel.fromJson(Map<String, dynamic> json) =
      _$AppConfigModelImpl.fromJson;

  @override
  ProfileConfigResp get profileConfigResp;
  @override
  CommunicateConfigResp get communicateConfigResp;
  @override
  RtcConfigResp get rtcConfigResp;
  @override
  ChatConfigResp get chatConfigResp;
  @override
  FileConfigResp get fileConfigResp;
  @override
  RegisterConfigResp get registerConfigResp;
  @override
  @JsonKey(ignore: true)
  _$$AppConfigModelImplCopyWith<_$AppConfigModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RegisterConfigResp _$RegisterConfigRespFromJson(Map<String, dynamic> json) {
  return _RegisterConfigResp.fromJson(json);
}

/// @nodoc
mixin _$RegisterConfigResp {
  List<CountryModel> get excludeCountries => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RegisterConfigRespCopyWith<RegisterConfigResp> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RegisterConfigRespCopyWith<$Res> {
  factory $RegisterConfigRespCopyWith(
          RegisterConfigResp value, $Res Function(RegisterConfigResp) then) =
      _$RegisterConfigRespCopyWithImpl<$Res, RegisterConfigResp>;
  @useResult
  $Res call({List<CountryModel> excludeCountries});
}

/// @nodoc
class _$RegisterConfigRespCopyWithImpl<$Res, $Val extends RegisterConfigResp>
    implements $RegisterConfigRespCopyWith<$Res> {
  _$RegisterConfigRespCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? excludeCountries = null,
  }) {
    return _then(_value.copyWith(
      excludeCountries: null == excludeCountries
          ? _value.excludeCountries
          : excludeCountries // ignore: cast_nullable_to_non_nullable
              as List<CountryModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RegisterConfigRespImplCopyWith<$Res>
    implements $RegisterConfigRespCopyWith<$Res> {
  factory _$$RegisterConfigRespImplCopyWith(_$RegisterConfigRespImpl value,
          $Res Function(_$RegisterConfigRespImpl) then) =
      __$$RegisterConfigRespImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<CountryModel> excludeCountries});
}

/// @nodoc
class __$$RegisterConfigRespImplCopyWithImpl<$Res>
    extends _$RegisterConfigRespCopyWithImpl<$Res, _$RegisterConfigRespImpl>
    implements _$$RegisterConfigRespImplCopyWith<$Res> {
  __$$RegisterConfigRespImplCopyWithImpl(_$RegisterConfigRespImpl _value,
      $Res Function(_$RegisterConfigRespImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? excludeCountries = null,
  }) {
    return _then(_$RegisterConfigRespImpl(
      excludeCountries: null == excludeCountries
          ? _value._excludeCountries
          : excludeCountries // ignore: cast_nullable_to_non_nullable
              as List<CountryModel>,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$RegisterConfigRespImpl implements _RegisterConfigResp {
  const _$RegisterConfigRespImpl(
      {final List<CountryModel> excludeCountries =
          AppConfigDefaults.excludeCountries})
      : _excludeCountries = excludeCountries;

  factory _$RegisterConfigRespImpl.fromJson(Map<String, dynamic> json) =>
      _$$RegisterConfigRespImplFromJson(json);

  final List<CountryModel> _excludeCountries;
  @override
  @JsonKey()
  List<CountryModel> get excludeCountries {
    if (_excludeCountries is EqualUnmodifiableListView)
      return _excludeCountries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_excludeCountries);
  }

  @override
  String toString() {
    return 'RegisterConfigResp(excludeCountries: $excludeCountries)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegisterConfigRespImpl &&
            const DeepCollectionEquality()
                .equals(other._excludeCountries, _excludeCountries));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_excludeCountries));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RegisterConfigRespImplCopyWith<_$RegisterConfigRespImpl> get copyWith =>
      __$$RegisterConfigRespImplCopyWithImpl<_$RegisterConfigRespImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RegisterConfigRespImplToJson(
      this,
    );
  }
}

abstract class _RegisterConfigResp implements RegisterConfigResp {
  const factory _RegisterConfigResp(
      {final List<CountryModel> excludeCountries}) = _$RegisterConfigRespImpl;

  factory _RegisterConfigResp.fromJson(Map<String, dynamic> json) =
      _$RegisterConfigRespImpl.fromJson;

  @override
  List<CountryModel> get excludeCountries;
  @override
  @JsonKey(ignore: true)
  _$$RegisterConfigRespImplCopyWith<_$RegisterConfigRespImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProfileConfigResp _$ProfileConfigRespFromJson(Map<String, dynamic> json) {
  return _ProfileConfigResp.fromJson(json);
}

/// @nodoc
mixin _$ProfileConfigResp {
  int get nicknameUpdateLimitDay => throw _privateConstructorUsedError;
  int get blackListMaxCount => throw _privateConstructorUsedError;
  int get deleteAccountCooldownDay => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ProfileConfigRespCopyWith<ProfileConfigResp> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileConfigRespCopyWith<$Res> {
  factory $ProfileConfigRespCopyWith(
          ProfileConfigResp value, $Res Function(ProfileConfigResp) then) =
      _$ProfileConfigRespCopyWithImpl<$Res, ProfileConfigResp>;
  @useResult
  $Res call(
      {int nicknameUpdateLimitDay,
      int blackListMaxCount,
      int deleteAccountCooldownDay});
}

/// @nodoc
class _$ProfileConfigRespCopyWithImpl<$Res, $Val extends ProfileConfigResp>
    implements $ProfileConfigRespCopyWith<$Res> {
  _$ProfileConfigRespCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nicknameUpdateLimitDay = null,
    Object? blackListMaxCount = null,
    Object? deleteAccountCooldownDay = null,
  }) {
    return _then(_value.copyWith(
      nicknameUpdateLimitDay: null == nicknameUpdateLimitDay
          ? _value.nicknameUpdateLimitDay
          : nicknameUpdateLimitDay // ignore: cast_nullable_to_non_nullable
              as int,
      blackListMaxCount: null == blackListMaxCount
          ? _value.blackListMaxCount
          : blackListMaxCount // ignore: cast_nullable_to_non_nullable
              as int,
      deleteAccountCooldownDay: null == deleteAccountCooldownDay
          ? _value.deleteAccountCooldownDay
          : deleteAccountCooldownDay // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProfileConfigRespImplCopyWith<$Res>
    implements $ProfileConfigRespCopyWith<$Res> {
  factory _$$ProfileConfigRespImplCopyWith(_$ProfileConfigRespImpl value,
          $Res Function(_$ProfileConfigRespImpl) then) =
      __$$ProfileConfigRespImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int nicknameUpdateLimitDay,
      int blackListMaxCount,
      int deleteAccountCooldownDay});
}

/// @nodoc
class __$$ProfileConfigRespImplCopyWithImpl<$Res>
    extends _$ProfileConfigRespCopyWithImpl<$Res, _$ProfileConfigRespImpl>
    implements _$$ProfileConfigRespImplCopyWith<$Res> {
  __$$ProfileConfigRespImplCopyWithImpl(_$ProfileConfigRespImpl _value,
      $Res Function(_$ProfileConfigRespImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nicknameUpdateLimitDay = null,
    Object? blackListMaxCount = null,
    Object? deleteAccountCooldownDay = null,
  }) {
    return _then(_$ProfileConfigRespImpl(
      nicknameUpdateLimitDay: null == nicknameUpdateLimitDay
          ? _value.nicknameUpdateLimitDay
          : nicknameUpdateLimitDay // ignore: cast_nullable_to_non_nullable
              as int,
      blackListMaxCount: null == blackListMaxCount
          ? _value.blackListMaxCount
          : blackListMaxCount // ignore: cast_nullable_to_non_nullable
              as int,
      deleteAccountCooldownDay: null == deleteAccountCooldownDay
          ? _value.deleteAccountCooldownDay
          : deleteAccountCooldownDay // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfileConfigRespImpl implements _ProfileConfigResp {
  const _$ProfileConfigRespImpl(
      {this.nicknameUpdateLimitDay = AppConfigDefaults.nicknameUpdateLimitDay,
      this.blackListMaxCount = AppConfigDefaults.blackListMaxCount,
      this.deleteAccountCooldownDay =
          AppConfigDefaults.deleteAccountCooldownDay});

  factory _$ProfileConfigRespImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfileConfigRespImplFromJson(json);

  @override
  @JsonKey()
  final int nicknameUpdateLimitDay;
  @override
  @JsonKey()
  final int blackListMaxCount;
  @override
  @JsonKey()
  final int deleteAccountCooldownDay;

  @override
  String toString() {
    return 'ProfileConfigResp(nicknameUpdateLimitDay: $nicknameUpdateLimitDay, blackListMaxCount: $blackListMaxCount, deleteAccountCooldownDay: $deleteAccountCooldownDay)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileConfigRespImpl &&
            (identical(other.nicknameUpdateLimitDay, nicknameUpdateLimitDay) ||
                other.nicknameUpdateLimitDay == nicknameUpdateLimitDay) &&
            (identical(other.blackListMaxCount, blackListMaxCount) ||
                other.blackListMaxCount == blackListMaxCount) &&
            (identical(
                    other.deleteAccountCooldownDay, deleteAccountCooldownDay) ||
                other.deleteAccountCooldownDay == deleteAccountCooldownDay));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, nicknameUpdateLimitDay,
      blackListMaxCount, deleteAccountCooldownDay);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileConfigRespImplCopyWith<_$ProfileConfigRespImpl> get copyWith =>
      __$$ProfileConfigRespImplCopyWithImpl<_$ProfileConfigRespImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfileConfigRespImplToJson(
      this,
    );
  }
}

abstract class _ProfileConfigResp implements ProfileConfigResp {
  const factory _ProfileConfigResp(
      {final int nicknameUpdateLimitDay,
      final int blackListMaxCount,
      final int deleteAccountCooldownDay}) = _$ProfileConfigRespImpl;

  factory _ProfileConfigResp.fromJson(Map<String, dynamic> json) =
      _$ProfileConfigRespImpl.fromJson;

  @override
  int get nicknameUpdateLimitDay;
  @override
  int get blackListMaxCount;
  @override
  int get deleteAccountCooldownDay;
  @override
  @JsonKey(ignore: true)
  _$$ProfileConfigRespImplCopyWith<_$ProfileConfigRespImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CommunicateConfigResp _$CommunicateConfigRespFromJson(
    Map<String, dynamic> json) {
  return _CommunicateConfigResp.fromJson(json);
}

/// @nodoc
mixin _$CommunicateConfigResp {
  RoomConfigResp get roomConfigResp => throw _privateConstructorUsedError;
  GiftConfigResp get giftConfigResp => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CommunicateConfigRespCopyWith<CommunicateConfigResp> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CommunicateConfigRespCopyWith<$Res> {
  factory $CommunicateConfigRespCopyWith(CommunicateConfigResp value,
          $Res Function(CommunicateConfigResp) then) =
      _$CommunicateConfigRespCopyWithImpl<$Res, CommunicateConfigResp>;
  @useResult
  $Res call({RoomConfigResp roomConfigResp, GiftConfigResp giftConfigResp});

  $RoomConfigRespCopyWith<$Res> get roomConfigResp;
  $GiftConfigRespCopyWith<$Res> get giftConfigResp;
}

/// @nodoc
class _$CommunicateConfigRespCopyWithImpl<$Res,
        $Val extends CommunicateConfigResp>
    implements $CommunicateConfigRespCopyWith<$Res> {
  _$CommunicateConfigRespCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? roomConfigResp = null,
    Object? giftConfigResp = null,
  }) {
    return _then(_value.copyWith(
      roomConfigResp: null == roomConfigResp
          ? _value.roomConfigResp
          : roomConfigResp // ignore: cast_nullable_to_non_nullable
              as RoomConfigResp,
      giftConfigResp: null == giftConfigResp
          ? _value.giftConfigResp
          : giftConfigResp // ignore: cast_nullable_to_non_nullable
              as GiftConfigResp,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $RoomConfigRespCopyWith<$Res> get roomConfigResp {
    return $RoomConfigRespCopyWith<$Res>(_value.roomConfigResp, (value) {
      return _then(_value.copyWith(roomConfigResp: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $GiftConfigRespCopyWith<$Res> get giftConfigResp {
    return $GiftConfigRespCopyWith<$Res>(_value.giftConfigResp, (value) {
      return _then(_value.copyWith(giftConfigResp: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CommunicateConfigRespImplCopyWith<$Res>
    implements $CommunicateConfigRespCopyWith<$Res> {
  factory _$$CommunicateConfigRespImplCopyWith(
          _$CommunicateConfigRespImpl value,
          $Res Function(_$CommunicateConfigRespImpl) then) =
      __$$CommunicateConfigRespImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({RoomConfigResp roomConfigResp, GiftConfigResp giftConfigResp});

  @override
  $RoomConfigRespCopyWith<$Res> get roomConfigResp;
  @override
  $GiftConfigRespCopyWith<$Res> get giftConfigResp;
}

/// @nodoc
class __$$CommunicateConfigRespImplCopyWithImpl<$Res>
    extends _$CommunicateConfigRespCopyWithImpl<$Res,
        _$CommunicateConfigRespImpl>
    implements _$$CommunicateConfigRespImplCopyWith<$Res> {
  __$$CommunicateConfigRespImplCopyWithImpl(_$CommunicateConfigRespImpl _value,
      $Res Function(_$CommunicateConfigRespImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? roomConfigResp = null,
    Object? giftConfigResp = null,
  }) {
    return _then(_$CommunicateConfigRespImpl(
      roomConfigResp: null == roomConfigResp
          ? _value.roomConfigResp
          : roomConfigResp // ignore: cast_nullable_to_non_nullable
              as RoomConfigResp,
      giftConfigResp: null == giftConfigResp
          ? _value.giftConfigResp
          : giftConfigResp // ignore: cast_nullable_to_non_nullable
              as GiftConfigResp,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CommunicateConfigRespImpl implements _CommunicateConfigResp {
  const _$CommunicateConfigRespImpl(
      {this.roomConfigResp = AppConfigDefaults.roomDefaults,
      this.giftConfigResp = AppConfigDefaults.giftDefaults});

  factory _$CommunicateConfigRespImpl.fromJson(Map<String, dynamic> json) =>
      _$$CommunicateConfigRespImplFromJson(json);

  @override
  @JsonKey()
  final RoomConfigResp roomConfigResp;
  @override
  @JsonKey()
  final GiftConfigResp giftConfigResp;

  @override
  String toString() {
    return 'CommunicateConfigResp(roomConfigResp: $roomConfigResp, giftConfigResp: $giftConfigResp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CommunicateConfigRespImpl &&
            (identical(other.roomConfigResp, roomConfigResp) ||
                other.roomConfigResp == roomConfigResp) &&
            (identical(other.giftConfigResp, giftConfigResp) ||
                other.giftConfigResp == giftConfigResp));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, roomConfigResp, giftConfigResp);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CommunicateConfigRespImplCopyWith<_$CommunicateConfigRespImpl>
      get copyWith => __$$CommunicateConfigRespImplCopyWithImpl<
          _$CommunicateConfigRespImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CommunicateConfigRespImplToJson(
      this,
    );
  }
}

abstract class _CommunicateConfigResp implements CommunicateConfigResp {
  const factory _CommunicateConfigResp(
      {final RoomConfigResp roomConfigResp,
      final GiftConfigResp giftConfigResp}) = _$CommunicateConfigRespImpl;

  factory _CommunicateConfigResp.fromJson(Map<String, dynamic> json) =
      _$CommunicateConfigRespImpl.fromJson;

  @override
  RoomConfigResp get roomConfigResp;
  @override
  GiftConfigResp get giftConfigResp;
  @override
  @JsonKey(ignore: true)
  _$$CommunicateConfigRespImplCopyWith<_$CommunicateConfigRespImpl>
      get copyWith => throw _privateConstructorUsedError;
}

RoomConfigResp _$RoomConfigRespFromJson(Map<String, dynamic> json) {
  return _RoomConfigResp.fromJson(json);
}

/// @nodoc
mixin _$RoomConfigResp {
  int get createRoomDayLimit => throw _privateConstructorUsedError;
  int get roomTTLInHour => throw _privateConstructorUsedError;
  int get maxUserInRoom => throw _privateConstructorUsedError;
  int get roomBoostActivities => throw _privateConstructorUsedError;
  int get roomTitleMaxLength => throw _privateConstructorUsedError;
  int get roomAnnouncementMaxLength => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RoomConfigRespCopyWith<RoomConfigResp> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RoomConfigRespCopyWith<$Res> {
  factory $RoomConfigRespCopyWith(
          RoomConfigResp value, $Res Function(RoomConfigResp) then) =
      _$RoomConfigRespCopyWithImpl<$Res, RoomConfigResp>;
  @useResult
  $Res call(
      {int createRoomDayLimit,
      int roomTTLInHour,
      int maxUserInRoom,
      int roomBoostActivities,
      int roomTitleMaxLength,
      int roomAnnouncementMaxLength});
}

/// @nodoc
class _$RoomConfigRespCopyWithImpl<$Res, $Val extends RoomConfigResp>
    implements $RoomConfigRespCopyWith<$Res> {
  _$RoomConfigRespCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? createRoomDayLimit = null,
    Object? roomTTLInHour = null,
    Object? maxUserInRoom = null,
    Object? roomBoostActivities = null,
    Object? roomTitleMaxLength = null,
    Object? roomAnnouncementMaxLength = null,
  }) {
    return _then(_value.copyWith(
      createRoomDayLimit: null == createRoomDayLimit
          ? _value.createRoomDayLimit
          : createRoomDayLimit // ignore: cast_nullable_to_non_nullable
              as int,
      roomTTLInHour: null == roomTTLInHour
          ? _value.roomTTLInHour
          : roomTTLInHour // ignore: cast_nullable_to_non_nullable
              as int,
      maxUserInRoom: null == maxUserInRoom
          ? _value.maxUserInRoom
          : maxUserInRoom // ignore: cast_nullable_to_non_nullable
              as int,
      roomBoostActivities: null == roomBoostActivities
          ? _value.roomBoostActivities
          : roomBoostActivities // ignore: cast_nullable_to_non_nullable
              as int,
      roomTitleMaxLength: null == roomTitleMaxLength
          ? _value.roomTitleMaxLength
          : roomTitleMaxLength // ignore: cast_nullable_to_non_nullable
              as int,
      roomAnnouncementMaxLength: null == roomAnnouncementMaxLength
          ? _value.roomAnnouncementMaxLength
          : roomAnnouncementMaxLength // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RoomConfigRespImplCopyWith<$Res>
    implements $RoomConfigRespCopyWith<$Res> {
  factory _$$RoomConfigRespImplCopyWith(_$RoomConfigRespImpl value,
          $Res Function(_$RoomConfigRespImpl) then) =
      __$$RoomConfigRespImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int createRoomDayLimit,
      int roomTTLInHour,
      int maxUserInRoom,
      int roomBoostActivities,
      int roomTitleMaxLength,
      int roomAnnouncementMaxLength});
}

/// @nodoc
class __$$RoomConfigRespImplCopyWithImpl<$Res>
    extends _$RoomConfigRespCopyWithImpl<$Res, _$RoomConfigRespImpl>
    implements _$$RoomConfigRespImplCopyWith<$Res> {
  __$$RoomConfigRespImplCopyWithImpl(
      _$RoomConfigRespImpl _value, $Res Function(_$RoomConfigRespImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? createRoomDayLimit = null,
    Object? roomTTLInHour = null,
    Object? maxUserInRoom = null,
    Object? roomBoostActivities = null,
    Object? roomTitleMaxLength = null,
    Object? roomAnnouncementMaxLength = null,
  }) {
    return _then(_$RoomConfigRespImpl(
      createRoomDayLimit: null == createRoomDayLimit
          ? _value.createRoomDayLimit
          : createRoomDayLimit // ignore: cast_nullable_to_non_nullable
              as int,
      roomTTLInHour: null == roomTTLInHour
          ? _value.roomTTLInHour
          : roomTTLInHour // ignore: cast_nullable_to_non_nullable
              as int,
      maxUserInRoom: null == maxUserInRoom
          ? _value.maxUserInRoom
          : maxUserInRoom // ignore: cast_nullable_to_non_nullable
              as int,
      roomBoostActivities: null == roomBoostActivities
          ? _value.roomBoostActivities
          : roomBoostActivities // ignore: cast_nullable_to_non_nullable
              as int,
      roomTitleMaxLength: null == roomTitleMaxLength
          ? _value.roomTitleMaxLength
          : roomTitleMaxLength // ignore: cast_nullable_to_non_nullable
              as int,
      roomAnnouncementMaxLength: null == roomAnnouncementMaxLength
          ? _value.roomAnnouncementMaxLength
          : roomAnnouncementMaxLength // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RoomConfigRespImpl implements _RoomConfigResp {
  const _$RoomConfigRespImpl(
      {this.createRoomDayLimit = AppConfigDefaults.createRoomDayLimit,
      this.roomTTLInHour = AppConfigDefaults.roomTTLInHour,
      this.maxUserInRoom = AppConfigDefaults.maxUserInRoom,
      this.roomBoostActivities = AppConfigDefaults.roomBoostActivities,
      this.roomTitleMaxLength = AppConfigDefaults.roomTitleMaxLength,
      this.roomAnnouncementMaxLength =
          AppConfigDefaults.roomAnnouncementMaxLength});

  factory _$RoomConfigRespImpl.fromJson(Map<String, dynamic> json) =>
      _$$RoomConfigRespImplFromJson(json);

  @override
  @JsonKey()
  final int createRoomDayLimit;
  @override
  @JsonKey()
  final int roomTTLInHour;
  @override
  @JsonKey()
  final int maxUserInRoom;
  @override
  @JsonKey()
  final int roomBoostActivities;
  @override
  @JsonKey()
  final int roomTitleMaxLength;
  @override
  @JsonKey()
  final int roomAnnouncementMaxLength;

  @override
  String toString() {
    return 'RoomConfigResp(createRoomDayLimit: $createRoomDayLimit, roomTTLInHour: $roomTTLInHour, maxUserInRoom: $maxUserInRoom, roomBoostActivities: $roomBoostActivities, roomTitleMaxLength: $roomTitleMaxLength, roomAnnouncementMaxLength: $roomAnnouncementMaxLength)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RoomConfigRespImpl &&
            (identical(other.createRoomDayLimit, createRoomDayLimit) ||
                other.createRoomDayLimit == createRoomDayLimit) &&
            (identical(other.roomTTLInHour, roomTTLInHour) ||
                other.roomTTLInHour == roomTTLInHour) &&
            (identical(other.maxUserInRoom, maxUserInRoom) ||
                other.maxUserInRoom == maxUserInRoom) &&
            (identical(other.roomBoostActivities, roomBoostActivities) ||
                other.roomBoostActivities == roomBoostActivities) &&
            (identical(other.roomTitleMaxLength, roomTitleMaxLength) ||
                other.roomTitleMaxLength == roomTitleMaxLength) &&
            (identical(other.roomAnnouncementMaxLength,
                    roomAnnouncementMaxLength) ||
                other.roomAnnouncementMaxLength == roomAnnouncementMaxLength));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      createRoomDayLimit,
      roomTTLInHour,
      maxUserInRoom,
      roomBoostActivities,
      roomTitleMaxLength,
      roomAnnouncementMaxLength);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RoomConfigRespImplCopyWith<_$RoomConfigRespImpl> get copyWith =>
      __$$RoomConfigRespImplCopyWithImpl<_$RoomConfigRespImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RoomConfigRespImplToJson(
      this,
    );
  }
}

abstract class _RoomConfigResp implements RoomConfigResp {
  const factory _RoomConfigResp(
      {final int createRoomDayLimit,
      final int roomTTLInHour,
      final int maxUserInRoom,
      final int roomBoostActivities,
      final int roomTitleMaxLength,
      final int roomAnnouncementMaxLength}) = _$RoomConfigRespImpl;

  factory _RoomConfigResp.fromJson(Map<String, dynamic> json) =
      _$RoomConfigRespImpl.fromJson;

  @override
  int get createRoomDayLimit;
  @override
  int get roomTTLInHour;
  @override
  int get maxUserInRoom;
  @override
  int get roomBoostActivities;
  @override
  int get roomTitleMaxLength;
  @override
  int get roomAnnouncementMaxLength;
  @override
  @JsonKey(ignore: true)
  _$$RoomConfigRespImplCopyWith<_$RoomConfigRespImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

GiftConfigResp _$GiftConfigRespFromJson(Map<String, dynamic> json) {
  return _GiftConfigResp.fromJson(json);
}

/// @nodoc
mixin _$GiftConfigResp {
  int get unactivatedGiftExpireDays => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GiftConfigRespCopyWith<GiftConfigResp> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GiftConfigRespCopyWith<$Res> {
  factory $GiftConfigRespCopyWith(
          GiftConfigResp value, $Res Function(GiftConfigResp) then) =
      _$GiftConfigRespCopyWithImpl<$Res, GiftConfigResp>;
  @useResult
  $Res call({int unactivatedGiftExpireDays});
}

/// @nodoc
class _$GiftConfigRespCopyWithImpl<$Res, $Val extends GiftConfigResp>
    implements $GiftConfigRespCopyWith<$Res> {
  _$GiftConfigRespCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? unactivatedGiftExpireDays = null,
  }) {
    return _then(_value.copyWith(
      unactivatedGiftExpireDays: null == unactivatedGiftExpireDays
          ? _value.unactivatedGiftExpireDays
          : unactivatedGiftExpireDays // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GiftConfigRespImplCopyWith<$Res>
    implements $GiftConfigRespCopyWith<$Res> {
  factory _$$GiftConfigRespImplCopyWith(_$GiftConfigRespImpl value,
          $Res Function(_$GiftConfigRespImpl) then) =
      __$$GiftConfigRespImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int unactivatedGiftExpireDays});
}

/// @nodoc
class __$$GiftConfigRespImplCopyWithImpl<$Res>
    extends _$GiftConfigRespCopyWithImpl<$Res, _$GiftConfigRespImpl>
    implements _$$GiftConfigRespImplCopyWith<$Res> {
  __$$GiftConfigRespImplCopyWithImpl(
      _$GiftConfigRespImpl _value, $Res Function(_$GiftConfigRespImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? unactivatedGiftExpireDays = null,
  }) {
    return _then(_$GiftConfigRespImpl(
      unactivatedGiftExpireDays: null == unactivatedGiftExpireDays
          ? _value.unactivatedGiftExpireDays
          : unactivatedGiftExpireDays // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GiftConfigRespImpl implements _GiftConfigResp {
  const _$GiftConfigRespImpl(
      {this.unactivatedGiftExpireDays =
          AppConfigDefaults.unactivatedGiftExpireDays});

  factory _$GiftConfigRespImpl.fromJson(Map<String, dynamic> json) =>
      _$$GiftConfigRespImplFromJson(json);

  @override
  @JsonKey()
  final int unactivatedGiftExpireDays;

  @override
  String toString() {
    return 'GiftConfigResp(unactivatedGiftExpireDays: $unactivatedGiftExpireDays)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GiftConfigRespImpl &&
            (identical(other.unactivatedGiftExpireDays,
                    unactivatedGiftExpireDays) ||
                other.unactivatedGiftExpireDays == unactivatedGiftExpireDays));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, unactivatedGiftExpireDays);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GiftConfigRespImplCopyWith<_$GiftConfigRespImpl> get copyWith =>
      __$$GiftConfigRespImplCopyWithImpl<_$GiftConfigRespImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GiftConfigRespImplToJson(
      this,
    );
  }
}

abstract class _GiftConfigResp implements GiftConfigResp {
  const factory _GiftConfigResp({final int unactivatedGiftExpireDays}) =
      _$GiftConfigRespImpl;

  factory _GiftConfigResp.fromJson(Map<String, dynamic> json) =
      _$GiftConfigRespImpl.fromJson;

  @override
  int get unactivatedGiftExpireDays;
  @override
  @JsonKey(ignore: true)
  _$$GiftConfigRespImplCopyWith<_$GiftConfigRespImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RtcConfigResp _$RtcConfigRespFromJson(Map<String, dynamic> json) {
  return _RtcConfigResp.fromJson(json);
}

/// @nodoc
mixin _$RtcConfigResp {
  InstantChatConfigResp get instantChatConfigResp =>
      throw _privateConstructorUsedError;
  InstantVoiceCallConfigResp get instantVoiceCallConfigResp =>
      throw _privateConstructorUsedError;
  VoiceCallConfigResp get voiceCallConfigResp =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RtcConfigRespCopyWith<RtcConfigResp> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RtcConfigRespCopyWith<$Res> {
  factory $RtcConfigRespCopyWith(
          RtcConfigResp value, $Res Function(RtcConfigResp) then) =
      _$RtcConfigRespCopyWithImpl<$Res, RtcConfigResp>;
  @useResult
  $Res call(
      {InstantChatConfigResp instantChatConfigResp,
      InstantVoiceCallConfigResp instantVoiceCallConfigResp,
      VoiceCallConfigResp voiceCallConfigResp});

  $InstantChatConfigRespCopyWith<$Res> get instantChatConfigResp;
  $InstantVoiceCallConfigRespCopyWith<$Res> get instantVoiceCallConfigResp;
  $VoiceCallConfigRespCopyWith<$Res> get voiceCallConfigResp;
}

/// @nodoc
class _$RtcConfigRespCopyWithImpl<$Res, $Val extends RtcConfigResp>
    implements $RtcConfigRespCopyWith<$Res> {
  _$RtcConfigRespCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? instantChatConfigResp = null,
    Object? instantVoiceCallConfigResp = null,
    Object? voiceCallConfigResp = null,
  }) {
    return _then(_value.copyWith(
      instantChatConfigResp: null == instantChatConfigResp
          ? _value.instantChatConfigResp
          : instantChatConfigResp // ignore: cast_nullable_to_non_nullable
              as InstantChatConfigResp,
      instantVoiceCallConfigResp: null == instantVoiceCallConfigResp
          ? _value.instantVoiceCallConfigResp
          : instantVoiceCallConfigResp // ignore: cast_nullable_to_non_nullable
              as InstantVoiceCallConfigResp,
      voiceCallConfigResp: null == voiceCallConfigResp
          ? _value.voiceCallConfigResp
          : voiceCallConfigResp // ignore: cast_nullable_to_non_nullable
              as VoiceCallConfigResp,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $InstantChatConfigRespCopyWith<$Res> get instantChatConfigResp {
    return $InstantChatConfigRespCopyWith<$Res>(_value.instantChatConfigResp,
        (value) {
      return _then(_value.copyWith(instantChatConfigResp: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $InstantVoiceCallConfigRespCopyWith<$Res> get instantVoiceCallConfigResp {
    return $InstantVoiceCallConfigRespCopyWith<$Res>(
        _value.instantVoiceCallConfigResp, (value) {
      return _then(_value.copyWith(instantVoiceCallConfigResp: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $VoiceCallConfigRespCopyWith<$Res> get voiceCallConfigResp {
    return $VoiceCallConfigRespCopyWith<$Res>(_value.voiceCallConfigResp,
        (value) {
      return _then(_value.copyWith(voiceCallConfigResp: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RtcConfigRespImplCopyWith<$Res>
    implements $RtcConfigRespCopyWith<$Res> {
  factory _$$RtcConfigRespImplCopyWith(
          _$RtcConfigRespImpl value, $Res Function(_$RtcConfigRespImpl) then) =
      __$$RtcConfigRespImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {InstantChatConfigResp instantChatConfigResp,
      InstantVoiceCallConfigResp instantVoiceCallConfigResp,
      VoiceCallConfigResp voiceCallConfigResp});

  @override
  $InstantChatConfigRespCopyWith<$Res> get instantChatConfigResp;
  @override
  $InstantVoiceCallConfigRespCopyWith<$Res> get instantVoiceCallConfigResp;
  @override
  $VoiceCallConfigRespCopyWith<$Res> get voiceCallConfigResp;
}

/// @nodoc
class __$$RtcConfigRespImplCopyWithImpl<$Res>
    extends _$RtcConfigRespCopyWithImpl<$Res, _$RtcConfigRespImpl>
    implements _$$RtcConfigRespImplCopyWith<$Res> {
  __$$RtcConfigRespImplCopyWithImpl(
      _$RtcConfigRespImpl _value, $Res Function(_$RtcConfigRespImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? instantChatConfigResp = null,
    Object? instantVoiceCallConfigResp = null,
    Object? voiceCallConfigResp = null,
  }) {
    return _then(_$RtcConfigRespImpl(
      instantChatConfigResp: null == instantChatConfigResp
          ? _value.instantChatConfigResp
          : instantChatConfigResp // ignore: cast_nullable_to_non_nullable
              as InstantChatConfigResp,
      instantVoiceCallConfigResp: null == instantVoiceCallConfigResp
          ? _value.instantVoiceCallConfigResp
          : instantVoiceCallConfigResp // ignore: cast_nullable_to_non_nullable
              as InstantVoiceCallConfigResp,
      voiceCallConfigResp: null == voiceCallConfigResp
          ? _value.voiceCallConfigResp
          : voiceCallConfigResp // ignore: cast_nullable_to_non_nullable
              as VoiceCallConfigResp,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RtcConfigRespImpl implements _RtcConfigResp {
  const _$RtcConfigRespImpl(
      {this.instantChatConfigResp = AppConfigDefaults.instantChatDefaults,
      this.instantVoiceCallConfigResp =
          AppConfigDefaults.instantVoiceCallDefaults,
      this.voiceCallConfigResp = AppConfigDefaults.voiceCallDefaults});

  factory _$RtcConfigRespImpl.fromJson(Map<String, dynamic> json) =>
      _$$RtcConfigRespImplFromJson(json);

  @override
  @JsonKey()
  final InstantChatConfigResp instantChatConfigResp;
  @override
  @JsonKey()
  final InstantVoiceCallConfigResp instantVoiceCallConfigResp;
  @override
  @JsonKey()
  final VoiceCallConfigResp voiceCallConfigResp;

  @override
  String toString() {
    return 'RtcConfigResp(instantChatConfigResp: $instantChatConfigResp, instantVoiceCallConfigResp: $instantVoiceCallConfigResp, voiceCallConfigResp: $voiceCallConfigResp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RtcConfigRespImpl &&
            (identical(other.instantChatConfigResp, instantChatConfigResp) ||
                other.instantChatConfigResp == instantChatConfigResp) &&
            (identical(other.instantVoiceCallConfigResp,
                    instantVoiceCallConfigResp) ||
                other.instantVoiceCallConfigResp ==
                    instantVoiceCallConfigResp) &&
            (identical(other.voiceCallConfigResp, voiceCallConfigResp) ||
                other.voiceCallConfigResp == voiceCallConfigResp));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, instantChatConfigResp,
      instantVoiceCallConfigResp, voiceCallConfigResp);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RtcConfigRespImplCopyWith<_$RtcConfigRespImpl> get copyWith =>
      __$$RtcConfigRespImplCopyWithImpl<_$RtcConfigRespImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RtcConfigRespImplToJson(
      this,
    );
  }
}

abstract class _RtcConfigResp implements RtcConfigResp {
  const factory _RtcConfigResp(
      {final InstantChatConfigResp instantChatConfigResp,
      final InstantVoiceCallConfigResp instantVoiceCallConfigResp,
      final VoiceCallConfigResp voiceCallConfigResp}) = _$RtcConfigRespImpl;

  factory _RtcConfigResp.fromJson(Map<String, dynamic> json) =
      _$RtcConfigRespImpl.fromJson;

  @override
  InstantChatConfigResp get instantChatConfigResp;
  @override
  InstantVoiceCallConfigResp get instantVoiceCallConfigResp;
  @override
  VoiceCallConfigResp get voiceCallConfigResp;
  @override
  @JsonKey(ignore: true)
  _$$RtcConfigRespImplCopyWith<_$RtcConfigRespImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

VoiceCallConfigResp _$VoiceCallConfigRespFromJson(Map<String, dynamic> json) {
  return _VoiceCallConfigResp.fromJson(json);
}

/// @nodoc
mixin _$VoiceCallConfigResp {
  int get pendingTimeoutSeconds => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VoiceCallConfigRespCopyWith<VoiceCallConfigResp> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VoiceCallConfigRespCopyWith<$Res> {
  factory $VoiceCallConfigRespCopyWith(
          VoiceCallConfigResp value, $Res Function(VoiceCallConfigResp) then) =
      _$VoiceCallConfigRespCopyWithImpl<$Res, VoiceCallConfigResp>;
  @useResult
  $Res call({int pendingTimeoutSeconds});
}

/// @nodoc
class _$VoiceCallConfigRespCopyWithImpl<$Res, $Val extends VoiceCallConfigResp>
    implements $VoiceCallConfigRespCopyWith<$Res> {
  _$VoiceCallConfigRespCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pendingTimeoutSeconds = null,
  }) {
    return _then(_value.copyWith(
      pendingTimeoutSeconds: null == pendingTimeoutSeconds
          ? _value.pendingTimeoutSeconds
          : pendingTimeoutSeconds // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VoiceCallConfigRespImplCopyWith<$Res>
    implements $VoiceCallConfigRespCopyWith<$Res> {
  factory _$$VoiceCallConfigRespImplCopyWith(_$VoiceCallConfigRespImpl value,
          $Res Function(_$VoiceCallConfigRespImpl) then) =
      __$$VoiceCallConfigRespImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int pendingTimeoutSeconds});
}

/// @nodoc
class __$$VoiceCallConfigRespImplCopyWithImpl<$Res>
    extends _$VoiceCallConfigRespCopyWithImpl<$Res, _$VoiceCallConfigRespImpl>
    implements _$$VoiceCallConfigRespImplCopyWith<$Res> {
  __$$VoiceCallConfigRespImplCopyWithImpl(_$VoiceCallConfigRespImpl _value,
      $Res Function(_$VoiceCallConfigRespImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pendingTimeoutSeconds = null,
  }) {
    return _then(_$VoiceCallConfigRespImpl(
      pendingTimeoutSeconds: null == pendingTimeoutSeconds
          ? _value.pendingTimeoutSeconds
          : pendingTimeoutSeconds // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VoiceCallConfigRespImpl implements _VoiceCallConfigResp {
  const _$VoiceCallConfigRespImpl(
      {this.pendingTimeoutSeconds = AppConfigDefaults.pendingTimeoutSeconds});

  factory _$VoiceCallConfigRespImpl.fromJson(Map<String, dynamic> json) =>
      _$$VoiceCallConfigRespImplFromJson(json);

  @override
  @JsonKey()
  final int pendingTimeoutSeconds;

  @override
  String toString() {
    return 'VoiceCallConfigResp(pendingTimeoutSeconds: $pendingTimeoutSeconds)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VoiceCallConfigRespImpl &&
            (identical(other.pendingTimeoutSeconds, pendingTimeoutSeconds) ||
                other.pendingTimeoutSeconds == pendingTimeoutSeconds));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, pendingTimeoutSeconds);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VoiceCallConfigRespImplCopyWith<_$VoiceCallConfigRespImpl> get copyWith =>
      __$$VoiceCallConfigRespImplCopyWithImpl<_$VoiceCallConfigRespImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VoiceCallConfigRespImplToJson(
      this,
    );
  }
}

abstract class _VoiceCallConfigResp implements VoiceCallConfigResp {
  const factory _VoiceCallConfigResp({final int pendingTimeoutSeconds}) =
      _$VoiceCallConfigRespImpl;

  factory _VoiceCallConfigResp.fromJson(Map<String, dynamic> json) =
      _$VoiceCallConfigRespImpl.fromJson;

  @override
  int get pendingTimeoutSeconds;
  @override
  @JsonKey(ignore: true)
  _$$VoiceCallConfigRespImplCopyWith<_$VoiceCallConfigRespImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

InstantChatConfigResp _$InstantChatConfigRespFromJson(
    Map<String, dynamic> json) {
  return _InstantChatConfigResp.fromJson(json);
}

/// @nodoc
mixin _$InstantChatConfigResp {
  int get pendingTimeoutSeconds => throw _privateConstructorUsedError;
  int get maleDailyMatchCount => throw _privateConstructorUsedError;
  int get femaleDailyMatchCount => throw _privateConstructorUsedError;
  int get gemCost => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $InstantChatConfigRespCopyWith<InstantChatConfigResp> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InstantChatConfigRespCopyWith<$Res> {
  factory $InstantChatConfigRespCopyWith(InstantChatConfigResp value,
          $Res Function(InstantChatConfigResp) then) =
      _$InstantChatConfigRespCopyWithImpl<$Res, InstantChatConfigResp>;
  @useResult
  $Res call(
      {int pendingTimeoutSeconds,
      int maleDailyMatchCount,
      int femaleDailyMatchCount,
      int gemCost});
}

/// @nodoc
class _$InstantChatConfigRespCopyWithImpl<$Res,
        $Val extends InstantChatConfigResp>
    implements $InstantChatConfigRespCopyWith<$Res> {
  _$InstantChatConfigRespCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pendingTimeoutSeconds = null,
    Object? maleDailyMatchCount = null,
    Object? femaleDailyMatchCount = null,
    Object? gemCost = null,
  }) {
    return _then(_value.copyWith(
      pendingTimeoutSeconds: null == pendingTimeoutSeconds
          ? _value.pendingTimeoutSeconds
          : pendingTimeoutSeconds // ignore: cast_nullable_to_non_nullable
              as int,
      maleDailyMatchCount: null == maleDailyMatchCount
          ? _value.maleDailyMatchCount
          : maleDailyMatchCount // ignore: cast_nullable_to_non_nullable
              as int,
      femaleDailyMatchCount: null == femaleDailyMatchCount
          ? _value.femaleDailyMatchCount
          : femaleDailyMatchCount // ignore: cast_nullable_to_non_nullable
              as int,
      gemCost: null == gemCost
          ? _value.gemCost
          : gemCost // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$InstantChatConfigRespImplCopyWith<$Res>
    implements $InstantChatConfigRespCopyWith<$Res> {
  factory _$$InstantChatConfigRespImplCopyWith(
          _$InstantChatConfigRespImpl value,
          $Res Function(_$InstantChatConfigRespImpl) then) =
      __$$InstantChatConfigRespImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int pendingTimeoutSeconds,
      int maleDailyMatchCount,
      int femaleDailyMatchCount,
      int gemCost});
}

/// @nodoc
class __$$InstantChatConfigRespImplCopyWithImpl<$Res>
    extends _$InstantChatConfigRespCopyWithImpl<$Res,
        _$InstantChatConfigRespImpl>
    implements _$$InstantChatConfigRespImplCopyWith<$Res> {
  __$$InstantChatConfigRespImplCopyWithImpl(_$InstantChatConfigRespImpl _value,
      $Res Function(_$InstantChatConfigRespImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pendingTimeoutSeconds = null,
    Object? maleDailyMatchCount = null,
    Object? femaleDailyMatchCount = null,
    Object? gemCost = null,
  }) {
    return _then(_$InstantChatConfigRespImpl(
      pendingTimeoutSeconds: null == pendingTimeoutSeconds
          ? _value.pendingTimeoutSeconds
          : pendingTimeoutSeconds // ignore: cast_nullable_to_non_nullable
              as int,
      maleDailyMatchCount: null == maleDailyMatchCount
          ? _value.maleDailyMatchCount
          : maleDailyMatchCount // ignore: cast_nullable_to_non_nullable
              as int,
      femaleDailyMatchCount: null == femaleDailyMatchCount
          ? _value.femaleDailyMatchCount
          : femaleDailyMatchCount // ignore: cast_nullable_to_non_nullable
              as int,
      gemCost: null == gemCost
          ? _value.gemCost
          : gemCost // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$InstantChatConfigRespImpl implements _InstantChatConfigResp {
  const _$InstantChatConfigRespImpl(
      {this.pendingTimeoutSeconds = AppConfigDefaults.pendingTimeoutSeconds,
      this.maleDailyMatchCount = AppConfigDefaults.maleDailyMatchCount,
      this.femaleDailyMatchCount = AppConfigDefaults.femaleDailyMatchCount,
      this.gemCost = AppConfigDefaults.gemCost});

  factory _$InstantChatConfigRespImpl.fromJson(Map<String, dynamic> json) =>
      _$$InstantChatConfigRespImplFromJson(json);

  @override
  @JsonKey()
  final int pendingTimeoutSeconds;
  @override
  @JsonKey()
  final int maleDailyMatchCount;
  @override
  @JsonKey()
  final int femaleDailyMatchCount;
  @override
  @JsonKey()
  final int gemCost;

  @override
  String toString() {
    return 'InstantChatConfigResp(pendingTimeoutSeconds: $pendingTimeoutSeconds, maleDailyMatchCount: $maleDailyMatchCount, femaleDailyMatchCount: $femaleDailyMatchCount, gemCost: $gemCost)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InstantChatConfigRespImpl &&
            (identical(other.pendingTimeoutSeconds, pendingTimeoutSeconds) ||
                other.pendingTimeoutSeconds == pendingTimeoutSeconds) &&
            (identical(other.maleDailyMatchCount, maleDailyMatchCount) ||
                other.maleDailyMatchCount == maleDailyMatchCount) &&
            (identical(other.femaleDailyMatchCount, femaleDailyMatchCount) ||
                other.femaleDailyMatchCount == femaleDailyMatchCount) &&
            (identical(other.gemCost, gemCost) || other.gemCost == gemCost));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, pendingTimeoutSeconds,
      maleDailyMatchCount, femaleDailyMatchCount, gemCost);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$InstantChatConfigRespImplCopyWith<_$InstantChatConfigRespImpl>
      get copyWith => __$$InstantChatConfigRespImplCopyWithImpl<
          _$InstantChatConfigRespImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$InstantChatConfigRespImplToJson(
      this,
    );
  }
}

abstract class _InstantChatConfigResp implements InstantChatConfigResp {
  const factory _InstantChatConfigResp(
      {final int pendingTimeoutSeconds,
      final int maleDailyMatchCount,
      final int femaleDailyMatchCount,
      final int gemCost}) = _$InstantChatConfigRespImpl;

  factory _InstantChatConfigResp.fromJson(Map<String, dynamic> json) =
      _$InstantChatConfigRespImpl.fromJson;

  @override
  int get pendingTimeoutSeconds;
  @override
  int get maleDailyMatchCount;
  @override
  int get femaleDailyMatchCount;
  @override
  int get gemCost;
  @override
  @JsonKey(ignore: true)
  _$$InstantChatConfigRespImplCopyWith<_$InstantChatConfigRespImpl>
      get copyWith => throw _privateConstructorUsedError;
}

InstantVoiceCallConfigResp _$InstantVoiceCallConfigRespFromJson(
    Map<String, dynamic> json) {
  return _InstantVoiceCallConfigResp.fromJson(json);
}

/// @nodoc
mixin _$InstantVoiceCallConfigResp {
  int get pendingTimeoutSeconds => throw _privateConstructorUsedError;
  int get maleDailyMatchCount => throw _privateConstructorUsedError;
  int get femaleDailyMatchCount => throw _privateConstructorUsedError;
  int get gemCost => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $InstantVoiceCallConfigRespCopyWith<InstantVoiceCallConfigResp>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InstantVoiceCallConfigRespCopyWith<$Res> {
  factory $InstantVoiceCallConfigRespCopyWith(InstantVoiceCallConfigResp value,
          $Res Function(InstantVoiceCallConfigResp) then) =
      _$InstantVoiceCallConfigRespCopyWithImpl<$Res,
          InstantVoiceCallConfigResp>;
  @useResult
  $Res call(
      {int pendingTimeoutSeconds,
      int maleDailyMatchCount,
      int femaleDailyMatchCount,
      int gemCost});
}

/// @nodoc
class _$InstantVoiceCallConfigRespCopyWithImpl<$Res,
        $Val extends InstantVoiceCallConfigResp>
    implements $InstantVoiceCallConfigRespCopyWith<$Res> {
  _$InstantVoiceCallConfigRespCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pendingTimeoutSeconds = null,
    Object? maleDailyMatchCount = null,
    Object? femaleDailyMatchCount = null,
    Object? gemCost = null,
  }) {
    return _then(_value.copyWith(
      pendingTimeoutSeconds: null == pendingTimeoutSeconds
          ? _value.pendingTimeoutSeconds
          : pendingTimeoutSeconds // ignore: cast_nullable_to_non_nullable
              as int,
      maleDailyMatchCount: null == maleDailyMatchCount
          ? _value.maleDailyMatchCount
          : maleDailyMatchCount // ignore: cast_nullable_to_non_nullable
              as int,
      femaleDailyMatchCount: null == femaleDailyMatchCount
          ? _value.femaleDailyMatchCount
          : femaleDailyMatchCount // ignore: cast_nullable_to_non_nullable
              as int,
      gemCost: null == gemCost
          ? _value.gemCost
          : gemCost // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$InstantVoiceCallConfigRespImplCopyWith<$Res>
    implements $InstantVoiceCallConfigRespCopyWith<$Res> {
  factory _$$InstantVoiceCallConfigRespImplCopyWith(
          _$InstantVoiceCallConfigRespImpl value,
          $Res Function(_$InstantVoiceCallConfigRespImpl) then) =
      __$$InstantVoiceCallConfigRespImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int pendingTimeoutSeconds,
      int maleDailyMatchCount,
      int femaleDailyMatchCount,
      int gemCost});
}

/// @nodoc
class __$$InstantVoiceCallConfigRespImplCopyWithImpl<$Res>
    extends _$InstantVoiceCallConfigRespCopyWithImpl<$Res,
        _$InstantVoiceCallConfigRespImpl>
    implements _$$InstantVoiceCallConfigRespImplCopyWith<$Res> {
  __$$InstantVoiceCallConfigRespImplCopyWithImpl(
      _$InstantVoiceCallConfigRespImpl _value,
      $Res Function(_$InstantVoiceCallConfigRespImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pendingTimeoutSeconds = null,
    Object? maleDailyMatchCount = null,
    Object? femaleDailyMatchCount = null,
    Object? gemCost = null,
  }) {
    return _then(_$InstantVoiceCallConfigRespImpl(
      pendingTimeoutSeconds: null == pendingTimeoutSeconds
          ? _value.pendingTimeoutSeconds
          : pendingTimeoutSeconds // ignore: cast_nullable_to_non_nullable
              as int,
      maleDailyMatchCount: null == maleDailyMatchCount
          ? _value.maleDailyMatchCount
          : maleDailyMatchCount // ignore: cast_nullable_to_non_nullable
              as int,
      femaleDailyMatchCount: null == femaleDailyMatchCount
          ? _value.femaleDailyMatchCount
          : femaleDailyMatchCount // ignore: cast_nullable_to_non_nullable
              as int,
      gemCost: null == gemCost
          ? _value.gemCost
          : gemCost // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$InstantVoiceCallConfigRespImpl implements _InstantVoiceCallConfigResp {
  const _$InstantVoiceCallConfigRespImpl(
      {this.pendingTimeoutSeconds = AppConfigDefaults.pendingTimeoutSeconds,
      this.maleDailyMatchCount = AppConfigDefaults.maleDailyMatchCount,
      this.femaleDailyMatchCount = AppConfigDefaults.femaleDailyMatchCount,
      this.gemCost = AppConfigDefaults.gemCost});

  factory _$InstantVoiceCallConfigRespImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$InstantVoiceCallConfigRespImplFromJson(json);

  @override
  @JsonKey()
  final int pendingTimeoutSeconds;
  @override
  @JsonKey()
  final int maleDailyMatchCount;
  @override
  @JsonKey()
  final int femaleDailyMatchCount;
  @override
  @JsonKey()
  final int gemCost;

  @override
  String toString() {
    return 'InstantVoiceCallConfigResp(pendingTimeoutSeconds: $pendingTimeoutSeconds, maleDailyMatchCount: $maleDailyMatchCount, femaleDailyMatchCount: $femaleDailyMatchCount, gemCost: $gemCost)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InstantVoiceCallConfigRespImpl &&
            (identical(other.pendingTimeoutSeconds, pendingTimeoutSeconds) ||
                other.pendingTimeoutSeconds == pendingTimeoutSeconds) &&
            (identical(other.maleDailyMatchCount, maleDailyMatchCount) ||
                other.maleDailyMatchCount == maleDailyMatchCount) &&
            (identical(other.femaleDailyMatchCount, femaleDailyMatchCount) ||
                other.femaleDailyMatchCount == femaleDailyMatchCount) &&
            (identical(other.gemCost, gemCost) || other.gemCost == gemCost));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, pendingTimeoutSeconds,
      maleDailyMatchCount, femaleDailyMatchCount, gemCost);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$InstantVoiceCallConfigRespImplCopyWith<_$InstantVoiceCallConfigRespImpl>
      get copyWith => __$$InstantVoiceCallConfigRespImplCopyWithImpl<
          _$InstantVoiceCallConfigRespImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$InstantVoiceCallConfigRespImplToJson(
      this,
    );
  }
}

abstract class _InstantVoiceCallConfigResp
    implements InstantVoiceCallConfigResp {
  const factory _InstantVoiceCallConfigResp(
      {final int pendingTimeoutSeconds,
      final int maleDailyMatchCount,
      final int femaleDailyMatchCount,
      final int gemCost}) = _$InstantVoiceCallConfigRespImpl;

  factory _InstantVoiceCallConfigResp.fromJson(Map<String, dynamic> json) =
      _$InstantVoiceCallConfigRespImpl.fromJson;

  @override
  int get pendingTimeoutSeconds;
  @override
  int get maleDailyMatchCount;
  @override
  int get femaleDailyMatchCount;
  @override
  int get gemCost;
  @override
  @JsonKey(ignore: true)
  _$$InstantVoiceCallConfigRespImplCopyWith<_$InstantVoiceCallConfigRespImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ChatConfigResp _$ChatConfigRespFromJson(Map<String, dynamic> json) {
  return _ChatConfigResp.fromJson(json);
}

/// @nodoc
mixin _$ChatConfigResp {
  int get maxContentLength => throw _privateConstructorUsedError;
  int get msgTimeoutDay => throw _privateConstructorUsedError;
  int get dailyReceiverLimit => throw _privateConstructorUsedError;
  int get msgClientTimeoutSeconds => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ChatConfigRespCopyWith<ChatConfigResp> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChatConfigRespCopyWith<$Res> {
  factory $ChatConfigRespCopyWith(
          ChatConfigResp value, $Res Function(ChatConfigResp) then) =
      _$ChatConfigRespCopyWithImpl<$Res, ChatConfigResp>;
  @useResult
  $Res call(
      {int maxContentLength,
      int msgTimeoutDay,
      int dailyReceiverLimit,
      int msgClientTimeoutSeconds});
}

/// @nodoc
class _$ChatConfigRespCopyWithImpl<$Res, $Val extends ChatConfigResp>
    implements $ChatConfigRespCopyWith<$Res> {
  _$ChatConfigRespCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maxContentLength = null,
    Object? msgTimeoutDay = null,
    Object? dailyReceiverLimit = null,
    Object? msgClientTimeoutSeconds = null,
  }) {
    return _then(_value.copyWith(
      maxContentLength: null == maxContentLength
          ? _value.maxContentLength
          : maxContentLength // ignore: cast_nullable_to_non_nullable
              as int,
      msgTimeoutDay: null == msgTimeoutDay
          ? _value.msgTimeoutDay
          : msgTimeoutDay // ignore: cast_nullable_to_non_nullable
              as int,
      dailyReceiverLimit: null == dailyReceiverLimit
          ? _value.dailyReceiverLimit
          : dailyReceiverLimit // ignore: cast_nullable_to_non_nullable
              as int,
      msgClientTimeoutSeconds: null == msgClientTimeoutSeconds
          ? _value.msgClientTimeoutSeconds
          : msgClientTimeoutSeconds // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ChatConfigRespImplCopyWith<$Res>
    implements $ChatConfigRespCopyWith<$Res> {
  factory _$$ChatConfigRespImplCopyWith(_$ChatConfigRespImpl value,
          $Res Function(_$ChatConfigRespImpl) then) =
      __$$ChatConfigRespImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int maxContentLength,
      int msgTimeoutDay,
      int dailyReceiverLimit,
      int msgClientTimeoutSeconds});
}

/// @nodoc
class __$$ChatConfigRespImplCopyWithImpl<$Res>
    extends _$ChatConfigRespCopyWithImpl<$Res, _$ChatConfigRespImpl>
    implements _$$ChatConfigRespImplCopyWith<$Res> {
  __$$ChatConfigRespImplCopyWithImpl(
      _$ChatConfigRespImpl _value, $Res Function(_$ChatConfigRespImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maxContentLength = null,
    Object? msgTimeoutDay = null,
    Object? dailyReceiverLimit = null,
    Object? msgClientTimeoutSeconds = null,
  }) {
    return _then(_$ChatConfigRespImpl(
      maxContentLength: null == maxContentLength
          ? _value.maxContentLength
          : maxContentLength // ignore: cast_nullable_to_non_nullable
              as int,
      msgTimeoutDay: null == msgTimeoutDay
          ? _value.msgTimeoutDay
          : msgTimeoutDay // ignore: cast_nullable_to_non_nullable
              as int,
      dailyReceiverLimit: null == dailyReceiverLimit
          ? _value.dailyReceiverLimit
          : dailyReceiverLimit // ignore: cast_nullable_to_non_nullable
              as int,
      msgClientTimeoutSeconds: null == msgClientTimeoutSeconds
          ? _value.msgClientTimeoutSeconds
          : msgClientTimeoutSeconds // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ChatConfigRespImpl implements _ChatConfigResp {
  const _$ChatConfigRespImpl(
      {this.maxContentLength = AppConfigDefaults.maxContentLength,
      this.msgTimeoutDay = AppConfigDefaults.msgTimeoutDay,
      this.dailyReceiverLimit = AppConfigDefaults.dailyReceiverLimit,
      this.msgClientTimeoutSeconds =
          AppConfigDefaults.msgClientTimeoutSeconds});

  factory _$ChatConfigRespImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChatConfigRespImplFromJson(json);

  @override
  @JsonKey()
  final int maxContentLength;
  @override
  @JsonKey()
  final int msgTimeoutDay;
  @override
  @JsonKey()
  final int dailyReceiverLimit;
  @override
  @JsonKey()
  final int msgClientTimeoutSeconds;

  @override
  String toString() {
    return 'ChatConfigResp(maxContentLength: $maxContentLength, msgTimeoutDay: $msgTimeoutDay, dailyReceiverLimit: $dailyReceiverLimit, msgClientTimeoutSeconds: $msgClientTimeoutSeconds)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChatConfigRespImpl &&
            (identical(other.maxContentLength, maxContentLength) ||
                other.maxContentLength == maxContentLength) &&
            (identical(other.msgTimeoutDay, msgTimeoutDay) ||
                other.msgTimeoutDay == msgTimeoutDay) &&
            (identical(other.dailyReceiverLimit, dailyReceiverLimit) ||
                other.dailyReceiverLimit == dailyReceiverLimit) &&
            (identical(
                    other.msgClientTimeoutSeconds, msgClientTimeoutSeconds) ||
                other.msgClientTimeoutSeconds == msgClientTimeoutSeconds));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, maxContentLength, msgTimeoutDay,
      dailyReceiverLimit, msgClientTimeoutSeconds);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChatConfigRespImplCopyWith<_$ChatConfigRespImpl> get copyWith =>
      __$$ChatConfigRespImplCopyWithImpl<_$ChatConfigRespImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChatConfigRespImplToJson(
      this,
    );
  }
}

abstract class _ChatConfigResp implements ChatConfigResp {
  const factory _ChatConfigResp(
      {final int maxContentLength,
      final int msgTimeoutDay,
      final int dailyReceiverLimit,
      final int msgClientTimeoutSeconds}) = _$ChatConfigRespImpl;

  factory _ChatConfigResp.fromJson(Map<String, dynamic> json) =
      _$ChatConfigRespImpl.fromJson;

  @override
  int get maxContentLength;
  @override
  int get msgTimeoutDay;
  @override
  int get dailyReceiverLimit;
  @override
  int get msgClientTimeoutSeconds;
  @override
  @JsonKey(ignore: true)
  _$$ChatConfigRespImplCopyWith<_$ChatConfigRespImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FileConfigResp _$FileConfigRespFromJson(Map<String, dynamic> json) {
  return _FileConfigResp.fromJson(json);
}

/// @nodoc
mixin _$FileConfigResp {
  int get fileSizeLimit => throw _privateConstructorUsedError;
  int get imageSizeLimit => throw _privateConstructorUsedError;
  int get videoSizeLimit => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FileConfigRespCopyWith<FileConfigResp> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FileConfigRespCopyWith<$Res> {
  factory $FileConfigRespCopyWith(
          FileConfigResp value, $Res Function(FileConfigResp) then) =
      _$FileConfigRespCopyWithImpl<$Res, FileConfigResp>;
  @useResult
  $Res call({int fileSizeLimit, int imageSizeLimit, int videoSizeLimit});
}

/// @nodoc
class _$FileConfigRespCopyWithImpl<$Res, $Val extends FileConfigResp>
    implements $FileConfigRespCopyWith<$Res> {
  _$FileConfigRespCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileSizeLimit = null,
    Object? imageSizeLimit = null,
    Object? videoSizeLimit = null,
  }) {
    return _then(_value.copyWith(
      fileSizeLimit: null == fileSizeLimit
          ? _value.fileSizeLimit
          : fileSizeLimit // ignore: cast_nullable_to_non_nullable
              as int,
      imageSizeLimit: null == imageSizeLimit
          ? _value.imageSizeLimit
          : imageSizeLimit // ignore: cast_nullable_to_non_nullable
              as int,
      videoSizeLimit: null == videoSizeLimit
          ? _value.videoSizeLimit
          : videoSizeLimit // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FileConfigRespImplCopyWith<$Res>
    implements $FileConfigRespCopyWith<$Res> {
  factory _$$FileConfigRespImplCopyWith(_$FileConfigRespImpl value,
          $Res Function(_$FileConfigRespImpl) then) =
      __$$FileConfigRespImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int fileSizeLimit, int imageSizeLimit, int videoSizeLimit});
}

/// @nodoc
class __$$FileConfigRespImplCopyWithImpl<$Res>
    extends _$FileConfigRespCopyWithImpl<$Res, _$FileConfigRespImpl>
    implements _$$FileConfigRespImplCopyWith<$Res> {
  __$$FileConfigRespImplCopyWithImpl(
      _$FileConfigRespImpl _value, $Res Function(_$FileConfigRespImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileSizeLimit = null,
    Object? imageSizeLimit = null,
    Object? videoSizeLimit = null,
  }) {
    return _then(_$FileConfigRespImpl(
      fileSizeLimit: null == fileSizeLimit
          ? _value.fileSizeLimit
          : fileSizeLimit // ignore: cast_nullable_to_non_nullable
              as int,
      imageSizeLimit: null == imageSizeLimit
          ? _value.imageSizeLimit
          : imageSizeLimit // ignore: cast_nullable_to_non_nullable
              as int,
      videoSizeLimit: null == videoSizeLimit
          ? _value.videoSizeLimit
          : videoSizeLimit // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FileConfigRespImpl implements _FileConfigResp {
  const _$FileConfigRespImpl(
      {this.fileSizeLimit = AppConfigDefaults.fileSizeLimit,
      this.imageSizeLimit = AppConfigDefaults.imageSizeLimit,
      this.videoSizeLimit = AppConfigDefaults.videoSizeLimit});

  factory _$FileConfigRespImpl.fromJson(Map<String, dynamic> json) =>
      _$$FileConfigRespImplFromJson(json);

  @override
  @JsonKey()
  final int fileSizeLimit;
  @override
  @JsonKey()
  final int imageSizeLimit;
  @override
  @JsonKey()
  final int videoSizeLimit;

  @override
  String toString() {
    return 'FileConfigResp(fileSizeLimit: $fileSizeLimit, imageSizeLimit: $imageSizeLimit, videoSizeLimit: $videoSizeLimit)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FileConfigRespImpl &&
            (identical(other.fileSizeLimit, fileSizeLimit) ||
                other.fileSizeLimit == fileSizeLimit) &&
            (identical(other.imageSizeLimit, imageSizeLimit) ||
                other.imageSizeLimit == imageSizeLimit) &&
            (identical(other.videoSizeLimit, videoSizeLimit) ||
                other.videoSizeLimit == videoSizeLimit));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, fileSizeLimit, imageSizeLimit, videoSizeLimit);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FileConfigRespImplCopyWith<_$FileConfigRespImpl> get copyWith =>
      __$$FileConfigRespImplCopyWithImpl<_$FileConfigRespImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FileConfigRespImplToJson(
      this,
    );
  }
}

abstract class _FileConfigResp implements FileConfigResp {
  const factory _FileConfigResp(
      {final int fileSizeLimit,
      final int imageSizeLimit,
      final int videoSizeLimit}) = _$FileConfigRespImpl;

  factory _FileConfigResp.fromJson(Map<String, dynamic> json) =
      _$FileConfigRespImpl.fromJson;

  @override
  int get fileSizeLimit;
  @override
  int get imageSizeLimit;
  @override
  int get videoSizeLimit;
  @override
  @JsonKey(ignore: true)
  _$$FileConfigRespImplCopyWith<_$FileConfigRespImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
