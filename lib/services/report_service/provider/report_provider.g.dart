// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'report_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$reportHash() => r'fee10f4fa69bc8596a9a4902c07527e39b6b76e5';

/// See also [Report].
@ProviderFor(Report)
final reportProvider = AsyncNotifierProvider<Report, List<String>>.internal(
  Report.new,
  name: r'reportProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$reportHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Report = AsyncNotifier<List<String>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
