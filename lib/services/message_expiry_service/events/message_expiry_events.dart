/// Events for message expiry service communication via EventBus
abstract class MessageExpiryEvent {}

/// Event fired when expired messages are deleted from database
class ExpiredMessagesDeletedEvent extends MessageExpiryEvent {
  final String conversationId;
  final List<String> deletedMessageIds;
  final DateTime timestamp;

  ExpiredMessagesDeletedEvent({
    required this.conversationId,
    required this.deletedMessageIds,
    required this.timestamp,
  });

  @override
  String toString() {
    return 'ExpiredMessagesDeletedEvent{conversationId: $conversationId, deletedMessageIds: $deletedMessageIds, timestamp: $timestamp}';
  }
}

/// Event to trigger immediate check for expired messages
class TriggerExpiryCheckEvent extends MessageExpiryEvent {
  final DateTime timestamp;

  TriggerExpiryCheckEvent({
    required this.timestamp,
  });

  @override
  String toString() {
    return 'TriggerExpiryCheckEvent{timestamp: $timestamp}';
  }
}

/// Event to notify that a new ephemeral message was added
class EphemeralMessageAddedEvent extends MessageExpiryEvent {
  final String conversationId;
  final String messageId;
  final DateTime expiryTime;

  EphemeralMessageAddedEvent({
    required this.conversationId,
    required this.messageId,
    required this.expiryTime,
  });

  @override
  String toString() {
    return 'EphemeralMessageAddedEvent{conversationId: $conversationId, messageId: $messageId, expiryTime: $expiryTime}';
  }
}

/// Event to notify that an ephemeral message was marked as read
class EphemeralMessageReadEvent extends MessageExpiryEvent {
  final String conversationId;
  final String messageId;
  final DateTime readTime;
  final int timeoutSeconds;

  EphemeralMessageReadEvent({
    required this.conversationId,
    required this.messageId,
    required this.readTime,
    required this.timeoutSeconds,
  });

  DateTime get expiryTime => readTime.add(Duration(seconds: timeoutSeconds));

  @override
  String toString() {
    return 'EphemeralMessageReadEvent{conversationId: $conversationId, messageId: $messageId, readTime: $readTime, timeoutSeconds: $timeoutSeconds}';
  }
}
