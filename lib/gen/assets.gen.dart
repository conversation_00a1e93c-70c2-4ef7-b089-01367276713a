/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter/services.dart';

class $AssetsAudioGen {
  const $AssetsAudioGen();

  /// File path: assets/audio/incoming_call.mp3
  String get incomingCall => 'assets/audio/incoming_call.mp3';

  /// File path: assets/audio/outgoing_call.mp3
  String get outgoingCall => 'assets/audio/outgoing_call.mp3';

  /// List of all assets
  List<String> get values => [incomingCall, outgoingCall];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/actions_close_room.webp
  AssetGenImage get actionsCloseRoom =>
      const AssetGenImage('assets/images/actions_close_room.webp');

  /// File path: assets/images/actions_host_guide.webp
  AssetGenImage get actionsHostGuide =>
      const AssetGenImage('assets/images/actions_host_guide.webp');

  /// File path: assets/images/actions_report_issue.webp
  AssetGenImage get actionsReportIssue =>
      const AssetGenImage('assets/images/actions_report_issue.webp');

  /// File path: assets/images/actions_report_user.webp
  AssetGenImage get actionsReportUser =>
      const AssetGenImage('assets/images/actions_report_user.webp');

  /// File path: assets/images/actions_room_info.webp
  AssetGenImage get actionsRoomInfo =>
      const AssetGenImage('assets/images/actions_room_info.webp');

  /// File path: assets/images/actions_share_room.webp
  AssetGenImage get actionsShareRoom =>
      const AssetGenImage('assets/images/actions_share_room.webp');

  /// File path: assets/images/call_mic.webp
  AssetGenImage get callMic =>
      const AssetGenImage('assets/images/call_mic.webp');

  /// File path: assets/images/default_avatar.webp
  AssetGenImage get defaultAvatar =>
      const AssetGenImage('assets/images/default_avatar.webp');

  /// File path: assets/images/delete_account_emoji.webp
  AssetGenImage get deleteAccountEmoji =>
      const AssetGenImage('assets/images/delete_account_emoji.webp');

  /// File path: assets/images/ghost_coin.png
  AssetGenImage get ghostCoin =>
      const AssetGenImage('assets/images/ghost_coin.png');

  /// File path: assets/images/ghost_coin_1888.webp
  AssetGenImage get ghostCoin1888 =>
      const AssetGenImage('assets/images/ghost_coin_1888.webp');

  /// File path: assets/images/ghost_coin_18888.webp
  AssetGenImage get ghostCoin18888 =>
      const AssetGenImage('assets/images/ghost_coin_18888.webp');

  /// File path: assets/images/ghost_coin_388.webp
  AssetGenImage get ghostCoin388 =>
      const AssetGenImage('assets/images/ghost_coin_388.webp');

  /// File path: assets/images/ghost_coin_4888.webp
  AssetGenImage get ghostCoin4888 =>
      const AssetGenImage('assets/images/ghost_coin_4888.webp');

  /// File path: assets/images/ghost_coin_88.webp
  AssetGenImage get ghostCoin88 =>
      const AssetGenImage('assets/images/ghost_coin_88.webp');

  /// File path: assets/images/ghost_coin_888.webp
  AssetGenImage get ghostCoin888 =>
      const AssetGenImage('assets/images/ghost_coin_888.webp');

  /// File path: assets/images/gift.webp
  AssetGenImage get gift => const AssetGenImage('assets/images/gift.webp');

  /// File path: assets/images/gift_store.webp
  AssetGenImage get giftStore =>
      const AssetGenImage('assets/images/gift_store.webp');

  /// File path: assets/images/instant_voice_call.webp
  AssetGenImage get instantVoiceCall =>
      const AssetGenImage('assets/images/instant_voice_call.webp');

  /// File path: assets/images/instant_voice_match_error.webp
  AssetGenImage get instantVoiceMatchError =>
      const AssetGenImage('assets/images/instant_voice_match_error.webp');

  /// File path: assets/images/instant_voice_matching.webp
  AssetGenImage get instantVoiceMatching =>
      const AssetGenImage('assets/images/instant_voice_matching.webp');

  /// File path: assets/images/login_logo.webp
  AssetGenImage get loginLogo =>
      const AssetGenImage('assets/images/login_logo.webp');

  /// File path: assets/images/pro_ crown.webp
  AssetGenImage get proCrown =>
      const AssetGenImage('assets/images/pro_ crown.webp');

  /// File path: assets/images/quick_join_room.webp
  AssetGenImage get quickJoinRoom =>
      const AssetGenImage('assets/images/quick_join_room.webp');

  /// File path: assets/images/star_coin.png
  AssetGenImage get starCoin =>
      const AssetGenImage('assets/images/star_coin.png');

  /// File path: assets/images/top_1.png
  AssetGenImage get top1 => const AssetGenImage('assets/images/top_1.png');

  /// File path: assets/images/top_2.png
  AssetGenImage get top2 => const AssetGenImage('assets/images/top_2.png');

  /// File path: assets/images/top_3.png
  AssetGenImage get top3 => const AssetGenImage('assets/images/top_3.png');

  /// List of all assets
  List<AssetGenImage> get values => [
        actionsCloseRoom,
        actionsHostGuide,
        actionsReportIssue,
        actionsReportUser,
        actionsRoomInfo,
        actionsShareRoom,
        callMic,
        defaultAvatar,
        deleteAccountEmoji,
        ghostCoin,
        ghostCoin1888,
        ghostCoin18888,
        ghostCoin388,
        ghostCoin4888,
        ghostCoin88,
        ghostCoin888,
        gift,
        giftStore,
        instantVoiceCall,
        instantVoiceMatchError,
        instantVoiceMatching,
        loginLogo,
        proCrown,
        quickJoinRoom,
        starCoin,
        top1,
        top2,
        top3
      ];
}

class $AssetsSvgsGen {
  const $AssetsSvgsGen();

  /// File path: assets/svgs/room_minimize.svg
  SvgGenImage get roomMinimize =>
      const SvgGenImage('assets/svgs/room_minimize.svg');

  /// File path: assets/svgs/search_room.svg
  SvgGenImage get searchRoom =>
      const SvgGenImage('assets/svgs/search_room.svg');

  /// File path: assets/svgs/share_room.svg
  SvgGenImage get shareRoom => const SvgGenImage('assets/svgs/share_room.svg');

  /// File path: assets/svgs/tab_following.svg
  SvgGenImage get tabFollowing =>
      const SvgGenImage('assets/svgs/tab_following.svg');

  /// File path: assets/svgs/tab_home.svg
  SvgGenImage get tabHome => const SvgGenImage('assets/svgs/tab_home.svg');

  /// File path: assets/svgs/tab_leaderboard.svg
  SvgGenImage get tabLeaderboard =>
      const SvgGenImage('assets/svgs/tab_leaderboard.svg');

  /// File path: assets/svgs/tab_me.svg
  SvgGenImage get tabMe => const SvgGenImage('assets/svgs/tab_me.svg');

  /// File path: assets/svgs/tab_shop.svg
  SvgGenImage get tabShop => const SvgGenImage('assets/svgs/tab_shop.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
        roomMinimize,
        searchRoom,
        shareRoom,
        tabFollowing,
        tabHome,
        tabLeaderboard,
        tabMe,
        tabShop
      ];
}

class Assets {
  Assets._();

  static const $AssetsAudioGen audio = $AssetsAudioGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsSvgsGen svgs = $AssetsSvgsGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName);

  final String _assetName;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = false,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.low,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(this._assetName);

  final String _assetName;

  SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    SvgTheme theme = const SvgTheme(),
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    return SvgPicture.asset(
      _assetName,
      key: key,
      matchTextDirection: matchTextDirection,
      bundle: bundle,
      package: package,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      theme: theme,
      colorFilter: colorFilter,
      color: color,
      colorBlendMode: colorBlendMode,
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
