// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'paginated_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PaginatedResponseImpl<T> _$$PaginatedResponseImplFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) =>
    _$PaginatedResponseImpl<T>(
      total: (json['total'] as num?)?.toInt() ?? 0,
      size: (json['size'] as num?)?.toInt() ?? 10,
      current: (json['current'] as num?)?.toInt() ?? 1,
      orders: json['orders'] as List<dynamic>? ?? const [],
      pages: (json['pages'] as num?)?.toInt() ?? 0,
      optimizeCountSql: json['optimizeCountSql'] as bool?,
      searchCount: json['searchCount'] as bool?,
      maxLimit: (json['maxLimit'] as num?)?.toInt(),
      countId: (json['countId'] as num?)?.toInt(),
      records: (json['records'] as List<dynamic>?)?.map(fromJsonT).toList() ??
          const [],
    );

Map<String, dynamic> _$$PaginatedResponseImplToJson<T>(
  _$PaginatedResponseImpl<T> instance,
  Object? Function(T value) toJsonT,
) =>
    <String, dynamic>{
      'total': instance.total,
      'size': instance.size,
      'current': instance.current,
      'orders': instance.orders,
      'pages': instance.pages,
      'optimizeCountSql': instance.optimizeCountSql,
      'searchCount': instance.searchCount,
      'maxLimit': instance.maxLimit,
      'countId': instance.countId,
      'records': instance.records.map(toJsonT).toList(),
    };
